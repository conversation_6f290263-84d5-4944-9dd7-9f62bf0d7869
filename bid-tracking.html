<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - Bid Tracking</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .tracking-card { transition: all 0.3s ease; }
        .tracking-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .progress-step { transition: all 0.3s ease; }
        .pulse-dot { animation: pulse 2s infinite; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="/bids-dashboard.html" class="text-purple-600 hover:text-purple-800 mr-4">← Back to Bids</a>
                    <h1 class="text-3xl font-bold text-purple-600">📍 Bid Tracking</h1>
                    <span class="ml-3 text-sm text-gray-500">Real-time Bid Status Monitoring</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-green-600 pulse-dot">🔴 Live Updates</span>
                    <button onclick="refreshAll()" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">🔄 Refresh All</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Active Bids Overview -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Bids</p>
                        <p class="text-2xl font-semibold text-gray-900">12</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 rounded-lg">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending Review</p>
                        <p class="text-2xl font-semibold text-gray-900">8</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Submitted</p>
                        <p class="text-2xl font-semibold text-gray-900">4</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-red-100 rounded-lg">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Urgent</p>
                        <p class="text-2xl font-semibold text-gray-900">2</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live Bid Tracking -->
        <div class="space-y-6">
            <!-- Bid 1: Road Maintenance -->
            <div class="tracking-card bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Road Maintenance Services</h3>
                            <p class="text-sm text-gray-600">TND-2024-001 • Submitted 2 days ago</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Under Review</span>
                            <div class="text-right">
                                <div class="text-lg font-bold text-green-600">85%</div>
                                <div class="text-xs text-gray-500">Success Probability</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <!-- Progress Timeline -->
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-medium">✓</div>
                            <div class="text-xs text-gray-600 mt-1">Created</div>
                        </div>
                        <div class="flex-1 h-1 bg-green-500 mx-2"></div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-medium">✓</div>
                            <div class="text-xs text-gray-600 mt-1">Submitted</div>
                        </div>
                        <div class="flex-1 h-1 bg-yellow-500 mx-2"></div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center text-white text-sm font-medium pulse-dot">⏳</div>
                            <div class="text-xs text-gray-600 mt-1">Review</div>
                        </div>
                        <div class="flex-1 h-1 bg-gray-300 mx-2"></div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-gray-500 text-sm font-medium">4</div>
                            <div class="text-xs text-gray-600 mt-1">Decision</div>
                        </div>
                        <div class="flex-1 h-1 bg-gray-300 mx-2"></div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-gray-500 text-sm font-medium">5</div>
                            <div class="text-xs text-gray-600 mt-1">Award</div>
                        </div>
                    </div>
                    
                    <!-- Details Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 class="font-medium text-blue-900 mb-2">📅 Timeline</h4>
                            <p class="text-sm text-blue-700">Deadline: Jan 25, 2024</p>
                            <p class="text-sm text-blue-700">Days remaining: 3</p>
                        </div>
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h4 class="font-medium text-green-900 mb-2">💰 Value</h4>
                            <p class="text-sm text-green-700">Bid Amount: R 450,000</p>
                            <p class="text-sm text-green-700">Estimated Margin: 18%</p>
                        </div>
                        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                            <h4 class="font-medium text-purple-900 mb-2">🎯 Competition</h4>
                            <p class="text-sm text-purple-700">Competitors: 5</p>
                            <p class="text-sm text-purple-700">Ranking: #2</p>
                        </div>
                    </div>
                    
                    <div class="mt-4 flex justify-between items-center">
                        <div class="text-sm text-gray-600">
                            <span class="font-medium">Last Update:</span> 2 hours ago - Document verification completed
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="viewDetails('TND-2024-001')" class="text-sm bg-purple-600 text-white px-3 py-1 rounded hover:bg-purple-700">View Details</button>
                            <button onclick="sendUpdate('TND-2024-001')" class="text-sm bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600">Request Update</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bid 2: IT Infrastructure -->
            <div class="tracking-card bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">IT Infrastructure Upgrade</h3>
                            <p class="text-sm text-gray-600">TND-2024-002 • Draft stage</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">In Progress</span>
                            <div class="text-right">
                                <div class="text-lg font-bold text-yellow-600">72%</div>
                                <div class="text-xs text-gray-500">Success Probability</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <!-- Progress Timeline -->
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium pulse-dot">📝</div>
                            <div class="text-xs text-gray-600 mt-1">Drafting</div>
                        </div>
                        <div class="flex-1 h-1 bg-gray-300 mx-2"></div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-gray-500 text-sm font-medium">2</div>
                            <div class="text-xs text-gray-600 mt-1">Review</div>
                        </div>
                        <div class="flex-1 h-1 bg-gray-300 mx-2"></div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-gray-500 text-sm font-medium">3</div>
                            <div class="text-xs text-gray-600 mt-1">Submit</div>
                        </div>
                        <div class="flex-1 h-1 bg-gray-300 mx-2"></div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-gray-500 text-sm font-medium">4</div>
                            <div class="text-xs text-gray-600 mt-1">Decision</div>
                        </div>
                        <div class="flex-1 h-1 bg-gray-300 mx-2"></div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-gray-500 text-sm font-medium">5</div>
                            <div class="text-xs text-gray-600 mt-1">Award</div>
                        </div>
                    </div>
                    
                    <!-- Details Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <h4 class="font-medium text-red-900 mb-2">⚠️ Urgent</h4>
                            <p class="text-sm text-red-700">Deadline: Jan 20, 2024</p>
                            <p class="text-sm text-red-700">Days remaining: 1</p>
                        </div>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 class="font-medium text-blue-900 mb-2">💰 Value</h4>
                            <p class="text-sm text-blue-700">Estimated: R 1,200,000</p>
                            <p class="text-sm text-blue-700">Completion: 65%</p>
                        </div>
                        <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                            <h4 class="font-medium text-orange-900 mb-2">📋 Tasks</h4>
                            <p class="text-sm text-orange-700">Completed: 8/12</p>
                            <p class="text-sm text-orange-700">Remaining: 4</p>
                        </div>
                    </div>
                    
                    <div class="mt-4 flex justify-between items-center">
                        <div class="text-sm text-gray-600">
                            <span class="font-medium">Last Update:</span> 30 minutes ago - Technical specifications added
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="continueBid('TND-2024-002')" class="text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">Continue Editing</button>
                            <button onclick="viewDetails('TND-2024-002')" class="text-sm bg-purple-600 text-white px-3 py-1 rounded hover:bg-purple-700">View Details</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bid 3: Security Services -->
            <div class="tracking-card bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Security Services Contract</h3>
                            <p class="text-sm text-gray-600">TND-2024-003 • Awaiting decision</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Decision Pending</span>
                            <div class="text-right">
                                <div class="text-lg font-bold text-blue-600">68%</div>
                                <div class="text-xs text-gray-500">Success Probability</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <!-- Progress Timeline -->
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-medium">✓</div>
                            <div class="text-xs text-gray-600 mt-1">Created</div>
                        </div>
                        <div class="flex-1 h-1 bg-green-500 mx-2"></div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-medium">✓</div>
                            <div class="text-xs text-gray-600 mt-1">Submitted</div>
                        </div>
                        <div class="flex-1 h-1 bg-green-500 mx-2"></div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-medium">✓</div>
                            <div class="text-xs text-gray-600 mt-1">Reviewed</div>
                        </div>
                        <div class="flex-1 h-1 bg-blue-500 mx-2"></div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium pulse-dot">🤔</div>
                            <div class="text-xs text-gray-600 mt-1">Decision</div>
                        </div>
                        <div class="flex-1 h-1 bg-gray-300 mx-2"></div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-gray-500 text-sm font-medium">5</div>
                            <div class="text-xs text-gray-600 mt-1">Award</div>
                        </div>
                    </div>
                    
                    <!-- Details Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h4 class="font-medium text-green-900 mb-2">📅 Timeline</h4>
                            <p class="text-sm text-green-700">Decision: Jan 30, 2024</p>
                            <p class="text-sm text-green-700">Days remaining: 8</p>
                        </div>
                        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                            <h4 class="font-medium text-purple-900 mb-2">💰 Value</h4>
                            <p class="text-sm text-purple-700">Bid Amount: R 650,000</p>
                            <p class="text-sm text-purple-700">Contract: 2 years</p>
                        </div>
                        <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                            <h4 class="font-medium text-indigo-900 mb-2">📊 Status</h4>
                            <p class="text-sm text-indigo-700">Shortlisted: Yes</p>
                            <p class="text-sm text-indigo-700">Ranking: #1</p>
                        </div>
                    </div>
                    
                    <div class="mt-4 flex justify-between items-center">
                        <div class="text-sm text-gray-600">
                            <span class="font-medium">Last Update:</span> 1 day ago - Moved to final evaluation stage
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="viewDetails('TND-2024-003')" class="text-sm bg-purple-600 text-white px-3 py-1 rounded hover:bg-purple-700">View Details</button>
                            <button onclick="sendUpdate('TND-2024-003')" class="text-sm bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600">Request Update</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function refreshAll() {
            alert('🔄 Refreshing all bid statuses...\n\nChecking for updates from:\n• Tender portals\n• Email notifications\n• Direct communications\n• System integrations\n\nAll statuses updated!');
        }

        function viewDetails(bidId) {
            alert(`📋 Opening detailed tracking view for ${bidId}...\n\nShowing:\n• Complete timeline\n• Document status\n• Communication log\n• Evaluation progress\n• Next steps`);
        }

        function sendUpdate(bidId) {
            alert(`📧 Sending update request for ${bidId}...\n\nRequest sent to:\n• Tender office\n• Project manager\n• Evaluation committee\n\nYou will be notified when updates are available.`);
        }

        function continueBid(bidId) {
            window.location.href = `/create-bid.html?edit=${bidId}`;
        }

        // Simulate real-time updates
        setInterval(() => {
            console.log(`[${new Date().toLocaleTimeString()}] Bid Tracking: Monitoring 12 active bids for status changes`);
        }, 30000);

        // Simulate status updates
        setTimeout(() => {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg z-50';
            notification.innerHTML = '🔔 New update: TND-2024-001 moved to final evaluation stage';
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }, 10000);
    </script>
</body>
</html>
