// BidBeez Dashboard Backend Integrations
// Comprehensive integration layer for all dashboards

class DashboardIntegrations {
    constructor() {
        this.api = window.BidBeezAPI;
        this.config = window.PLATFORM_CONFIG;
        this.cache = new Map();
        this.subscriptions = [];
    }

    // Main Dashboard Integration
    async initializeMainDashboard() {
        try {
            const [stats, recentTenders, activeBids, notifications] = await Promise.all([
                this.getDashboardStats(),
                this.getRecentTenders(),
                this.getActiveBids(),
                this.getRecentNotifications()
            ]);

            this.updateMainDashboardDisplay(stats, recentTenders, activeBids, notifications);
            this.setupMainDashboardRealtime();
        } catch (error) {
            console.error('Error initializing main dashboard:', error);
        }
    }

    async getDashboardStats() {
        try {
            const [
                totalTenders,
                activeBids,
                totalBees,
                activeTasks,
                complianceStatus
            ] = await Promise.all([
                this.api.supabaseQuery('tenders', { select: 'count' }),
                this.api.supabaseQuery('bids', { select: 'count', filter: { status: 'active' } }),
                this.api.supabaseQuery('bee_profiles', { select: 'count', filter: { is_active: true } }),
                this.api.supabaseQuery('bee_tasks', { select: 'count', filter: { status: 'assigned' } }),
                this.getComplianceStatus()
            ]);

            return {
                totalTenders: totalTenders?.length || 0,
                activeBids: activeBids?.length || 0,
                totalBees: totalBees?.length || 0,
                activeTasks: activeTasks?.length || 0,
                complianceLevel: complianceStatus.level || 'Level 4',
                complianceScore: complianceStatus.score || 78.5
            };
        } catch (error) {
            console.error('Error getting dashboard stats:', error);
            return this.getMockDashboardStats();
        }
    }

    async getRecentTenders() {
        try {
            return await this.api.supabaseQuery('tenders', {
                select: 'id, title, issuer_name, tender_value, closing_date, category_code',
                order: { column: 'created_at', ascending: false },
                limit: 10
            });
        } catch (error) {
            console.error('Error getting recent tenders:', error);
            return [];
        }
    }

    async getActiveBids() {
        try {
            return await this.api.supabaseQuery('bids', {
                select: 'id, tender_id, bid_amount, status, win_probability, tenders(title, issuer_name)',
                filter: { status: 'submitted' },
                order: { column: 'created_at', ascending: false },
                limit: 10
            });
        } catch (error) {
            console.error('Error getting active bids:', error);
            return [];
        }
    }

    async getRecentNotifications() {
        // Mock notifications - in production this would come from a notifications table
        return [
            {
                id: 1,
                title: 'New Tender Alert',
                message: 'Road Maintenance Services tender published',
                type: 'tender',
                created_at: new Date().toISOString(),
                read: false
            },
            {
                id: 2,
                title: 'Bid Deadline Reminder',
                message: 'IT Services bid deadline in 2 hours',
                type: 'deadline',
                created_at: new Date(Date.now() - 3600000).toISOString(),
                read: false
            }
        ];
    }

    async getComplianceStatus() {
        try {
            const validations = await this.api.supabaseQuery('compliance_validations', {
                select: '*',
                order: { column: 'validation_date', ascending: false },
                limit: 1
            });

            if (validations && validations.length > 0) {
                return {
                    level: validations[0].bee_level || 'Level 4',
                    score: validations[0].score || 78.5,
                    lastUpdated: validations[0].validation_date
                };
            }
        } catch (error) {
            console.error('Error getting compliance status:', error);
        }

        return { level: 'Level 4', score: 78.5 };
    }

    updateMainDashboardDisplay(stats, tenders, bids, notifications) {
        // Update dashboard statistics
        this.updateElement('.total-tenders', stats.totalTenders);
        this.updateElement('.active-bids', stats.activeBids);
        this.updateElement('.total-bees', stats.totalBees);
        this.updateElement('.active-tasks', stats.activeTasks);
        this.updateElement('.compliance-level', stats.complianceLevel);
        this.updateElement('.compliance-score', stats.complianceScore);

        // Update recent tenders list
        this.updateTendersList(tenders);
        
        // Update active bids list
        this.updateBidsList(bids);
        
        // Update notifications
        this.updateNotificationsList(notifications);

        console.log('Main dashboard updated with backend data');
    }

    // SkillSync Dashboard Integration
    async initializeSkillSyncDashboard() {
        try {
            const [skillProviders, skills, trainingPrograms] = await Promise.all([
                this.getSkillProviders(),
                this.getSkills(),
                this.getTrainingPrograms()
            ]);

            this.updateSkillSyncDisplay(skillProviders, skills, trainingPrograms);
        } catch (error) {
            console.error('Error initializing SkillSync dashboard:', error);
        }
    }

    async getSkillProviders() {
        try {
            return await this.api.supabaseQuery('skill_provider_profiles', {
                select: '*, skill_provider_skills(*), skill_provider_ratings(*)',
                order: { column: 'rating', ascending: false },
                limit: 20
            });
        } catch (error) {
            console.error('Error getting skill providers:', error);
            return [];
        }
    }

    async getSkills() {
        try {
            return await this.api.supabaseQuery('skills', {
                select: '*',
                order: { column: 'demand_score', ascending: false }
            });
        } catch (error) {
            console.error('Error getting skills:', error);
            return [];
        }
    }

    async getTrainingPrograms() {
        // Mock training programs - would come from training table in production
        return [
            {
                id: 1,
                name: 'CISSP Certification',
                provider: 'CyberSec Institute',
                duration: '6 weeks',
                cost: 'R15,000',
                rating: 4.8,
                available_slots: 2
            },
            {
                id: 2,
                name: 'Project Management Professional',
                provider: 'PM Academy',
                duration: '8 weeks',
                cost: 'R12,000',
                rating: 4.9,
                available_slots: 5
            }
        ];
    }

    // ToolSync Dashboard Integration
    async initializeToolSyncDashboard() {
        try {
            const toolInventory = await this.getToolInventory();
            const maintenanceSchedule = await this.getMaintenanceSchedule();
            const toolSharing = await this.getToolSharingOpportunities();

            this.updateToolSyncDisplay(toolInventory, maintenanceSchedule, toolSharing);
        } catch (error) {
            console.error('Error initializing ToolSync dashboard:', error);
        }
    }

    async getToolInventory() {
        // Mock tool inventory - would come from tools table in production
        return [
            {
                id: 1,
                name: 'Caterpillar 320 Excavator',
                category: 'Construction',
                status: 'available',
                location: 'Warehouse A - Bay 3',
                value: 850000,
                last_service: '2024-01-15'
            },
            {
                id: 2,
                name: 'Cisco Network Analyzer',
                category: 'IT Equipment',
                status: 'in_use',
                location: 'Site: Municipal Building',
                value: 85000,
                last_service: '2024-01-10'
            }
        ];
    }

    async getMaintenanceSchedule() {
        // Mock maintenance schedule
        return [
            {
                tool_id: 1,
                tool_name: 'Concrete Testing Kit',
                due_date: '2024-02-15',
                type: 'routine',
                priority: 'medium'
            }
        ];
    }

    async getToolSharingOpportunities() {
        // Mock tool sharing opportunities
        return [
            {
                id: 1,
                tool_name: 'Heavy Excavator',
                owner: 'BuildCorp Ltd',
                daily_rate: 8500,
                location: 'Johannesburg',
                rating: 4.9
            }
        ];
    }

    // BEE Compliance Dashboard Integration
    async initializeBEEComplianceDashboard() {
        try {
            const complianceData = await this.getBEEComplianceData();
            this.updateBEEComplianceDisplay(complianceData);
        } catch (error) {
            console.error('Error initializing BEE compliance dashboard:', error);
        }
    }

    async getBEEComplianceData() {
        try {
            const validations = await this.api.supabaseQuery('compliance_validations', {
                select: '*',
                order: { column: 'validation_date', ascending: false },
                limit: 1
            });

            if (validations && validations.length > 0) {
                return validations[0];
            }
        } catch (error) {
            console.error('Error getting BEE compliance data:', error);
        }

        // Mock BEE compliance data
        return {
            bee_level: 'Level 4',
            score: 78.5,
            ownership_score: 18.5,
            management_score: 12.8,
            skills_development_score: 16.2,
            enterprise_development_score: 22.5,
            socio_economic_score: 4.5,
            procurement_recognition: 100,
            certificate_expiry: '2024-12-31'
        };
    }

    // Real-time Updates Setup
    setupMainDashboardRealtime() {
        // Subscribe to tender updates
        const tenderSub = this.api.supabase
            .channel('tenders')
            .on('postgres_changes', 
                { event: '*', schema: 'public', table: 'tenders' },
                (payload) => this.handleTenderUpdate(payload)
            )
            .subscribe();

        // Subscribe to bid updates
        const bidSub = this.api.supabase
            .channel('bids')
            .on('postgres_changes', 
                { event: '*', schema: 'public', table: 'bids' },
                (payload) => this.handleBidUpdate(payload)
            )
            .subscribe();

        this.subscriptions.push(tenderSub, bidSub);
    }

    handleTenderUpdate(payload) {
        console.log('Tender update received:', payload);
        // Update tender display in real-time
        this.refreshTendersList();
    }

    handleBidUpdate(payload) {
        console.log('Bid update received:', payload);
        // Update bid display in real-time
        this.refreshBidsList();
    }

    // Utility Methods
    updateElement(selector, value) {
        const element = document.querySelector(selector);
        if (element) {
            element.textContent = value;
        }
    }

    updateTendersList(tenders) {
        // Update tenders list in UI
        console.log('Updating tenders list:', tenders.length);
    }

    updateBidsList(bids) {
        // Update bids list in UI
        console.log('Updating bids list:', bids.length);
    }

    updateNotificationsList(notifications) {
        // Update notifications list in UI
        console.log('Updating notifications list:', notifications.length);
    }

    updateSkillSyncDisplay(providers, skills, programs) {
        console.log('Updating SkillSync display:', { providers: providers.length, skills: skills.length, programs: programs.length });
    }

    updateToolSyncDisplay(inventory, maintenance, sharing) {
        console.log('Updating ToolSync display:', { inventory: inventory.length, maintenance: maintenance.length, sharing: sharing.length });
    }

    updateBEEComplianceDisplay(data) {
        console.log('Updating BEE compliance display:', data);
    }

    async refreshTendersList() {
        const tenders = await this.getRecentTenders();
        this.updateTendersList(tenders);
    }

    async refreshBidsList() {
        const bids = await this.getActiveBids();
        this.updateBidsList(bids);
    }

    getMockDashboardStats() {
        return {
            totalTenders: 156,
            activeBids: 23,
            totalBees: 24,
            activeTasks: 47,
            complianceLevel: 'Level 4',
            complianceScore: 78.5
        };
    }

    // Cleanup
    cleanup() {
        this.subscriptions.forEach(sub => {
            if (sub && typeof sub.unsubscribe === 'function') {
                sub.unsubscribe();
            }
        });
        this.subscriptions = [];
    }
}

// Initialize global dashboard integrations
window.DashboardIntegrations = new DashboardIntegrations();

// Auto-initialize based on current page
document.addEventListener('DOMContentLoaded', function() {
    const currentPage = window.location.pathname;
    
    if (currentPage.includes('dashboard.html')) {
        window.DashboardIntegrations.initializeMainDashboard();
    } else if (currentPage.includes('skillsync-dashboard.html')) {
        window.DashboardIntegrations.initializeSkillSyncDashboard();
    } else if (currentPage.includes('toolsync-dashboard.html')) {
        window.DashboardIntegrations.initializeToolSyncDashboard();
    } else if (currentPage.includes('bee-compliance-dashboard.html')) {
        window.DashboardIntegrations.initializeBEEComplianceDashboard();
    }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DashboardIntegrations;
}
