// BidBeez Platform API Service
// Comprehensive backend integration service

class BidBeezAPIService {
    constructor() {
        this.config = window.PLATFORM_CONFIG;
        this.baseUrl = this.config.api.baseUrl;
        this.supabaseUrl = this.config.supabase.url;
        this.supabaseKey = this.config.supabase.anonKey;
        this.cache = new Map();
        this.initializeSupabase();
    }

    // Initialize Supabase client
    initializeSupabase() {
        if (typeof window.supabase !== 'undefined') {
            this.supabase = window.supabase.createClient(this.supabaseUrl, this.supabaseKey);
        }
    }

    // Generic API request method
    async apiRequest(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        };

        try {
            const response = await fetch(url, { ...defaultOptions, ...options });
            if (!response.ok) {
                throw new Error(`API request failed: ${response.status} ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('API Request Error:', error);
            throw error;
        }
    }

    // Supabase query method
    async supabaseQuery(table, query = {}) {
        if (!this.supabase) {
            throw new Error('Supabase not initialized');
        }

        try {
            let queryBuilder = this.supabase.from(table);
            
            if (query.select) queryBuilder = queryBuilder.select(query.select);
            if (query.filter) {
                Object.entries(query.filter).forEach(([key, value]) => {
                    queryBuilder = queryBuilder.eq(key, value);
                });
            }
            if (query.order) queryBuilder = queryBuilder.order(query.order.column, { ascending: query.order.ascending });
            if (query.limit) queryBuilder = queryBuilder.limit(query.limit);
            if (query.range) queryBuilder = queryBuilder.range(query.range.from, query.range.to);

            const { data, error } = await queryBuilder;
            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Supabase Query Error:', error);
            throw error;
        }
    }

    // Cache management
    getCached(key) {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.config.performance.caching.duration) {
            return cached.data;
        }
        return null;
    }

    setCached(key, data) {
        this.cache.set(key, { data, timestamp: Date.now() });
    }

    // QueenBee System APIs
    async getBeeProfiles(filters = {}) {
        const cacheKey = `bee_profiles_${JSON.stringify(filters)}`;
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        try {
            const data = await this.supabaseQuery('bee_profiles', {
                select: '*',
                filter: filters,
                order: { column: 'rating', ascending: false }
            });
            this.setCached(cacheKey, data);
            return data;
        } catch (error) {
            console.error('Error fetching bee profiles:', error);
            return [];
        }
    }

    async getBeeTasks(filters = {}) {
        const cacheKey = `bee_tasks_${JSON.stringify(filters)}`;
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        try {
            const data = await this.supabaseQuery('bee_tasks', {
                select: '*',
                filter: filters,
                order: { column: 'created_at', ascending: false }
            });
            this.setCached(cacheKey, data);
            return data;
        } catch (error) {
            console.error('Error fetching bee tasks:', error);
            return [];
        }
    }

    async getBeeLocations() {
        try {
            return await this.supabaseQuery('bee_locations', {
                select: '*',
                order: { column: 'updated_at', ascending: false }
            });
        } catch (error) {
            console.error('Error fetching bee locations:', error);
            return [];
        }
    }

    async updateBeeLocation(beeId, location) {
        try {
            const { data, error } = await this.supabase
                .from('bee_locations')
                .upsert({
                    bee_id: beeId,
                    latitude: location.lat,
                    longitude: location.lng,
                    updated_at: new Date().toISOString()
                });
            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Error updating bee location:', error);
            throw error;
        }
    }

    async assignTask(taskId, beeId) {
        try {
            const { data, error } = await this.supabase
                .from('bee_tasks')
                .update({ 
                    assigned_bee_id: beeId, 
                    status: 'assigned',
                    assigned_at: new Date().toISOString()
                })
                .eq('id', taskId);
            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Error assigning task:', error);
            throw error;
        }
    }

    // Tender and Bid APIs
    async getTenders(filters = {}) {
        const cacheKey = `tenders_${JSON.stringify(filters)}`;
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        try {
            const data = await this.supabaseQuery('tenders', {
                select: '*',
                filter: filters,
                order: { column: 'closing_date', ascending: true },
                limit: 50
            });
            this.setCached(cacheKey, data);
            return data;
        } catch (error) {
            console.error('Error fetching tenders:', error);
            return [];
        }
    }

    async getBids(filters = {}) {
        try {
            return await this.supabaseQuery('bids', {
                select: '*, tenders(*)',
                filter: filters,
                order: { column: 'created_at', ascending: false }
            });
        } catch (error) {
            console.error('Error fetching bids:', error);
            return [];
        }
    }

    async createBid(bidData) {
        try {
            const { data, error } = await this.supabase
                .from('bids')
                .insert(bidData);
            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Error creating bid:', error);
            throw error;
        }
    }

    // Skills Management APIs
    async getSkills(filters = {}) {
        try {
            return await this.supabaseQuery('skills', {
                select: '*',
                filter: filters,
                order: { column: 'name', ascending: true }
            });
        } catch (error) {
            console.error('Error fetching skills:', error);
            return [];
        }
    }

    async getSkillProviders(filters = {}) {
        try {
            return await this.supabaseQuery('skill_provider_profiles', {
                select: '*, skill_provider_skills(*), skill_provider_ratings(*)',
                filter: filters,
                order: { column: 'rating', ascending: false }
            });
        } catch (error) {
            console.error('Error fetching skill providers:', error);
            return [];
        }
    }

    // Contractor Management APIs
    async getContractors(filters = {}) {
        try {
            return await this.supabaseQuery('contractor_profiles', {
                select: '*',
                filter: filters,
                order: { column: 'rating', ascending: false }
            });
        } catch (error) {
            console.error('Error fetching contractors:', error);
            return [];
        }
    }

    async getContractorQuoteRequests(filters = {}) {
        try {
            return await this.supabaseQuery('contractor_quote_requests', {
                select: '*, contractor_profiles(*)',
                filter: filters,
                order: { column: 'created_at', ascending: false }
            });
        } catch (error) {
            console.error('Error fetching quote requests:', error);
            return [];
        }
    }

    // Compliance APIs
    async getComplianceValidations(filters = {}) {
        try {
            return await this.supabaseQuery('compliance_validations', {
                select: '*',
                filter: filters,
                order: { column: 'validation_date', ascending: false }
            });
        } catch (error) {
            console.error('Error fetching compliance validations:', error);
            return [];
        }
    }

    // Real-time subscriptions
    subscribeToBeeLocations(callback) {
        if (!this.supabase) return null;
        
        return this.supabase
            .channel('bee_locations')
            .on('postgres_changes', 
                { event: '*', schema: 'public', table: 'bee_locations' },
                callback
            )
            .subscribe();
    }

    subscribeToBeeTasks(callback) {
        if (!this.supabase) return null;
        
        return this.supabase
            .channel('bee_tasks')
            .on('postgres_changes', 
                { event: '*', schema: 'public', table: 'bee_tasks' },
                callback
            )
            .subscribe();
    }

    subscribeToBeeHeartbeats(callback) {
        if (!this.supabase) return null;
        
        return this.supabase
            .channel('bee_heartbeats')
            .on('postgres_changes', 
                { event: '*', schema: 'public', table: 'bee_heartbeats' },
                callback
            )
            .subscribe();
    }

    // Analytics APIs
    async getDashboardStats() {
        try {
            const [beeProfiles, beeTasks, tenders, bids] = await Promise.all([
                this.supabaseQuery('bee_profiles', { select: 'count' }),
                this.supabaseQuery('bee_tasks', { select: 'count' }),
                this.supabaseQuery('tenders', { select: 'count' }),
                this.supabaseQuery('bids', { select: 'count' })
            ]);

            return {
                totalBees: beeProfiles?.length || 0,
                activeTasks: beeTasks?.filter(t => t.status === 'assigned')?.length || 0,
                totalTenders: tenders?.length || 0,
                totalBids: bids?.length || 0
            };
        } catch (error) {
            console.error('Error fetching dashboard stats:', error);
            return {
                totalBees: 0,
                activeTasks: 0,
                totalTenders: 0,
                totalBids: 0
            };
        }
    }

    // Utility methods
    formatCurrency(amount, currency = 'ZAR') {
        return new Intl.NumberFormat('en-ZA', {
            style: 'currency',
            currency: currency
        }).format(amount);
    }

    formatDate(date) {
        return new Intl.DateTimeFormat('en-ZA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }).format(new Date(date));
    }

    calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371; // Radius of the Earth in km
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLon = (lon2 - lon1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLon/2) * Math.sin(dLon/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c; // Distance in km
    }
}

// Initialize global API service
window.BidBeezAPI = new BidBeezAPIService();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BidBeezAPIService;
}
