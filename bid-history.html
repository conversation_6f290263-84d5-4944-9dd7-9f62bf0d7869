<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - Bid History</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .bid-row { transition: all 0.3s ease; }
        .bid-row:hover { background-color: #f8fafc; }
        .status-badge { font-size: 0.75rem; padding: 0.25rem 0.5rem; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="/bids-dashboard.html" class="text-blue-600 hover:text-blue-800 mr-4">← Back to Bids</a>
                    <h1 class="text-3xl font-bold text-blue-600">📚 Bid History</h1>
                    <span class="ml-3 text-sm text-gray-500">Historical Bid Data & Analytics</span>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="exportHistory()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">📊 Export Data</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Filters -->
    <div class="bg-white border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex flex-wrap items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">Status:</label>
                    <select id="status-filter" class="border border-gray-300 rounded px-3 py-1 text-sm" onchange="filterBids()">
                        <option value="all">All Status</option>
                        <option value="won">Won</option>
                        <option value="lost">Lost</option>
                        <option value="pending">Pending</option>
                        <option value="submitted">Submitted</option>
                    </select>
                </div>
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">Category:</label>
                    <select id="category-filter" class="border border-gray-300 rounded px-3 py-1 text-sm" onchange="filterBids()">
                        <option value="all">All Categories</option>
                        <option value="construction">Construction</option>
                        <option value="it">IT Services</option>
                        <option value="consulting">Consulting</option>
                        <option value="maintenance">Maintenance</option>
                    </select>
                </div>
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">Date Range:</label>
                    <select id="date-filter" class="border border-gray-300 rounded px-3 py-1 text-sm" onchange="filterBids()">
                        <option value="all">All Time</option>
                        <option value="30">Last 30 days</option>
                        <option value="90">Last 3 months</option>
                        <option value="365">Last year</option>
                    </select>
                </div>
                <button onclick="resetFilters()" class="text-sm text-gray-600 hover:text-gray-800">Reset Filters</button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Summary Stats -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <div class="text-2xl font-bold text-blue-600">47</div>
                <div class="text-sm text-gray-600">Total Bids</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <div class="text-2xl font-bold text-green-600">32</div>
                <div class="text-sm text-gray-600">Won</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <div class="text-2xl font-bold text-red-600">12</div>
                <div class="text-sm text-gray-600">Lost</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <div class="text-2xl font-bold text-yellow-600">3</div>
                <div class="text-sm text-gray-600">Pending</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <div class="text-2xl font-bold text-purple-600">68%</div>
                <div class="text-sm text-gray-600">Success Rate</div>
            </div>
        </div>

        <!-- Bid History Table -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">📋 Bid History</h2>
                <p class="text-sm text-gray-600">Complete record of all your bids with detailed analytics</p>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tender</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Success %</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="bid-table-body">
                        <!-- Bid Row 1 -->
                        <tr class="bid-row" data-status="won" data-category="construction" data-date="2024-01-15">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Road Maintenance Services</div>
                                    <div class="text-sm text-gray-500">TND-2024-001</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Construction</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">R 450,000</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge inline-flex items-center rounded-full bg-green-100 text-green-800 font-medium">Won</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Jan 15, 2024</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">85%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="viewBidDetails('TND-2024-001')" class="text-blue-600 hover:text-blue-900 mr-3">View</button>
                                <button onclick="analyzeBid('TND-2024-001')" class="text-purple-600 hover:text-purple-900">Analyze</button>
                            </td>
                        </tr>

                        <!-- Bid Row 2 -->
                        <tr class="bid-row" data-status="pending" data-category="it" data-date="2024-01-10">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">IT Infrastructure Upgrade</div>
                                    <div class="text-sm text-gray-500">TND-2024-002</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">IT Services</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">R 1,200,000</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge inline-flex items-center rounded-full bg-yellow-100 text-yellow-800 font-medium">Pending</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Jan 10, 2024</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-yellow-600">72%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="viewBidDetails('TND-2024-002')" class="text-blue-600 hover:text-blue-900 mr-3">View</button>
                                <button onclick="analyzeBid('TND-2024-002')" class="text-purple-600 hover:text-purple-900">Analyze</button>
                            </td>
                        </tr>

                        <!-- Bid Row 3 -->
                        <tr class="bid-row" data-status="lost" data-category="consulting" data-date="2024-01-05">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Management Consulting</div>
                                    <div class="text-sm text-gray-500">TND-2024-003</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Consulting</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">R 800,000</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge inline-flex items-center rounded-full bg-red-100 text-red-800 font-medium">Lost</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Jan 5, 2024</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">45%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="viewBidDetails('TND-2024-003')" class="text-blue-600 hover:text-blue-900 mr-3">View</button>
                                <button onclick="analyzeBid('TND-2024-003')" class="text-purple-600 hover:text-purple-900">Analyze</button>
                            </td>
                        </tr>

                        <!-- Bid Row 4 -->
                        <tr class="bid-row" data-status="won" data-category="maintenance" data-date="2023-12-20">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Building Maintenance</div>
                                    <div class="text-sm text-gray-500">TND-2023-045</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Maintenance</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">R 320,000</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge inline-flex items-center rounded-full bg-green-100 text-green-800 font-medium">Won</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Dec 20, 2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">78%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="viewBidDetails('TND-2023-045')" class="text-blue-600 hover:text-blue-900 mr-3">View</button>
                                <button onclick="analyzeBid('TND-2023-045')" class="text-purple-600 hover:text-purple-900">Analyze</button>
                            </td>
                        </tr>

                        <!-- Bid Row 5 -->
                        <tr class="bid-row" data-status="submitted" data-category="construction" data-date="2023-12-15">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Security Services</div>
                                    <div class="text-sm text-gray-500">TND-2023-044</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Security</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">R 650,000</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge inline-flex items-center rounded-full bg-blue-100 text-blue-800 font-medium">Submitted</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Dec 15, 2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">68%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="viewBidDetails('TND-2023-044')" class="text-blue-600 hover:text-blue-900 mr-3">View</button>
                                <button onclick="analyzeBid('TND-2023-044')" class="text-purple-600 hover:text-purple-900">Analyze</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        <div class="mt-6 flex items-center justify-between">
            <div class="text-sm text-gray-700">
                Showing <span class="font-medium">1</span> to <span class="font-medium">5</span> of <span class="font-medium">47</span> results
            </div>
            <div class="flex space-x-2">
                <button class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">Previous</button>
                <button class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-blue-50 text-blue-600">1</button>
                <button class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">2</button>
                <button class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">3</button>
                <button class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">Next</button>
            </div>
        </div>
    </main>

    <script>
        function filterBids() {
            const statusFilter = document.getElementById('status-filter').value;
            const categoryFilter = document.getElementById('category-filter').value;
            const dateFilter = document.getElementById('date-filter').value;
            const rows = document.querySelectorAll('.bid-row');

            rows.forEach(row => {
                let show = true;

                if (statusFilter !== 'all' && row.dataset.status !== statusFilter) {
                    show = false;
                }

                if (categoryFilter !== 'all' && row.dataset.category !== categoryFilter) {
                    show = false;
                }

                row.style.display = show ? '' : 'none';
            });
        }

        function resetFilters() {
            document.getElementById('status-filter').value = 'all';
            document.getElementById('category-filter').value = 'all';
            document.getElementById('date-filter').value = 'all';
            filterBids();
        }

        function viewBidDetails(bidId) {
            window.location.href = `/bid-tracking.html?bid=${bidId}`;
        }

        function analyzeBid(bidId) {
            alert(`📊 Opening AI analysis for ${bidId}...\n\nAnalysis includes:\n• Success factors\n• Areas for improvement\n• Competitive positioning\n• Psychological insights\n• Recommendations for future bids`);
        }

        function exportHistory() {
            alert('📊 Exporting bid history data...\n\nGenerating comprehensive report with:\n• All bid records\n• Performance analytics\n• Success rate trends\n• Category breakdowns\n\nDownload will start shortly.');
        }
    </script>
</body>
</html>
