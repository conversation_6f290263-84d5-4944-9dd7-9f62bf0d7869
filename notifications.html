<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - Notifications Center</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .notification-card { transition: all 0.3s ease; }
        .notification-card:hover { transform: translateY(-1px); box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
        .pulse-dot { animation: pulse 2s infinite; }
        .unread { border-left: 4px solid #3B82F6; }
        .urgent { border-left: 4px solid #EF4444; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="/dashboard.html" class="text-blue-600 hover:text-blue-800 mr-4">← Back to Dashboard</a>
                    <h1 class="text-3xl font-bold text-blue-600">🔔 Notifications Center</h1>
                    <span class="ml-3 text-sm text-gray-500">All Alerts & Updates</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-red-600 pulse-dot">🔴 3 Urgent</span>
                    <button onclick="markAllRead()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">✅ Mark All Read</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Summary Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-red-100 rounded-lg">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Urgent</p>
                        <p class="text-2xl font-semibold text-red-600">3</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Unread</p>
                        <p class="text-2xl font-semibold text-blue-600">12</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Today</p>
                        <p class="text-2xl font-semibold text-green-600">8</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">AI Alerts</p>
                        <p class="text-2xl font-semibold text-purple-600">5</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow mb-8 p-6">
            <div class="flex flex-wrap items-center space-x-4">
                <button onclick="filterNotifications('all')" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700" id="filter-all">All</button>
                <button onclick="filterNotifications('urgent')" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300" id="filter-urgent">Urgent</button>
                <button onclick="filterNotifications('bids')" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300" id="filter-bids">Bids</button>
                <button onclick="filterNotifications('skills')" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300" id="filter-skills">Skills</button>
                <button onclick="filterNotifications('system')" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300" id="filter-system">System</button>
                <button onclick="filterNotifications('ai')" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300" id="filter-ai">AI Insights</button>
            </div>
        </div>

        <!-- Notifications List -->
        <div class="space-y-4" id="notifications-list">
            <!-- Urgent Notification -->
            <div class="notification-card bg-white rounded-lg shadow urgent unread" data-category="urgent bids">
                <div class="p-6">
                    <div class="flex items-start justify-between">
                        <div class="flex items-start">
                            <div class="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center text-white text-sm font-bold">⚠️</div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-gray-900">Urgent: Bid Deadline Tomorrow</h3>
                                <p class="text-gray-600 mb-2">Road Maintenance Services (TND-2024-001) deadline is tomorrow at 2:00 PM</p>
                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                    <span>📝 Bids</span>
                                    <span>⏰ 2 minutes ago</span>
                                    <span class="bg-red-100 text-red-800 px-2 py-1 rounded">URGENT</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="viewBid('TND-2024-001')" class="text-blue-600 hover:text-blue-800 text-sm">View Bid</button>
                            <button onclick="markRead(this)" class="text-gray-400 hover:text-gray-600">✕</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Insight Notification -->
            <div class="notification-card bg-white rounded-lg shadow unread" data-category="ai">
                <div class="p-6">
                    <div class="flex items-start justify-between">
                        <div class="flex items-start">
                            <div class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold">🧠</div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-gray-900">AI Optimization Opportunity</h3>
                                <p class="text-gray-600 mb-2">Your bid success rate could increase by 15% by adjusting pricing strategy for IT tenders</p>
                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                    <span>🧠 AI Insights</span>
                                    <span>⏰ 15 minutes ago</span>
                                    <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded">OPTIMIZATION</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="viewOptimization()" class="text-purple-600 hover:text-purple-800 text-sm">View Details</button>
                            <button onclick="markRead(this)" class="text-gray-400 hover:text-gray-600">✕</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Skills Notification -->
            <div class="notification-card bg-white rounded-lg shadow unread" data-category="skills">
                <div class="p-6">
                    <div class="flex items-start justify-between">
                        <div class="flex items-start">
                            <div class="w-10 h-10 bg-indigo-500 rounded-full flex items-center justify-center text-white text-sm font-bold">🎓</div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-gray-900">New Training Opportunity</h3>
                                <p class="text-gray-600 mb-2">CISSP Certification course starting next week - only 2 slots remaining</p>
                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                    <span>🎓 SkillSync</span>
                                    <span>⏰ 1 hour ago</span>
                                    <span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded">TRAINING</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="enrollTraining()" class="text-indigo-600 hover:text-indigo-800 text-sm">Enroll Now</button>
                            <button onclick="markRead(this)" class="text-gray-400 hover:text-gray-600">✕</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bid Success Notification -->
            <div class="notification-card bg-white rounded-lg shadow" data-category="bids">
                <div class="p-6">
                    <div class="flex items-start justify-between">
                        <div class="flex items-start">
                            <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold">🏆</div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-gray-900">Bid Won: Security Services</h3>
                                <p class="text-gray-600 mb-2">Congratulations! You won the Security Services contract worth R650,000</p>
                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                    <span>📝 Bids</span>
                                    <span>⏰ 3 hours ago</span>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded">SUCCESS</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="viewContract()" class="text-green-600 hover:text-green-800 text-sm">View Contract</button>
                            <button onclick="markRead(this)" class="text-gray-400 hover:text-gray-600">✕</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Notification -->
            <div class="notification-card bg-white rounded-lg shadow" data-category="system">
                <div class="p-6">
                    <div class="flex items-start justify-between">
                        <div class="flex items-start">
                            <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">⚙️</div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-gray-900">System Update Complete</h3>
                                <p class="text-gray-600 mb-2">BidBeez platform updated with new AI optimization features and improved performance</p>
                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                    <span>⚙️ System</span>
                                    <span>⏰ 6 hours ago</span>
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">UPDATE</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="viewChangelog()" class="text-blue-600 hover:text-blue-800 text-sm">View Changes</button>
                            <button onclick="markRead(this)" class="text-gray-400 hover:text-gray-600">✕</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- WhatsApp Notification -->
            <div class="notification-card bg-white rounded-lg shadow" data-category="whatsapp">
                <div class="p-6">
                    <div class="flex items-start justify-between">
                        <div class="flex items-start">
                            <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold">💬</div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-gray-900">WhatsApp Auto-Bid Successful</h3>
                                <p class="text-gray-600 mb-2">Auto-bid submitted via WhatsApp for "Building Maintenance" tender</p>
                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                    <span>💬 WhatsApp</span>
                                    <span>⏰ 8 hours ago</span>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded">AUTO-BID</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="viewAutoBid()" class="text-green-600 hover:text-green-800 text-sm">View Details</button>
                            <button onclick="markRead(this)" class="text-gray-400 hover:text-gray-600">✕</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Load More -->
        <div class="text-center mt-8">
            <button onclick="loadMore()" class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700">Load More Notifications</button>
        </div>
    </main>

    <script>
        function filterNotifications(category) {
            const notifications = document.querySelectorAll('.notification-card');
            const buttons = document.querySelectorAll('[id^="filter-"]');
            
            // Reset button styles
            buttons.forEach(btn => {
                btn.className = 'px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300';
            });
            
            // Highlight active button
            document.getElementById(`filter-${category}`).className = 'px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700';
            
            // Filter notifications
            notifications.forEach(notification => {
                if (category === 'all' || notification.dataset.category.includes(category)) {
                    notification.style.display = 'block';
                } else {
                    notification.style.display = 'none';
                }
            });
        }

        function markRead(element) {
            const notification = element.closest('.notification-card');
            notification.classList.remove('unread');
            notification.style.opacity = '0.7';
        }

        function markAllRead() {
            const unreadNotifications = document.querySelectorAll('.unread');
            unreadNotifications.forEach(notification => {
                notification.classList.remove('unread');
                notification.style.opacity = '0.7';
            });
            alert('✅ All notifications marked as read!');
        }

        function viewBid(bidId) {
            window.open(`/bid-tracking.html?bid=${bidId}`, '_blank');
        }

        function viewOptimization() {
            window.open('/bid-optimization.html', '_blank');
        }

        function enrollTraining() {
            alert('🎓 Redirecting to SkillSync for CISSP enrollment...');
            window.open('/skillsync-dashboard.html', '_blank');
        }

        function viewContract() {
            alert('📄 Opening contract details...');
        }

        function viewChangelog() {
            alert('📋 System Changelog:\n\n✅ Enhanced AI optimization algorithms\n✅ Improved psychological profiling accuracy\n✅ Faster bid processing\n✅ New WhatsApp integration features\n✅ Better mobile responsiveness');
        }

        function viewAutoBid() {
            window.open('/whatsapp-dashboard.html', '_blank');
        }

        function loadMore() {
            alert('📥 Loading more notifications...\n\nShowing older notifications from the past week.');
        }

        // Simulate real-time notifications
        setInterval(() => {
            console.log(`[${new Date().toLocaleTimeString()}] Notifications: Monitoring for new alerts and updates`);
        }, 30000);

        // Simulate new notification
        setTimeout(() => {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-blue-500 text-white p-4 rounded-lg shadow-lg z-50';
            notification.innerHTML = '🔔 New notification: Tender alert for Environmental Services - R680K';
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }, 10000);
    </script>
</body>
</html>
