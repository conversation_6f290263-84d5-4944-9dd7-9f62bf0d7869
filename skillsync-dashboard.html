<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - SkillSync Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .skill-card { transition: all 0.3s ease; }
        .skill-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .progress-ring { transform: rotate(-90deg); }
        .pulse-animation { animation: pulse 2s infinite; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="/dashboard.html" class="text-indigo-600 hover:text-indigo-800 mr-4">← Back to Dashboard</a>
                    <h1 class="text-3xl font-bold text-indigo-600">🎓 SkillSync Dashboard</h1>
                    <span class="ml-3 text-sm text-gray-500">Professional Development & Compliance</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-red-600 pulse-animation">⚠️ 3 Critical Gaps</span>
                    <a href="/toolsync-dashboard.html" class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700">🔧 ToolSync</a>
                    <button onclick="syncSkills()" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">🚀 Sync Skills</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Alert Banner -->
    <div class="bg-red-50 border-l-4 border-red-400 p-4">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-red-700">
                        <strong>Professional Identity Crisis Alert:</strong> You're missing critical skills for 67% of available tenders. Complete bidders win 73% more contracts!
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-red-100 rounded-lg">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Critical Gaps</p>
                        <p class="text-2xl font-semibold text-red-600">3</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 rounded-lg">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Expiring Soon</p>
                        <p class="text-2xl font-semibold text-yellow-600">2</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Verified Skills</p>
                        <p class="text-2xl font-semibold text-green-600">12</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Success Rate</p>
                        <p class="text-2xl font-semibold text-blue-600">67%</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Critical Skills Gaps -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">🚨 Critical Skills Gaps</h2>
                <p class="text-sm text-gray-600">Missing skills that are blocking you from high-value tenders</p>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <!-- Gap 1 -->
                    <div class="skill-card border border-red-200 rounded-lg p-4 bg-red-50">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center text-white text-xl">🏗️</div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-semibold text-red-900">Construction Project Management</h3>
                                    <p class="text-sm text-red-700">Required for 23 available tenders worth R4.2M</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-red-600">CRITICAL</div>
                                <div class="text-xs text-red-500">Blocking R4.2M</div>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div class="bg-white border border-red-200 rounded-lg p-3">
                                <h4 class="font-medium text-red-900 mb-1">📚 Training Available</h4>
                                <p class="text-sm text-red-700">SACPCMP Certification - 3 months</p>
                                <p class="text-xs text-red-600">Only 2 slots left this quarter!</p>
                            </div>
                            <div class="bg-white border border-red-200 rounded-lg p-3">
                                <h4 class="font-medium text-red-900 mb-1">💰 Investment</h4>
                                <p class="text-sm text-red-700">R45,000 total cost</p>
                                <p class="text-xs text-red-600">ROI: 9,333% (R4.2M potential)</p>
                            </div>
                            <div class="bg-white border border-red-200 rounded-lg p-3">
                                <h4 class="font-medium text-red-900 mb-1">⏰ Urgency</h4>
                                <p class="text-sm text-red-700">Next intake: Feb 15</p>
                                <p class="text-xs text-red-600">Registration closes in 5 days!</p>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-red-600">
                                <span class="font-medium">Impact:</span> Without this skill, you're automatically disqualified from 67% of construction tenders
                            </div>
                            <button onclick="enrollSkill('construction-pm')" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">🚀 Enroll Now</button>
                        </div>
                    </div>

                    <!-- Gap 2 -->
                    <div class="skill-card border border-orange-200 rounded-lg p-4 bg-orange-50">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center text-white text-xl">💻</div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-semibold text-orange-900">Advanced IT Security</h3>
                                    <p class="text-sm text-orange-700">Required for 15 IT tenders worth R2.8M</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-orange-600">HIGH</div>
                                <div class="text-xs text-orange-500">Blocking R2.8M</div>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div class="bg-white border border-orange-200 rounded-lg p-3">
                                <h4 class="font-medium text-orange-900 mb-1">📚 Training Available</h4>
                                <p class="text-sm text-orange-700">CISSP Certification - 6 months</p>
                                <p class="text-xs text-orange-600">4 slots available</p>
                            </div>
                            <div class="bg-white border border-orange-200 rounded-lg p-3">
                                <h4 class="font-medium text-orange-900 mb-1">💰 Investment</h4>
                                <p class="text-sm text-orange-700">R32,000 total cost</p>
                                <p class="text-xs text-orange-600">ROI: 8,750% (R2.8M potential)</p>
                            </div>
                            <div class="bg-white border border-orange-200 rounded-lg p-3">
                                <h4 class="font-medium text-orange-900 mb-1">⏰ Urgency</h4>
                                <p class="text-sm text-orange-700">Next intake: Mar 1</p>
                                <p class="text-xs text-orange-600">Early bird discount ends soon!</p>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-orange-600">
                                <span class="font-medium">Impact:</span> IT security is mandatory for all government IT contracts
                            </div>
                            <button onclick="enrollSkill('it-security')" class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700">📚 Learn More</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Skills & Certifications -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Verified Skills -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900">✅ Verified Skills</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm">✓</div>
                                <div class="ml-3">
                                    <div class="font-medium text-gray-900">Project Management (PMP)</div>
                                    <div class="text-sm text-gray-600">Expires: Dec 2025</div>
                                </div>
                            </div>
                            <div class="text-sm text-green-600 font-medium">Active</div>
                        </div>
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm">✓</div>
                                <div class="ml-3">
                                    <div class="font-medium text-gray-900">BBBEE Compliance</div>
                                    <div class="text-sm text-gray-600">Expires: Jun 2024</div>
                                </div>
                            </div>
                            <div class="text-sm text-yellow-600 font-medium">Expiring Soon</div>
                        </div>
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm">✓</div>
                                <div class="ml-3">
                                    <div class="font-medium text-gray-900">Quality Management (ISO 9001)</div>
                                    <div class="text-sm text-gray-600">Expires: Mar 2025</div>
                                </div>
                            </div>
                            <div class="text-sm text-green-600 font-medium">Active</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Skill Marketplace -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900">🛒 Skill Marketplace</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h3 class="font-medium text-blue-900 mb-2">🔥 Hot Skills This Month</h3>
                            <div class="space-y-2">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-blue-700">Environmental Management</span>
                                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">+45% demand</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-blue-700">Digital Transformation</span>
                                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">+38% demand</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-blue-700">Supply Chain Management</span>
                                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">+32% demand</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                            <h3 class="font-medium text-purple-900 mb-2">⚡ Quick Wins</h3>
                            <p class="text-sm text-purple-700 mb-3">Skills you can acquire in under 30 days</p>
                            <button onclick="viewQuickWins()" class="w-full bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700">View Quick Wins</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function syncSkills() {
            alert('🚀 SkillSync Activated!\n\nAnalyzing:\n• Current skill portfolio\n• Market demand trends\n• Tender requirements\n• Training opportunities\n\nRecommendations updated!');
        }

        function enrollSkill(skillId) {
            const skills = {
                'construction-pm': 'Construction Project Management (SACPCMP)',
                'it-security': 'Advanced IT Security (CISSP)'
            };
            
            alert(`📚 Enrolling in ${skills[skillId]}...\n\n✅ Application submitted\n✅ Payment processing\n✅ Training provider contacted\n\nYou'll receive confirmation within 24 hours.\n\n🎯 This skill will unlock R${skillId === 'construction-pm' ? '4.2M' : '2.8M'} in tender opportunities!`);
        }

        function viewQuickWins() {
            alert('⚡ Quick Win Skills (Under 30 days):\n\n• Health & Safety Level 1 (5 days)\n• Basic Environmental Awareness (3 days)\n• Microsoft Project Certification (14 days)\n• First Aid Certification (2 days)\n• Fire Safety Training (1 day)\n\nTotal investment: R8,500\nPotential unlock: R850K in tenders');
        }

        // Simulate real-time skill monitoring
        setInterval(() => {
            console.log(`[${new Date().toLocaleTimeString()}] SkillSync: Monitoring skill gaps and market opportunities`);
        }, 30000);
    </script>
</body>
</html>
