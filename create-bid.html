<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - Create New Bid</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .form-section { transition: all 0.3s ease; }
        .form-section:hover { box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
        .progress-bar { transition: width 0.3s ease; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="/bids-dashboard.html" class="text-green-600 hover:text-green-800 mr-4">← Back to Bids</a>
                    <h1 class="text-3xl font-bold text-green-600">➕ Create New Bid</h1>
                    <span class="ml-3 text-sm text-gray-500">AI-Powered Bid Creation</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-blue-600">🧠 Psychological Analysis: Active</span>
                    <button onclick="saveDraft()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">Save Draft</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Progress Bar -->
    <div class="bg-white border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">Bid Creation Progress</span>
                    <span class="text-sm text-gray-500" id="progress-text">Step 1 of 5</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="progress-bar bg-green-600 h-2 rounded-full" style="width: 20%" id="progress-bar"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Form -->
            <div class="lg:col-span-2">
                <form id="bid-form" class="space-y-8">
                    <!-- Step 1: Basic Information -->
                    <div class="form-section bg-white rounded-lg shadow p-6" id="step-1">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">📋 Basic Information</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Tender Title</label>
                                <input type="text" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="Enter tender title" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Tender Reference</label>
                                <input type="text" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="e.g., TND-2024-001" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                                <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500" required>
                                    <option value="">Select category</option>
                                    <option value="construction">Construction</option>
                                    <option value="it">IT Services</option>
                                    <option value="consulting">Consulting</option>
                                    <option value="maintenance">Maintenance</option>
                                    <option value="security">Security</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Estimated Value</label>
                                <input type="number" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="0.00" min="0" step="0.01" required>
                            </div>
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                <textarea class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500" rows="4" placeholder="Describe the tender requirements..." required></textarea>
                            </div>
                        </div>
                        <div class="mt-6 flex justify-end">
                            <button type="button" onclick="nextStep()" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700">Next Step</button>
                        </div>
                    </div>

                    <!-- Step 2: Requirements (Hidden initially) -->
                    <div class="form-section bg-white rounded-lg shadow p-6 hidden" id="step-2">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">📝 Requirements & Specifications</h2>
                        <div class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Technical Requirements</label>
                                <textarea class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500" rows="4" placeholder="List technical requirements..."></textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Compliance Requirements</label>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                                        <span class="ml-2 text-sm text-gray-700">BBBEE Compliance Required</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                                        <span class="ml-2 text-sm text-gray-700">ISO Certification Required</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                                        <span class="ml-2 text-sm text-gray-700">Tax Clearance Certificate</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="mt-6 flex justify-between">
                            <button type="button" onclick="prevStep()" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600">Previous</button>
                            <button type="button" onclick="nextStep()" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700">Next Step</button>
                        </div>
                    </div>

                    <!-- Step 3: Timeline (Hidden initially) -->
                    <div class="form-section bg-white rounded-lg shadow p-6 hidden" id="step-3">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">📅 Timeline & Deadlines</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Submission Deadline</label>
                                <input type="datetime-local" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Project Start Date</label>
                                <input type="date" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Project Duration</label>
                                <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                    <option value="">Select duration</option>
                                    <option value="1-3">1-3 months</option>
                                    <option value="3-6">3-6 months</option>
                                    <option value="6-12">6-12 months</option>
                                    <option value="12+">12+ months</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Priority Level</label>
                                <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                    <option value="low">Low</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="high">High</option>
                                    <option value="urgent">Urgent</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-6 flex justify-between">
                            <button type="button" onclick="prevStep()" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600">Previous</button>
                            <button type="button" onclick="nextStep()" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700">Next Step</button>
                        </div>
                    </div>

                    <!-- Step 4: Documents (Hidden initially) -->
                    <div class="form-section bg-white rounded-lg shadow p-6 hidden" id="step-4">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">📎 Documents & Attachments</h2>
                        <div class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Tender Documents</label>
                                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="mt-4">
                                        <label class="cursor-pointer">
                                            <span class="mt-2 block text-sm font-medium text-gray-900">Upload tender documents</span>
                                            <input type="file" class="sr-only" multiple accept=".pdf,.doc,.docx">
                                        </label>
                                        <p class="mt-1 text-xs text-gray-500">PDF, DOC, DOCX up to 10MB each</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-6 flex justify-between">
                            <button type="button" onclick="prevStep()" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600">Previous</button>
                            <button type="button" onclick="nextStep()" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700">Next Step</button>
                        </div>
                    </div>

                    <!-- Step 5: Review & Submit (Hidden initially) -->
                    <div class="form-section bg-white rounded-lg shadow p-6 hidden" id="step-5">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">✅ Review & Submit</h2>
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                            <h3 class="text-lg font-medium text-green-900 mb-2">🧠 AI Analysis Complete</h3>
                            <p class="text-sm text-green-700">Psychological profiling suggests this bid has a <strong>78% success probability</strong> based on your bidding patterns and market analysis.</p>
                        </div>
                        <div class="space-y-4">
                            <div class="flex justify-between py-2 border-b">
                                <span class="font-medium">Tender Title:</span>
                                <span id="review-title">-</span>
                            </div>
                            <div class="flex justify-between py-2 border-b">
                                <span class="font-medium">Category:</span>
                                <span id="review-category">-</span>
                            </div>
                            <div class="flex justify-between py-2 border-b">
                                <span class="font-medium">Estimated Value:</span>
                                <span id="review-value">-</span>
                            </div>
                            <div class="flex justify-between py-2 border-b">
                                <span class="font-medium">Submission Deadline:</span>
                                <span id="review-deadline">-</span>
                            </div>
                        </div>
                        <div class="mt-6 flex justify-between">
                            <button type="button" onclick="prevStep()" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600">Previous</button>
                            <button type="submit" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700">Create Bid</button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- AI Assistant -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">🤖 AI Assistant</h3>
                    <div class="space-y-3">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                            <p class="text-sm text-blue-800">💡 Based on your profile, consider highlighting your experience in similar projects.</p>
                        </div>
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                            <p class="text-sm text-yellow-800">⚠️ This tender has high competition. Focus on unique value propositions.</p>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">⚡ Quick Actions</h3>
                    <div class="space-y-2">
                        <button onclick="loadTemplate()" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded">📋 Load Template</button>
                        <button onclick="saveDraft()" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded">💾 Save Draft</button>
                        <button onclick="previewBid()" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded">👁️ Preview Bid</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let currentStep = 1;
        const totalSteps = 5;

        function nextStep() {
            if (currentStep < totalSteps) {
                document.getElementById(`step-${currentStep}`).classList.add('hidden');
                currentStep++;
                document.getElementById(`step-${currentStep}`).classList.remove('hidden');
                updateProgress();
            }
        }

        function prevStep() {
            if (currentStep > 1) {
                document.getElementById(`step-${currentStep}`).classList.add('hidden');
                currentStep--;
                document.getElementById(`step-${currentStep}`).classList.remove('hidden');
                updateProgress();
            }
        }

        function updateProgress() {
            const progress = (currentStep / totalSteps) * 100;
            document.getElementById('progress-bar').style.width = `${progress}%`;
            document.getElementById('progress-text').textContent = `Step ${currentStep} of ${totalSteps}`;
        }

        function saveDraft() {
            alert('💾 Draft saved successfully!');
        }

        function loadTemplate() {
            window.location.href = '/bid-templates.html';
        }

        function previewBid() {
            alert('👁️ Opening bid preview...');
        }

        // Form submission
        document.getElementById('bid-form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('🎉 Bid created successfully! Redirecting to bid tracking...');
            setTimeout(() => {
                window.location.href = '/bid-tracking.html';
            }, 2000);
        });
    </script>
</body>
</html>
