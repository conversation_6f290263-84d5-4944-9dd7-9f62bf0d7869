<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - Tool Inventory Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .tool-row { transition: all 0.3s ease; }
        .tool-row:hover { background-color: #f8fafc; }
        .status-indicator { animation: pulse 2s infinite; }
        .maintenance-due { border-left: 4px solid #F59E0B; }
        .available { border-left: 4px solid #10B981; }
        .in-use { border-left: 4px solid #3B82F6; }
        .maintenance { border-left: 4px solid #EF4444; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="/toolsync-dashboard.html" class="text-orange-600 hover:text-orange-800 mr-4">← Back to ToolSync</a>
                    <h1 class="text-3xl font-bold text-orange-600">📦 Tool Inventory</h1>
                    <span class="ml-3 text-sm text-gray-500">Equipment Tracking & Management</span>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="addNewTool()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">➕ Add Tool</button>
                    <button onclick="exportInventory()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">📊 Export</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Filters and Search -->
    <div class="bg-white border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex flex-wrap items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <input type="text" placeholder="Search tools..." class="border border-gray-300 rounded px-3 py-2 text-sm w-64" id="search-input">
                    <button onclick="searchTools()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">🔍</button>
                </div>
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">Status:</label>
                    <select id="status-filter" class="border border-gray-300 rounded px-3 py-2 text-sm" onchange="filterTools()">
                        <option value="all">All Status</option>
                        <option value="available">Available</option>
                        <option value="in-use">In Use</option>
                        <option value="maintenance">Maintenance</option>
                        <option value="maintenance-due">Maintenance Due</option>
                    </select>
                </div>
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">Category:</label>
                    <select id="category-filter" class="border border-gray-300 rounded px-3 py-2 text-sm" onchange="filterTools()">
                        <option value="all">All Categories</option>
                        <option value="construction">Construction</option>
                        <option value="it">IT Equipment</option>
                        <option value="testing">Testing & Measurement</option>
                        <option value="transportation">Transportation</option>
                        <option value="safety">Safety Equipment</option>
                    </select>
                </div>
                <button onclick="resetFilters()" class="text-sm text-gray-600 hover:text-gray-800">Reset Filters</button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <div class="text-2xl font-bold text-gray-900">247</div>
                <div class="text-sm text-gray-600">Total Tools</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <div class="text-2xl font-bold text-green-600">189</div>
                <div class="text-sm text-gray-600">Available</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <div class="text-2xl font-bold text-blue-600">45</div>
                <div class="text-sm text-gray-600">In Use</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <div class="text-2xl font-bold text-red-600">8</div>
                <div class="text-sm text-gray-600">Maintenance</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <div class="text-2xl font-bold text-yellow-600">5</div>
                <div class="text-sm text-gray-600">Maintenance Due</div>
            </div>
        </div>

        <!-- Tool Inventory Table -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">🔧 Tool Inventory</h2>
                <p class="text-sm text-gray-600">Complete equipment tracking and management</p>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tool</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Service</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="tools-table-body">
                        <!-- Tool Row 1 -->
                        <tr class="tool-row available" data-status="available" data-category="construction">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm">🏗️</div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">Caterpillar 320 Excavator</div>
                                        <div class="text-sm text-gray-500">Serial: CAT-320-2019-001</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Construction</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <span class="status-indicator w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                                    Available
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Warehouse A - Bay 3</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">R 850,000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="viewTool('CAT-320-001')" class="text-blue-600 hover:text-blue-900 mr-3">View</button>
                                <button onclick="assignTool('CAT-320-001')" class="text-green-600 hover:text-green-900 mr-3">Assign</button>
                                <button onclick="scheduleMaintenance('CAT-320-001')" class="text-orange-600 hover:text-orange-900">Service</button>
                            </td>
                        </tr>

                        <!-- Tool Row 2 -->
                        <tr class="tool-row in-use" data-status="in-use" data-category="it">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm">💻</div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">Cisco Network Analyzer</div>
                                        <div class="text-sm text-gray-500">Model: CNA-5000-Pro</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">IT Equipment</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <span class="status-indicator w-2 h-2 bg-blue-400 rounded-full mr-1"></span>
                                    In Use
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Site: Municipal Building</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">R 85,000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-10</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="viewTool('CNA-5000-001')" class="text-blue-600 hover:text-blue-900 mr-3">View</button>
                                <button onclick="trackTool('CNA-5000-001')" class="text-purple-600 hover:text-purple-900 mr-3">Track</button>
                                <button onclick="recallTool('CNA-5000-001')" class="text-red-600 hover:text-red-900">Recall</button>
                            </td>
                        </tr>

                        <!-- Tool Row 3 -->
                        <tr class="tool-row maintenance-due" data-status="maintenance-due" data-category="testing">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white text-sm">🔬</div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">Concrete Testing Kit</div>
                                        <div class="text-sm text-gray-500">Model: CTK-Professional</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Testing</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <span class="status-indicator w-2 h-2 bg-yellow-400 rounded-full mr-1"></span>
                                    Maintenance Due
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Lab - Testing Bay 2</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">R 25,000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="viewTool('CTK-001')" class="text-blue-600 hover:text-blue-900 mr-3">View</button>
                                <button onclick="scheduleMaintenance('CTK-001')" class="text-orange-600 hover:text-orange-900 mr-3">Schedule</button>
                                <button onclick="urgentMaintenance('CTK-001')" class="text-red-600 hover:text-red-900">Urgent</button>
                            </td>
                        </tr>

                        <!-- Tool Row 4 -->
                        <tr class="tool-row maintenance" data-status="maintenance" data-category="transportation">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center text-white text-sm">🚛</div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">Mercedes Actros Truck</div>
                                        <div class="text-sm text-gray-500">Reg: ABC 123 GP</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">Transportation</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <span class="status-indicator w-2 h-2 bg-red-400 rounded-full mr-1"></span>
                                    In Maintenance
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Service Center</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">R 1,200,000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="viewTool('ACTROS-001')" class="text-blue-600 hover:text-blue-900 mr-3">View</button>
                                <button onclick="checkMaintenanceStatus('ACTROS-001')" class="text-orange-600 hover:text-orange-900 mr-3">Status</button>
                                <button onclick="expediteMaintenance('ACTROS-001')" class="text-red-600 hover:text-red-900">Expedite</button>
                            </td>
                        </tr>

                        <!-- Tool Row 5 -->
                        <tr class="tool-row available" data-status="available" data-category="safety">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center text-white text-sm">🦺</div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">Safety Equipment Set</div>
                                        <div class="text-sm text-gray-500">Complete PPE Kit - 50 units</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Safety</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <span class="status-indicator w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                                    Available
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Safety Store - Rack A</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">R 15,000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-05</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="viewTool('SAFETY-001')" class="text-blue-600 hover:text-blue-900 mr-3">View</button>
                                <button onclick="assignTool('SAFETY-001')" class="text-green-600 hover:text-green-900 mr-3">Assign</button>
                                <button onclick="checkStock('SAFETY-001')" class="text-purple-600 hover:text-purple-900">Stock</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        <div class="mt-6 flex items-center justify-between">
            <div class="text-sm text-gray-700">
                Showing <span class="font-medium">1</span> to <span class="font-medium">5</span> of <span class="font-medium">247</span> tools
            </div>
            <div class="flex space-x-2">
                <button class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">Previous</button>
                <button class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-blue-50 text-blue-600">1</button>
                <button class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">2</button>
                <button class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">3</button>
                <button class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">Next</button>
            </div>
        </div>
    </main>

    <script>
        function filterTools() {
            const statusFilter = document.getElementById('status-filter').value;
            const categoryFilter = document.getElementById('category-filter').value;
            const rows = document.querySelectorAll('.tool-row');

            rows.forEach(row => {
                let show = true;

                if (statusFilter !== 'all' && row.dataset.status !== statusFilter) {
                    show = false;
                }

                if (categoryFilter !== 'all' && row.dataset.category !== categoryFilter) {
                    show = false;
                }

                row.style.display = show ? '' : 'none';
            });
        }

        function resetFilters() {
            document.getElementById('status-filter').value = 'all';
            document.getElementById('category-filter').value = 'all';
            document.getElementById('search-input').value = '';
            filterTools();
        }

        function searchTools() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            const rows = document.querySelectorAll('.tool-row');

            rows.forEach(row => {
                const toolName = row.querySelector('.text-sm.font-medium').textContent.toLowerCase();
                const serial = row.querySelector('.text-sm.text-gray-500').textContent.toLowerCase();
                
                if (toolName.includes(searchTerm) || serial.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function addNewTool() {
            alert('➕ Adding New Tool\n\nOpening tool registration form:\n• Basic information\n• Technical specifications\n• Purchase/lease details\n• Maintenance schedule\n• Insurance information\n• Location assignment');
        }

        function exportInventory() {
            alert('📊 Exporting Tool Inventory\n\nGenerating comprehensive report:\n• Complete tool list\n• Status summary\n• Maintenance schedules\n• Usage analytics\n• Financial summary\n\nDownload will start shortly.');
        }

        function viewTool(toolId) {
            alert(`🔧 Tool Details: ${toolId}\n\nShowing:\n• Complete specifications\n• Usage history\n• Maintenance records\n• Current location\n• Assignment history\n• Performance metrics`);
        }

        function assignTool(toolId) {
            alert(`📋 Assigning Tool: ${toolId}\n\nAssignment options:\n• Project assignment\n• Contractor assignment\n• Site deployment\n• Rental agreement\n\nSelect assignment type and duration.`);
        }

        function scheduleMaintenance(toolId) {
            alert(`🔧 Scheduling Maintenance: ${toolId}\n\n✅ Maintenance provider contacted\n✅ Service slot reserved\n✅ Downtime scheduled\n✅ Replacement tool arranged\n\nMaintenance scheduled for next week.`);
        }

        function trackTool(toolId) {
            alert(`📍 Tool Tracking: ${toolId}\n\nReal-time location:\n• GPS coordinates\n• Site assignment\n• Usage status\n• Operator information\n• Expected return date`);
        }

        // Simulate real-time inventory updates
        setInterval(() => {
            console.log(`[${new Date().toLocaleTimeString()}] Tool Inventory: Monitoring 247 tools across all locations`);
        }, 30000);
    </script>
</body>
</html>
