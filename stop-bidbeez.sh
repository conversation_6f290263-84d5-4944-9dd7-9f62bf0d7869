#!/bin/bash

# BidBeez Platform Stop Script
# This script stops all BidBeez services

echo "🛑 Stopping BidBeez Platform..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Stop processes by PID if files exist
if [ -f "logs/api.pid" ]; then
    API_PID=$(cat logs/api.pid)
    if kill -0 $API_PID 2>/dev/null; then
        kill $API_PID
        print_status "API Server stopped (PID: $API_PID)"
    fi
    rm -f logs/api.pid
fi

if [ -f "logs/frontend.pid" ]; then
    FRONTEND_PID=$(cat logs/frontend.pid)
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        kill $FRONTEND_PID
        print_status "Frontend Server stopped (PID: $FRONTEND_PID)"
    fi
    rm -f logs/frontend.pid
fi

# Kill any remaining processes on our ports
print_info "Cleaning up any remaining processes..."

if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    lsof -ti:8000 | xargs kill -9 2>/dev/null || true
    print_status "Cleaned up port 8000"
fi

if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    print_status "Cleaned up port 3000"
fi

if lsof -Pi :5173 -sTCP:LISTEN -t >/dev/null 2>&1; then
    lsof -ti:5173 | xargs kill -9 2>/dev/null || true
    print_status "Cleaned up port 5173"
fi

print_status "🎯 BidBeez Platform stopped successfully!"
print_info "All services have been terminated and ports are now free."
