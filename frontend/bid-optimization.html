<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - Bid Optimization</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .optimization-card { transition: all 0.3s ease; }
        .optimization-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .pulse-animation { animation: pulse 2s infinite; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="/bids-dashboard.html" class="text-pink-600 hover:text-pink-800 mr-4">← Back to Bids</a>
                    <h1 class="text-3xl font-bold text-pink-600">⚡ Bid Optimization</h1>
                    <span class="ml-3 text-sm text-gray-500">AI-Powered Optimization</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-green-600 pulse-animation">🧠 AI Analysis: Active</span>
                    <button onclick="runOptimization()" class="bg-pink-600 text-white px-4 py-2 rounded hover:bg-pink-700">🚀 Optimize All</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Optimization Overview -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-pink-100 rounded-lg">
                        <svg class="w-6 h-6 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Success Rate Improvement</p>
                        <p class="text-2xl font-semibold text-gray-900">+23%</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Cost Savings</p>
                        <p class="text-2xl font-semibold text-gray-900">R156K</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Time Saved</p>
                        <p class="text-2xl font-semibold text-gray-900">47hrs</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Optimized Bids</p>
                        <p class="text-2xl font-semibold text-gray-900">28</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Optimizations -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-2xl font-semibold text-gray-900">🎯 Active Bid Optimizations</h2>
                <p class="text-sm text-gray-600">AI-powered recommendations for your current bids</p>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <!-- Optimization Item 1 -->
                    <div class="optimization-card border border-gray-200 rounded-lg p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white text-xl">🏗️</div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-semibold text-gray-900">Road Maintenance Services</h3>
                                    <p class="text-sm text-gray-600">TND-2024-001 • Submitted 2 days ago</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-green-600">78%</div>
                                <div class="text-sm text-gray-600">Success Probability</div>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                <h4 class="font-medium text-green-900 mb-2">💰 Pricing Optimization</h4>
                                <p class="text-sm text-green-700">Reduce bid by 8% to increase win probability by 15%</p>
                                <button onclick="applyOptimization('pricing', 'TND-2024-001')" class="mt-2 text-xs bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700">Apply</button>
                            </div>
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <h4 class="font-medium text-blue-900 mb-2">📝 Content Enhancement</h4>
                                <p class="text-sm text-blue-700">Add 3 key technical specifications to strengthen proposal</p>
                                <button onclick="applyOptimization('content', 'TND-2024-001')" class="mt-2 text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">Apply</button>
                            </div>
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                <h4 class="font-medium text-yellow-900 mb-2">⏰ Timeline Adjustment</h4>
                                <p class="text-sm text-yellow-700">Extend delivery timeline by 2 weeks for better feasibility</p>
                                <button onclick="applyOptimization('timeline', 'TND-2024-001')" class="mt-2 text-xs bg-yellow-600 text-white px-3 py-1 rounded hover:bg-yellow-700">Apply</button>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-gray-600">
                                <span class="font-medium">Psychological Profile:</span> Analytical Bidder • Risk-Averse
                            </div>
                            <button onclick="viewFullAnalysis('TND-2024-001')" class="text-sm text-pink-600 hover:text-pink-800">View Full Analysis →</button>
                        </div>
                    </div>

                    <!-- Optimization Item 2 -->
                    <div class="optimization-card border border-gray-200 rounded-lg p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white text-xl">💻</div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-semibold text-gray-900">IT Infrastructure Upgrade</h3>
                                    <p class="text-sm text-gray-600">TND-2024-002 • Draft</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-yellow-600">65%</div>
                                <div class="text-sm text-gray-600">Success Probability</div>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                <h4 class="font-medium text-red-900 mb-2">⚠️ Risk Mitigation</h4>
                                <p class="text-sm text-red-700">Add contingency plan for potential technical challenges</p>
                                <button onclick="applyOptimization('risk', 'TND-2024-002')" class="mt-2 text-xs bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700">Apply</button>
                            </div>
                            <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                                <h4 class="font-medium text-purple-900 mb-2">🎯 Competitive Edge</h4>
                                <p class="text-sm text-purple-700">Highlight unique cloud migration expertise</p>
                                <button onclick="applyOptimization('competitive', 'TND-2024-002')" class="mt-2 text-xs bg-purple-600 text-white px-3 py-1 rounded hover:bg-purple-700">Apply</button>
                            </div>
                            <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                                <h4 class="font-medium text-indigo-900 mb-2">📊 Data Presentation</h4>
                                <p class="text-sm text-indigo-700">Include performance benchmarks and case studies</p>
                                <button onclick="applyOptimization('data', 'TND-2024-002')" class="mt-2 text-xs bg-indigo-600 text-white px-3 py-1 rounded hover:bg-indigo-700">Apply</button>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-gray-600">
                                <span class="font-medium">Psychological Profile:</span> Strategic Bidder • Innovation-Focused
                            </div>
                            <button onclick="viewFullAnalysis('TND-2024-002')" class="text-sm text-pink-600 hover:text-pink-800">View Full Analysis →</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Optimization Tools -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- AI Recommendations -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900">🤖 AI Recommendations</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h3 class="font-medium text-blue-900 mb-2">💡 Market Intelligence</h3>
                            <p class="text-sm text-blue-700">Construction tenders are 23% more successful when submitted 3-5 days before deadline</p>
                        </div>
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h3 class="font-medium text-green-900 mb-2">📈 Pricing Strategy</h3>
                            <p class="text-sm text-green-700">Your optimal pricing range is 5-12% below market average for your bid profile</p>
                        </div>
                        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                            <h3 class="font-medium text-purple-900 mb-2">🎯 Psychological Insight</h3>
                            <p class="text-sm text-purple-700">Your analytical approach works best with detailed technical specifications</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Optimization History -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900">📊 Optimization History</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <div>
                                <div class="font-medium text-gray-900">Security Services Bid</div>
                                <div class="text-sm text-gray-600">Pricing optimization applied</div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-green-600">+15% success rate</div>
                                <div class="text-xs text-gray-500">2 days ago</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <div>
                                <div class="font-medium text-gray-900">Consulting Project</div>
                                <div class="text-sm text-gray-600">Content enhancement applied</div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-green-600">+8% success rate</div>
                                <div class="text-xs text-gray-500">1 week ago</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <div>
                                <div class="font-medium text-gray-900">Maintenance Contract</div>
                                <div class="text-sm text-gray-600">Timeline adjustment applied</div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-green-600">+12% success rate</div>
                                <div class="text-xs text-gray-500">2 weeks ago</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function runOptimization() {
            alert('🚀 Running AI optimization on all active bids...\n\nAnalyzing:\n• Market conditions\n• Psychological profiles\n• Historical performance\n• Competitive landscape\n\nOptimization complete! Check individual bid recommendations.');
        }

        function applyOptimization(type, bidId) {
            const optimizationTypes = {
                pricing: 'Pricing optimization',
                content: 'Content enhancement',
                timeline: 'Timeline adjustment',
                risk: 'Risk mitigation',
                competitive: 'Competitive positioning',
                data: 'Data presentation'
            };
            
            alert(`✅ ${optimizationTypes[type]} applied to ${bidId}\n\nChanges have been automatically applied to your bid. Success probability updated!`);
        }

        function viewFullAnalysis(bidId) {
            alert(`📊 Opening full AI analysis for ${bidId}...\n\nDetailed analysis includes:\n• Psychological profiling\n• Market positioning\n• Competitive analysis\n• Risk assessment\n• Success probability factors`);
        }

        // Simulate real-time optimization updates
        setInterval(() => {
            console.log(`[${new Date().toLocaleTimeString()}] AI Optimization: Analyzing market conditions and bid performance`);
        }, 15000);
    </script>
</body>
</html>
