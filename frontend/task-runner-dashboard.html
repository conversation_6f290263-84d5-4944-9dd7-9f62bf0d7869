<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - Task Runner Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .task-card { transition: all 0.3s ease; }
        .task-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .running-indicator { animation: pulse 2s infinite; }
        .automation-flow { animation: flow 3s infinite; }
        @keyframes flow { 0%, 100% { opacity: 0.5; } 50% { opacity: 1; } }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="/dashboard.html" class="text-indigo-600 hover:text-indigo-800 mr-4">← Back to Dashboard</a>
                    <h1 class="text-3xl font-bold text-indigo-600">⚡ Task Runner Dashboard</h1>
                    <span class="ml-3 text-sm text-gray-500">Workflow Automation & Task Management</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-green-600 running-indicator">🟢 12 Tasks Running</span>
                    <button onclick="createNewTask()" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">➕ New Task</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Status Overview -->
    <div class="bg-indigo-50 border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-6">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-indigo-600">47</div>
                        <div class="text-xs text-gray-600">Total Tasks</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">12</div>
                        <div class="text-xs text-gray-600">Running</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-yellow-600">8</div>
                        <div class="text-xs text-gray-600">Scheduled</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">23</div>
                        <div class="text-xs text-gray-600">Completed Today</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-red-600">4</div>
                        <div class="text-xs text-gray-600">Failed</div>
                    </div>
                </div>
                <div class="flex space-x-2">
                    <button onclick="pauseAllTasks()" class="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700">⏸️ Pause All</button>
                    <button onclick="resumeAllTasks()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">▶️ Resume All</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Active Tasks -->
        <div class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-900 mb-6">🔄 Active Tasks</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Task 1: Bid Monitoring -->
                <div class="task-card bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">📝 Bid Monitoring Automation</h3>
                                <p class="text-sm text-gray-600">Monitors tender portals for new opportunities</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <span class="running-indicator w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                                    Running
                                </span>
                                <button onclick="viewTaskDetails('bid-monitor')" class="text-indigo-600 hover:text-indigo-800">⚙️</button>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Last Run:</span>
                                <span class="text-sm font-medium">2 minutes ago</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Success Rate:</span>
                                <span class="text-sm font-medium text-green-600">98.5%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">New Tenders Found:</span>
                                <span class="text-sm font-medium text-blue-600">3 today</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="automation-flow bg-green-600 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                            <div class="text-xs text-gray-500">Next run in 8 minutes</div>
                        </div>
                        <div class="mt-4 flex space-x-2">
                            <button onclick="pauseTask('bid-monitor')" class="bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700">Pause</button>
                            <button onclick="runNow('bid-monitor')" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Run Now</button>
                        </div>
                    </div>
                </div>

                <!-- Task 2: BEE Compliance Check -->
                <div class="task-card bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">🏛️ BEE Compliance Checker</h3>
                                <p class="text-sm text-gray-600">Monitors BEE status and compliance deadlines</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <span class="running-indicator w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                                    Running
                                </span>
                                <button onclick="viewTaskDetails('bee-compliance')" class="text-indigo-600 hover:text-indigo-800">⚙️</button>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Last Check:</span>
                                <span class="text-sm font-medium">1 hour ago</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Current Level:</span>
                                <span class="text-sm font-medium text-green-600">Level 4 BEE</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Next Deadline:</span>
                                <span class="text-sm font-medium text-orange-600">March 2024</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="automation-flow bg-green-600 h-2 rounded-full" style="width: 92%"></div>
                            </div>
                            <div class="text-xs text-gray-500">Next check in 23 hours</div>
                        </div>
                        <div class="mt-4 flex space-x-2">
                            <button onclick="pauseTask('bee-compliance')" class="bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700">Pause</button>
                            <button onclick="linkToBEE()" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">View BEE</button>
                        </div>
                    </div>
                </div>

                <!-- Task 3: Tool Maintenance -->
                <div class="task-card bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">🔧 Tool Maintenance Scheduler</h3>
                                <p class="text-sm text-gray-600">Automates tool maintenance scheduling</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <span class="w-2 h-2 bg-blue-400 rounded-full mr-1"></span>
                                    Scheduled
                                </span>
                                <button onclick="viewTaskDetails('tool-maintenance')" class="text-indigo-600 hover:text-indigo-800">⚙️</button>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Next Run:</span>
                                <span class="text-sm font-medium">Tomorrow 9:00 AM</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Tools Monitored:</span>
                                <span class="text-sm font-medium text-blue-600">247 tools</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Maintenance Due:</span>
                                <span class="text-sm font-medium text-orange-600">5 tools</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 45%"></div>
                            </div>
                            <div class="text-xs text-gray-500">Runs daily at 9:00 AM</div>
                        </div>
                        <div class="mt-4 flex space-x-2">
                            <button onclick="runNow('tool-maintenance')" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Run Now</button>
                            <button onclick="linkToToolSync()" class="bg-orange-600 text-white px-3 py-1 rounded text-sm hover:bg-orange-700">View Tools</button>
                        </div>
                    </div>
                </div>

                <!-- Task 4: WhatsApp Notifications -->
                <div class="task-card bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">💬 WhatsApp Auto-Notifications</h3>
                                <p class="text-sm text-gray-600">Sends automated WhatsApp alerts and updates</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <span class="running-indicator w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                                    Running
                                </span>
                                <button onclick="viewTaskDetails('whatsapp-notifications')" class="text-indigo-600 hover:text-indigo-800">⚙️</button>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Messages Sent Today:</span>
                                <span class="text-sm font-medium text-green-600">47 messages</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Delivery Rate:</span>
                                <span class="text-sm font-medium text-green-600">99.2%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Queue:</span>
                                <span class="text-sm font-medium text-blue-600">3 pending</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="automation-flow bg-green-600 h-2 rounded-full" style="width: 78%"></div>
                            </div>
                            <div class="text-xs text-gray-500">Continuous monitoring</div>
                        </div>
                        <div class="mt-4 flex space-x-2">
                            <button onclick="pauseTask('whatsapp-notifications')" class="bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700">Pause</button>
                            <button onclick="linkToWhatsApp()" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">View WhatsApp</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Task Templates -->
        <div class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-900 mb-6">📋 Task Templates</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white rounded-lg shadow p-6 text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <div class="text-2xl">📝</div>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Bid Automation</h3>
                    <p class="text-gray-600 text-sm mb-4">Automate bid monitoring, alerts, and submissions</p>
                    <button onclick="createBidTask()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Create Task</button>
                </div>
                <div class="bg-white rounded-lg shadow p-6 text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <div class="text-2xl">🏛️</div>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Compliance Monitoring</h3>
                    <p class="text-gray-600 text-sm mb-4">Monitor BEE, legal, and regulatory compliance</p>
                    <button onclick="createComplianceTask()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">Create Task</button>
                </div>
                <div class="bg-white rounded-lg shadow p-6 text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <div class="text-2xl">🔧</div>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Resource Management</h3>
                    <p class="text-gray-600 text-sm mb-4">Automate tool, skill, and contractor management</p>
                    <button onclick="createResourceTask()" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">Create Task</button>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">📊 Recent Activity</h2>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">Bid Monitoring completed successfully</p>
                            <p class="text-xs text-gray-500">2 minutes ago • Found 3 new tenders</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">WhatsApp notification sent</p>
                            <p class="text-xs text-gray-500">15 minutes ago • Tender deadline reminder</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-purple-400 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">Tool maintenance scheduled</p>
                            <p class="text-xs text-gray-500">1 hour ago • 5 tools due for service</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-yellow-400 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">BEE compliance check completed</p>
                            <p class="text-xs text-gray-500">3 hours ago • Status: Level 4 BEE maintained</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function createNewTask() {
            alert('➕ Creating New Task\n\nTask types available:\n• Bid monitoring\n• Compliance checking\n• Resource management\n• Notification automation\n• Custom workflows\n\nSelect task type and configure automation rules.');
        }

        function viewTaskDetails(taskId) {
            const tasks = {
                'bid-monitor': 'Bid Monitoring Automation',
                'bee-compliance': 'BEE Compliance Checker',
                'tool-maintenance': 'Tool Maintenance Scheduler',
                'whatsapp-notifications': 'WhatsApp Auto-Notifications'
            };
            
            alert(`⚙️ Task Details: ${tasks[taskId]}\n\nConfiguration:\n• Schedule settings\n• Trigger conditions\n• Action parameters\n• Error handling\n• Notification rules\n\nEdit task configuration?`);
        }

        function pauseTask(taskId) {
            alert(`⏸️ Pausing task: ${taskId}\n\n✅ Task execution paused\n✅ Current state saved\n✅ Notifications disabled\n\nTask can be resumed at any time.`);
        }

        function runNow(taskId) {
            alert(`▶️ Running task immediately: ${taskId}\n\n✅ Task execution started\n✅ Progress monitoring active\n✅ Results will be logged\n\nTask running in background...`);
        }

        function pauseAllTasks() {
            alert('⏸️ Pausing All Tasks\n\n✅ 12 running tasks paused\n✅ Scheduled tasks suspended\n✅ System in maintenance mode\n\nAll automation temporarily stopped.');
        }

        function resumeAllTasks() {
            alert('▶️ Resuming All Tasks\n\n✅ 12 tasks resumed\n✅ Schedules reactivated\n✅ Automation fully operational\n\nAll systems back online!');
        }

        function linkToBEE() {
            window.open('/bee-compliance-dashboard.html', '_blank');
        }

        function linkToToolSync() {
            window.open('/toolsync-dashboard.html', '_blank');
        }

        function linkToWhatsApp() {
            window.open('/whatsapp-dashboard.html', '_blank');
        }

        function createBidTask() {
            alert('📝 Creating Bid Automation Task\n\nAvailable automations:\n• Monitor tender portals\n• Alert on new opportunities\n• Auto-submit bids\n• Track bid status\n• Generate reports\n\nConfigure your bid automation workflow.');
        }

        function createComplianceTask() {
            alert('🏛️ Creating Compliance Monitoring Task\n\nCompliance areas:\n• BEE status monitoring\n• Certificate renewals\n• Regulatory updates\n• Deadline tracking\n• Report generation\n\nSetup automated compliance management.');
        }

        function createResourceTask() {
            alert('🔧 Creating Resource Management Task\n\nResource automations:\n• Tool maintenance scheduling\n• Skill gap monitoring\n• Contractor availability\n• Inventory tracking\n• Performance analytics\n\nAutomate your resource management.');
        }

        // Simulate real-time task monitoring
        setInterval(() => {
            console.log(`[${new Date().toLocaleTimeString()}] Task Runner: Monitoring 47 tasks, 12 currently running`);
        }, 30000);
    </script>
</body>
</html>
