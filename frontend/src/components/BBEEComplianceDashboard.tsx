import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  Card<PERSON>eader,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Chip,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogActions,
  Alert,
  LinearProgress,
  Box,
  Grid,
  IconButton
} from '@mui/material';
import {
  Shield,
  EmojiEvents as Award,
  Warning as AlertTriangle,
  CalendarToday as Calendar,
  TrendingUp,
  CheckCircle,
  Schedule as Clock,
  Upload,
  Download,
  Visibility as Eye,
  Refresh as RefreshCw,
  Notifications as Bell,
  <PERSON><PERSON>hart as BarChart3,
  Description as FileText,
  People as Users
} from '@mui/icons-material';

interface BBEEStatus {
  supplier_id: string;
  current_level: number;
  status: string;
  compliance_score: number;
  risk_level: string;
  days_to_expiry: number;
  renewal_required: boolean;
  verification_status: string;
  last_updated: string;
}

interface BBEECertificate {
  supplier_id: string;
  bbbee_level: number;
  certificate_number: string;
  issuing_authority: string;
  issue_date: string;
  expiry_date: string;
  certificate_url?: string;
  verification_agency: string;
  annual_turnover?: number;
  ownership_score?: number;
  management_control_score?: number;
  skills_development_score?: number;
  enterprise_development_score?: number;
  socioeconomic_development_score?: number;
}

interface BBEEAlert {
  alert_id: string;
  supplier_id: string;
  alert_type: string;
  severity: string;
  message: string;
  created_at: string;
  resolved: boolean;
}

interface BBEEAnalytics {
  total_suppliers: number;
  compliant_suppliers: number;
  compliance_rate: number;
  level_distribution: Record<number, number>;
  expiring_certificates: number;
  risk_distribution: Record<string, number>;
  verification_backlog: number;
}

const BBEEComplianceDashboard: React.FC = () => {
  const [bbeeStatus, setBbeeStatus] = useState<BBEEStatus | null>(null);
  const [certificate, setCertificate] = useState<BBEECertificate | null>(null);
  const [alerts, setAlerts] = useState<BBEEAlert[]>([]);
  const [analytics, setAnalytics] = useState<BBEEAnalytics | null>(null);
  const [loading, setLoading] = useState(false);
  const [showCertificateForm, setShowCertificateForm] = useState(false);
  const [formData, setFormData] = useState<Partial<BBEECertificate>>({
    bbbee_level: 1,
    certificate_number: '',
    issuing_authority: '',
    verification_agency: 'SANAS Accredited Verification Agency',
    issue_date: '',
    expiry_date: ''
  });

  useEffect(() => {
    loadBBEEData();
  }, []);

  const loadBBEEData = async () => {
    setLoading(true);
    try {
      const supplierId = 'current-supplier-id'; // Replace with actual auth
      
      // Load B-BBEE status
      const statusResponse = await fetch(`/api/bbee/status/${supplierId}`);
      if (statusResponse.ok) {
        const status = await statusResponse.json();
        setBbeeStatus(status);
      }

      // Load certificate details
      const certResponse = await fetch(`/api/bbee/certificate/${supplierId}`);
      if (certResponse.ok) {
        const cert = await certResponse.json();
        setCertificate(cert);
      }

      // Load alerts
      const alertsResponse = await fetch(`/api/bbee/alerts/${supplierId}`);
      if (alertsResponse.ok) {
        const alertsData = await alertsResponse.json();
        setAlerts(alertsData.alerts || []);
      }

      // Load analytics (for admin view)
      const analyticsResponse = await fetch('/api/bbee/analytics');
      if (analyticsResponse.ok) {
        const analyticsData = await analyticsResponse.json();
        setAnalytics(analyticsData);
      }

    } catch (err) {
      console.error('Failed to load B-BBEE data:', err);
    } finally {
      setLoading(false);
    }
  };

  const submitCertificate = async () => {
    try {
      setLoading(true);
      
      const certData = {
        ...formData,
        supplier_id: 'current-supplier-id', // Replace with actual auth
        bbbee_level: parseInt(formData.bbbee_level?.toString() || '1'),
        annual_turnover: formData.annual_turnover ? parseFloat(formData.annual_turnover.toString()) : undefined
      };

      const response = await fetch('/api/bbee/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(certData),
      });

      if (response.ok) {
        await loadBBEEData();
        setShowCertificateForm(false);
        resetForm();
      } else {
        throw new Error('Failed to register certificate');
      }
    } catch (err) {
      console.error('Error submitting certificate:', err);
    } finally {
      setLoading(false);
    }
  };

  const resolveAlert = async (alertId: string) => {
    try {
      const response = await fetch(`/api/bbee/alerts/${alertId}/resolve`, {
        method: 'PUT'
      });

      if (response.ok) {
        setAlerts(prev => prev.filter(alert => alert.alert_id !== alertId));
      }
    } catch (err) {
      console.error('Error resolving alert:', err);
    }
  };

  const resetForm = () => {
    setFormData({
      bbbee_level: 1,
      certificate_number: '',
      issuing_authority: '',
      verification_agency: 'SANAS Accredited Verification Agency',
      issue_date: '',
      expiry_date: ''
    });
  };

  const procurementRecognition = bbeeStatus ? {
    1: 135, 2: 125, 3: 110, 4: 100, 5: 80, 6: 60, 7: 50, 8: 10
  }[bbeeStatus.current_level] || 0 : 0;

  return (
    <Box sx={{ maxWidth: '1200px', mx: 'auto', p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            B-BBEE Compliance
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your B-BBEE certification and compliance status
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button variant="outlined" onClick={loadBBEEData} startIcon={<RefreshCw />}>
            Refresh
          </Button>
          <Button variant="contained" onClick={() => setShowCertificateForm(true)} startIcon={<Upload />}>
            {certificate ? 'Update Certificate' : 'Register Certificate'}
          </Button>
        </Box>
      </Box>

      {/* B-BBEE Status Overview */}
      {bbeeStatus && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ pt: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      B-BBEE Level
                    </Typography>
                    <Typography variant="h3" component="div" fontWeight="bold" color="primary">
                      {bbeeStatus.current_level}
                    </Typography>
                  </Box>
                  <Award sx={{ fontSize: 40, color: 'warning.main' }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ pt: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Compliance Score
                    </Typography>
                    <Typography variant="h4" component="div" fontWeight="bold" color="success.main">
                      {bbeeStatus.compliance_score.toFixed(1)}%
                    </Typography>
                  </Box>
                  <Shield sx={{ fontSize: 40, color: 'success.main' }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ pt: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Days to Expiry
                    </Typography>
                    <Typography 
                      variant="h4" 
                      component="div" 
                      fontWeight="bold" 
                      color={bbeeStatus.days_to_expiry <= 30 ? 'error.main' : 'success.main'}
                    >
                      {bbeeStatus.days_to_expiry}
                    </Typography>
                  </Box>
                  <Calendar sx={{ fontSize: 40, color: 'info.main' }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ pt: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Risk Level
                    </Typography>
                    <Typography variant="h4" component="div" fontWeight="bold" color="warning.main">
                      {bbeeStatus.risk_level.toUpperCase()}
                    </Typography>
                  </Box>
                  <AlertTriangle sx={{ fontSize: 40, color: 'warning.main' }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Status and Alerts */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Current Status */}
        <Grid item xs={12} lg={6}>
          <Card sx={{ height: '100%' }}>
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Shield />
                  <Typography variant="h6">Current Status</Typography>
                </Box>
              }
            />
            <CardContent>
              {bbeeStatus ? (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography>Status:</Typography>
                    <Chip 
                      label={bbeeStatus.status.replace('_', ' ')} 
                      color={bbeeStatus.status === 'active' ? 'success' : 'warning'}
                      size="small"
                    />
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography>Procurement Recognition:</Typography>
                    <Typography fontWeight="bold" color="success.main">
                      {procurementRecognition}%
                    </Typography>
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography>Verification Status:</Typography>
                    <Chip 
                      label={bbeeStatus.verification_status}
                      color={bbeeStatus.verification_status === 'verified' ? 'success' : 'default'}
                      size="small"
                    />
                  </Box>
                  
                  {bbeeStatus.renewal_required && (
                    <Alert severity="warning" sx={{ mt: 2 }}>
                      Certificate renewal required within {bbeeStatus.days_to_expiry} days
                    </Alert>
                  )}
                  
                  <Box sx={{ mt: 2 }}>
                    <LinearProgress 
                      variant="determinate" 
                      value={bbeeStatus.compliance_score} 
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                    <Typography variant="body2" color="text.secondary" align="center" sx={{ mt: 1 }}>
                      Compliance Score: {bbeeStatus.compliance_score.toFixed(1)}%
                    </Typography>
                  </Box>
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Shield sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    No B-BBEE Certificate
                  </Typography>
                  <Typography color="text.secondary" sx={{ mb: 2 }}>
                    Register your B-BBEE certificate to get started
                  </Typography>
                  <Button variant="contained" onClick={() => setShowCertificateForm(true)}>
                    Register Certificate
                  </Button>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Alerts */}
        <Grid item xs={12} lg={6}>
          <Card sx={{ height: '100%' }}>
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Bell />
                  <Typography variant="h6">Compliance Alerts</Typography>
                </Box>
              }
            />
            <CardContent>
              {alerts.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <CheckCircle sx={{ fontSize: 60, color: 'success.main', mb: 2 }} />
                  <Typography color="text.secondary">No active alerts</Typography>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {alerts.map((alert) => (
                    <Card key={alert.alert_id} variant="outlined">
                      <CardContent sx={{ p: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                          <Chip 
                            label={alert.severity}
                            color={alert.severity === 'critical' ? 'error' : alert.severity === 'warning' ? 'warning' : 'info'}
                            size="small"
                          />
                          <Button 
                            variant="text" 
                            size="small"
                            onClick={() => resolveAlert(alert.alert_id)}
                          >
                            Resolve
                          </Button>
                        </Box>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                          {alert.message}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(alert.created_at).toLocaleDateString()}
                        </Typography>
                      </CardContent>
                    </Card>
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Certificate Details */}
      {certificate && (
        <Card sx={{ mb: 3 }}>
          <CardHeader
            title={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <FileText />
                <Typography variant="h6">Certificate Details</Typography>
              </Box>
            }
          />
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Typography variant="body2" color="text.secondary">Certificate Number:</Typography>
                <Typography variant="body1" fontFamily="monospace">{certificate.certificate_number}</Typography>
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="body2" color="text.secondary">Issuing Authority:</Typography>
                <Typography variant="body1">{certificate.issuing_authority}</Typography>
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="body2" color="text.secondary">Verification Agency:</Typography>
                <Typography variant="body1">{certificate.verification_agency}</Typography>
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="body2" color="text.secondary">Issue Date:</Typography>
                <Typography variant="body1">{new Date(certificate.issue_date).toLocaleDateString()}</Typography>
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="body2" color="text.secondary">Expiry Date:</Typography>
                <Typography variant="body1">{new Date(certificate.expiry_date).toLocaleDateString()}</Typography>
              </Grid>
              {certificate.annual_turnover && (
                <Grid item xs={12} md={4}>
                  <Typography variant="body2" color="text.secondary">Annual Turnover:</Typography>
                  <Typography variant="body1">R{certificate.annual_turnover.toLocaleString()}</Typography>
                </Grid>
              )}
            </Grid>

            {/* Scorecard Elements */}
            {(certificate.ownership_score || certificate.management_control_score) && (
              <Box sx={{ mt: 4 }}>
                <Typography variant="h6" gutterBottom>Scorecard Elements</Typography>
                <Grid container spacing={2}>
                  {certificate.ownership_score && (
                    <Grid item xs={6} md={2.4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" fontWeight="bold" color="info.main">
                          {certificate.ownership_score}%
                        </Typography>
                        <Typography variant="body2" color="text.secondary">Ownership</Typography>
                      </Box>
                    </Grid>
                  )}
                  {certificate.management_control_score && (
                    <Grid item xs={6} md={2.4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" fontWeight="bold" color="success.main">
                          {certificate.management_control_score}%
                        </Typography>
                        <Typography variant="body2" color="text.secondary">Management</Typography>
                      </Box>
                    </Grid>
                  )}
                  {certificate.skills_development_score && (
                    <Grid item xs={6} md={2.4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" fontWeight="bold" color="secondary.main">
                          {certificate.skills_development_score}%
                        </Typography>
                        <Typography variant="body2" color="text.secondary">Skills Dev</Typography>
                      </Box>
                    </Grid>
                  )}
                  {certificate.enterprise_development_score && (
                    <Grid item xs={6} md={2.4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" fontWeight="bold" color="warning.main">
                          {certificate.enterprise_development_score}%
                        </Typography>
                        <Typography variant="body2" color="text.secondary">Enterprise Dev</Typography>
                      </Box>
                    </Grid>
                  )}
                  {certificate.socioeconomic_development_score && (
                    <Grid item xs={6} md={2.4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" fontWeight="bold" color="error.main">
                          {certificate.socioeconomic_development_score}%
                        </Typography>
                        <Typography variant="body2" color="text.secondary">Socioeconomic</Typography>
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </Box>
            )}
          </CardContent>
        </Card>
      )}

      {/* Analytics (Admin View) */}
      {analytics && (
        <Card sx={{ mb: 3 }}>
          <CardHeader
            title={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <BarChart3 />
                <Typography variant="h6">B-BBEE Analytics</Typography>
              </Box>
            }
          />
          <CardContent>
            <Grid container spacing={3} sx={{ mb: 3 }}>
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" fontWeight="bold" color="info.main">
                    {analytics.total_suppliers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">Total Suppliers</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    {analytics.compliant_suppliers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">Compliant</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" fontWeight="bold" color="secondary.main">
                    {analytics.compliance_rate.toFixed(1)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">Compliance Rate</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" fontWeight="bold" color="warning.main">
                    {analytics.expiring_certificates}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">Expiring Soon</Typography>
                </Box>
              </Grid>
            </Grid>

            <Box>
              <Typography variant="h6" gutterBottom>Level Distribution</Typography>
              <Grid container spacing={1}>
                {[1,2,3,4,5,6,7,8].map(level => (
                  <Grid item xs={3} md={1.5} key={level}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Box 
                        sx={{ 
                          width: '100%', 
                          height: 32, 
                          borderRadius: 1, 
                          display: 'flex', 
                          alignItems: 'center', 
                          justifyContent: 'center', 
                          color: 'white', 
                          fontWeight: 'bold',
                          bgcolor: level <= 2 ? 'success.main' : level <= 4 ? 'info.main' : level <= 6 ? 'warning.main' : 'error.main'
                        }}
                      >
                        {analytics.level_distribution[level] || 0}
                      </Box>
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                        Level {level}
                      </Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Certificate Registration Dialog */}
      <Dialog open={showCertificateForm} onClose={() => setShowCertificateForm(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {certificate ? 'Update B-BBEE Certificate' : 'Register B-BBEE Certificate'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>B-BBEE Level</InputLabel>
                <Select
                  value={formData.bbbee_level?.toString() || ''}
                  label="B-BBEE Level"
                  onChange={(e) => setFormData(prev => ({ ...prev, bbbee_level: parseInt(e.target.value) }))}
                >
                  {[1,2,3,4,5,6,7,8].map(level => (
                    <MenuItem key={level} value={level.toString()}>
                      Level {level}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Certificate Number"
                value={formData.certificate_number || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, certificate_number: e.target.value }))}
                placeholder="Enter certificate number"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Issuing Authority"
                value={formData.issuing_authority || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, issuing_authority: e.target.value }))}
                placeholder="e.g., Department of Trade and Industry"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Verification Agency</InputLabel>
                <Select
                  value={formData.verification_agency || ''}
                  label="Verification Agency"
                  onChange={(e) => setFormData(prev => ({ ...prev, verification_agency: e.target.value }))}
                >
                  <MenuItem value="SANAS Accredited Verification Agency">SANAS Accredited</MenuItem>
                  <MenuItem value="Independent Regulatory Board for Auditors (IRBA)">IRBA</MenuItem>
                  <MenuItem value="Chartered Accountants South Africa (SAICA)">SAICA</MenuItem>
                  <MenuItem value="Association for the Advancement of Black Accountants (AABA)">AABA</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Issue Date"
                type="date"
                value={formData.issue_date || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, issue_date: e.target.value }))}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Expiry Date"
                type="date"
                value={formData.expiry_date || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, expiry_date: e.target.value }))}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Annual Turnover (Optional)"
                type="number"
                value={formData.annual_turnover || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, annual_turnover: parseFloat(e.target.value) }))}
                placeholder="Enter annual turnover in ZAR"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCertificateForm(false)}>
            Cancel
          </Button>
          <Button onClick={submitCertificate} disabled={loading} variant="contained">
            {loading ? 'Submitting...' : (certificate ? 'Update Certificate' : 'Register Certificate')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BBEEComplianceDashboard;
