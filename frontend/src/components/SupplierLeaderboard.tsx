import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Button,
  Chip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Tabs,
  Tab,
  LinearProgress,
  Avatar,
  Box,
  Typography,
  Grid,
  Container,
  Paper,
  Stack,
  Divider
} from '@mui/material';
import {
  EmojiEvents as Trophy,
  WorkspacePremium as Medal,
  Star,
  TrendingUp,
  TrendingDown,
  EmojiEvents as Award,
  WorkspacePremium as Crown,
  Bolt as Zap,
  GpsFixed as Target,
  People as Users,
  CalendarToday as Calendar,
  BarChart as BarChart3,
  FilterList as Filter,
  Refresh as RefreshCw,
  KeyboardArrowUp as ChevronUp,
  KeyboardArrowDown as ChevronDown,
  Whatshot as Flame
} from '@mui/icons-material';

interface LeaderboardEntry {
  rank: number;
  supplier_id: string;
  supplier_name: string;
  company_name: string;
  score: number;
  points: number;
  badges: BadgeType[];
  streak: number;
  level: string;
  bee_level: number;
  location: string;
  change_from_last_period: number;
}

interface BadgeType {
  badge_id: string;
  name: string;
  description: string;
  icon: string;
  tier: string;
  category: string;
  points_value: number;
  rarity: string;
}

interface GamificationStats {
  total_points: number;
  current_level: string;
  next_level: string;
  points_to_next_level: number;
  total_badges: number;
  current_streak: number;
  longest_streak: number;
  rank_overall: number;
  rank_category: number;
  achievements_unlocked: number;
  completion_percentage: number;
}

interface GamificationAnalytics {
  total_active_suppliers: number;
  total_points_awarded: number;
  average_points: number;
  level_distribution: Record<string, number>;
  badge_distribution: Record<string, number>;
  engagement_metrics: {
    active_streaks: number;
    total_badges_awarded: number;
  };
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const SupplierLeaderboard: React.FC = () => {
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [myStats, setMyStats] = useState<GamificationStats | null>(null);
  const [analytics, setAnalytics] = useState<GamificationAnalytics | null>(null);
  const [availableBadges, setAvailableBadges] = useState<BadgeType[]>([]);
  const [levels, setLevels] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    period: 'monthly',
    category: '',
    limit: 50
  });
  const [tabValue, setTabValue] = useState(0);

  useEffect(() => {
    loadLeaderboardData();
  }, [filters]);

  const loadLeaderboardData = async () => {
    setLoading(true);
    try {
      // Load leaderboard
      const params = new URLSearchParams({
        period: filters.period,
        limit: filters.limit.toString()
      });
      if (filters.category) params.append('category', filters.category);

      const leaderboardResponse = await fetch(`/api/gamification/leaderboard?${params}`);
      if (leaderboardResponse.ok) {
        const data = await leaderboardResponse.json();
        setLeaderboard(data.leaderboard || []);
      }

      // Load my stats
      const supplierId = 'current-supplier-id'; // Replace with actual auth
      const statsResponse = await fetch(`/api/gamification/supplier/${supplierId}/gamification`);
      if (statsResponse.ok) {
        const stats = await statsResponse.json();
        setMyStats(stats);
      }

      // Load analytics
      const analyticsResponse = await fetch('/api/gamification/analytics/gamification');
      if (analyticsResponse.ok) {
        const analyticsData = await analyticsResponse.json();
        setAnalytics(analyticsData);
      }

      // Load available badges
      const badgesResponse = await fetch('/api/gamification/badges/available');
      if (badgesResponse.ok) {
        const badgesData = await badgesResponse.json();
        setAvailableBadges(badgesData.badges || []);
      }

      // Load levels
      const levelsResponse = await fetch('/api/gamification/levels');
      if (levelsResponse.ok) {
        const levelsData = await levelsResponse.json();
        setLevels(levelsData.levels || {});
      }

    } catch (err) {
      console.error('Failed to load leaderboard data:', err);
    } finally {
      setLoading(false);
    }
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return <Crown sx={{ fontSize: 24, color: 'gold' }} />;
      case 2: return <Medal sx={{ fontSize: 24, color: 'silver' }} />;
      case 3: return <Award sx={{ fontSize: 24, color: '#cd7f32' }} />;
      default: return <Typography variant="h6" fontWeight="bold" color="text.secondary">#{rank}</Typography>;
    }
  };

  const getLevelColor = (level: string) => {
    const colors = {
      'Rookie': 'default',
      'Explorer': 'primary',
      'Achiever': 'success',
      'Expert': 'warning',
      'Master': 'error',
      'Legend': 'secondary',
      'Champion': 'info',
      'Elite': 'warning'
    };
    return colors[level as keyof typeof colors] || 'default';
  };

  const getTierColor = (tier: string) => {
    const colors = {
      'bronze': '#cd7f32',
      'silver': '#c0c0c0',
      'gold': '#ffd700',
      'platinum': '#e5e4e2',
      'diamond': '#b9f2ff'
    };
    return colors[tier as keyof typeof colors] || '#666';
  };

  const getBeeColor = (level: number) => {
    if (level <= 2) return 'success.main';
    if (level <= 4) return 'primary.main';
    if (level <= 6) return 'warning.main';
    return 'error.main';
  };

  const getChangeIcon = (change: number) => {
    if (change > 0) return <ChevronUp sx={{ color: 'success.main' }} />;
    if (change < 0) return <ChevronDown sx={{ color: 'error.main' }} />;
    return <Typography color="text.disabled">-</Typography>;
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <Stack spacing={3}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
              Supplier Leaderboard
            </Typography>
            <Typography variant="h6" color="text.secondary">
              Compete, achieve, and climb the rankings
            </Typography>
          </Box>
          <Button variant="outlined" onClick={loadLeaderboardData} startIcon={<RefreshCw />}>
            Refresh
          </Button>
        </Stack>

        {/* My Stats */}
        {myStats && (
          <Card>
            <CardContent>
              <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 3 }}>
                <Target />
                <Typography variant="h6">Your Performance</Typography>
              </Stack>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6} lg={3} textAlign="center">
                  <Box sx={{ mb: 2 }}>
                    <Chip 
                      label={myStats.current_level}
                      color={getLevelColor(myStats.current_level) as any}
                    />
                  </Box>
                  <Typography variant="body2" color="text.secondary">Current Level</Typography>
                  <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 1 }}>
                    {myStats.points_to_next_level} points to {myStats.next_level}
                  </Typography>
                  <LinearProgress 
                    variant="determinate"
                    value={((myStats.total_points % 1000) / 1000) * 100} 
                    sx={{ mt: 1, height: 8, borderRadius: 4 }}
                  />
                </Grid>

                <Grid item xs={12} md={6} lg={3} textAlign="center">
                  <Stack direction="row" spacing={1} justifyContent="center" alignItems="center">
                    <Star sx={{ color: 'primary.main' }} />
                    <Typography variant="h4" fontWeight="bold" color="primary.main">
                      {myStats.total_points.toLocaleString()}
                    </Typography>
                  </Stack>
                  <Typography variant="body2" color="text.secondary">Total Points</Typography>
                  <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 1 }}>
                    Rank #{myStats.rank_overall}
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6} lg={3} textAlign="center">
                  <Stack direction="row" spacing={1} justifyContent="center" alignItems="center">
                    <Flame sx={{ color: 'warning.main' }} />
                    <Typography variant="h4" fontWeight="bold" color="warning.main">
                      {myStats.current_streak}
                    </Typography>
                  </Stack>
                  <Typography variant="body2" color="text.secondary">Current Streak</Typography>
                  <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 1 }}>
                    Best: {myStats.longest_streak} days
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6} lg={3} textAlign="center">
                  <Stack direction="row" spacing={1} justifyContent="center" alignItems="center">
                    <Award sx={{ color: 'secondary.main' }} />
                    <Typography variant="h4" fontWeight="bold" color="secondary.main">
                      {myStats.total_badges}
                    </Typography>
                  </Stack>
                  <Typography variant="body2" color="text.secondary">Badges Earned</Typography>
                  <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 1 }}>
                    {myStats.completion_percentage.toFixed(0)}% complete
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        )}

        {/* Filters */}
        <Card>
          <CardContent>
            <Stack direction="row" spacing={2} alignItems="center">
              <Stack direction="row" spacing={1} alignItems="center">
                <Filter />
                <Typography variant="body2" fontWeight="medium">Filters:</Typography>
              </Stack>
              
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Period</InputLabel>
                <Select 
                  value={filters.period} 
                  label="Period"
                  onChange={(e) => setFilters(prev => ({ ...prev, period: e.target.value }))}
                >
                  <MenuItem value="monthly">Monthly</MenuItem>
                  <MenuItem value="quarterly">Quarterly</MenuItem>
                  <MenuItem value="yearly">Yearly</MenuItem>
                  <MenuItem value="all_time">All Time</MenuItem>
                </Select>
              </FormControl>

              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Category</InputLabel>
                <Select 
                  value={filters.category} 
                  label="Category"
                  onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
                >
                  <MenuItem value="">All Categories</MenuItem>
                  <MenuItem value="construction">Construction</MenuItem>
                  <MenuItem value="services">Services</MenuItem>
                  <MenuItem value="goods">Goods</MenuItem>
                  <MenuItem value="consulting">Consulting</MenuItem>
                </Select>
              </FormControl>

              <FormControl size="small" sx={{ minWidth: 100 }}>
                <InputLabel>Limit</InputLabel>
                <Select 
                  value={filters.limit.toString()} 
                  label="Limit"
                  onChange={(e) => setFilters(prev => ({ ...prev, limit: parseInt(e.target.value) }))}
                >
                  <MenuItem value="25">Top 25</MenuItem>
                  <MenuItem value="50">Top 50</MenuItem>
                  <MenuItem value="100">Top 100</MenuItem>
                </Select>
              </FormControl>
            </Stack>
          </CardContent>
        </Card>

        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Leaderboard" />
            <Tab label="Badges" />
            <Tab label="Analytics" />
          </Tabs>
        </Box>

        <CustomTabPanel value={tabValue} index={0}>
          {loading ? (
            <Box textAlign="center" py={4}>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                <Box sx={{ width: 40, height: 40, border: 2, borderColor: 'primary.main', borderTopColor: 'transparent', borderRadius: '50%', animation: 'spin 1s linear infinite' }} />
              </Box>
              <Typography color="text.secondary">Loading leaderboard...</Typography>
            </Box>
          ) : leaderboard.length === 0 ? (
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 4 }}>
                <Trophy sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
                <Typography variant="h6" gutterBottom>No Rankings Yet</Typography>
                <Typography color="text.secondary">Start participating to see rankings</Typography>
              </CardContent>
            </Card>
          ) : (
            <Stack spacing={2}>
              {leaderboard.map((entry, index) => (
                <Card 
                  key={entry.supplier_id}
                  sx={{
                    transition: 'all 0.3s',
                    '&:hover': { boxShadow: 3 },
                    ...(entry.rank <= 3 && {
                      border: 2,
                      borderColor: 'warning.light',
                      background: 'linear-gradient(to right, #fff8e1, #ffffff)'
                    })
                  }}
                >
                  <CardContent>
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                      <Stack direction="row" spacing={2} alignItems="center">
                        <Stack direction="row" spacing={1} alignItems="center">
                          {getRankIcon(entry.rank)}
                          {getChangeIcon(entry.change_from_last_period)}
                        </Stack>
                        
                        <Avatar sx={{ width: 48, height: 48 }}>
                          {entry.company_name ? entry.company_name.substring(0, 2).toUpperCase() : 'SU'}
                        </Avatar>
                        
                        <Box>
                          <Typography variant="h6" fontWeight="semibold">
                            {entry.company_name || entry.supplier_name}
                          </Typography>
                          <Stack direction="row" spacing={1} alignItems="center">
                            <Typography variant="body2" color="text.secondary">
                              {entry.location}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">•</Typography>
                            <Typography 
                              variant="body2" 
                              sx={{ color: getBeeColor(entry.bee_level) }}
                            >
                              B-BBEE Level {entry.bee_level}
                            </Typography>
                          </Stack>
                        </Box>
                      </Stack>
                      
                      <Box textAlign="right">
                        <Typography variant="h4" fontWeight="bold" color="primary.main">
                          {entry.score.toFixed(1)}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {entry.points.toLocaleString()} pts
                        </Typography>
                      </Box>
                    </Stack>
                    
                    <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mt: 2 }}>
                      <Stack direction="row" spacing={2} alignItems="center">
                        <Chip 
                          label={entry.level}
                          color={getLevelColor(entry.level) as any}
                          size="small"
                        />
                        
                        {entry.streak > 0 && (
                          <Stack direction="row" spacing={0.5} alignItems="center">
                            <Flame sx={{ fontSize: 16, color: 'warning.main' }} />
                            <Typography variant="body2" fontWeight="medium" color="warning.main">
                              {entry.streak} day streak
                            </Typography>
                          </Stack>
                        )}
                        
                        <Stack direction="row" spacing={0.5} alignItems="center">
                          <Award sx={{ fontSize: 16, color: 'secondary.main' }} />
                          <Typography variant="body2" color="text.secondary">
                            {entry.badges.length} badges
                          </Typography>
                        </Stack>
                      </Stack>
                      
                      <Stack direction="row" spacing={0.5}>
                        {entry.badges.slice(0, 3).map((badge, badgeIndex) => (
                          <Typography 
                            key={badgeIndex}
                            sx={{ 
                              fontSize: 18,
                              color: getTierColor(badge.tier)
                            }}
                            title={badge.name}
                          >
                            {badge.icon}
                          </Typography>
                        ))}
                        {entry.badges.length > 3 && (
                          <Typography variant="body2" color="text.secondary">
                            +{entry.badges.length - 3}
                          </Typography>
                        )}
                      </Stack>
                    </Stack>
                  </CardContent>
                </Card>
              ))}
            </Stack>
          )}
        </CustomTabPanel>

        <CustomTabPanel value={tabValue} index={1}>
          <Grid container spacing={2}>
            {availableBadges.map((badge, index) => (
              <Grid item xs={12} md={6} lg={4} key={index}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography 
                      sx={{ 
                        fontSize: 32,
                        color: getTierColor(badge.tier),
                        mb: 1
                      }}
                    >
                      {badge.icon}
                    </Typography>
                    <Typography variant="h6" fontWeight="semibold" gutterBottom>
                      {badge.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {badge.description}
                    </Typography>
                    <Stack direction="row" spacing={1} justifyContent="center">
                      <Chip 
                        label={badge.tier}
                        variant="outlined"
                        size="small"
                        sx={{ color: getTierColor(badge.tier) }}
                      />
                      <Chip 
                        label={`${badge.points_value} pts`}
                        variant="outlined"
                        size="small"
                      />
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </CustomTabPanel>

        <CustomTabPanel value={tabValue} index={2}>
          {analytics && (
            <Stack spacing={3}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={3}>
                  <Card>
                    <CardContent>
                      <Stack direction="row" justifyContent="space-between" alignItems="center">
                        <Box>
                          <Typography variant="body2" fontWeight="medium" color="text.secondary">
                            Active Suppliers
                          </Typography>
                          <Typography variant="h4" fontWeight="bold" color="primary.main">
                            {analytics.total_active_suppliers}
                          </Typography>
                        </Box>
                        <Users sx={{ fontSize: 32, color: 'primary.main' }} />
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={3}>
                  <Card>
                    <CardContent>
                      <Stack direction="row" justifyContent="space-between" alignItems="center">
                        <Box>
                          <Typography variant="body2" fontWeight="medium" color="text.secondary">
                            Total Points
                          </Typography>
                          <Typography variant="h4" fontWeight="bold" color="success.main">
                            {analytics.total_points_awarded.toLocaleString()}
                          </Typography>
                        </Box>
                        <Star sx={{ fontSize: 32, color: 'success.main' }} />
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={3}>
                  <Card>
                    <CardContent>
                      <Stack direction="row" justifyContent="space-between" alignItems="center">
                        <Box>
                          <Typography variant="body2" fontWeight="medium" color="text.secondary">
                            Avg Points
                          </Typography>
                          <Typography variant="h4" fontWeight="bold" color="secondary.main">
                            {Math.round(analytics.average_points).toLocaleString()}
                          </Typography>
                        </Box>
                        <BarChart3 sx={{ fontSize: 32, color: 'secondary.main' }} />
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={3}>
                  <Card>
                    <CardContent>
                      <Stack direction="row" justifyContent="space-between" alignItems="center">
                        <Box>
                          <Typography variant="body2" fontWeight="medium" color="text.secondary">
                            Active Streaks
                          </Typography>
                          <Typography variant="h4" fontWeight="bold" color="warning.main">
                            {analytics.engagement_metrics.active_streaks}
                          </Typography>
                        </Box>
                        <Flame sx={{ fontSize: 32, color: 'warning.main' }} />
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              <Grid container spacing={3}>
                <Grid item xs={12} lg={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Level Distribution</Typography>
                      <Stack spacing={2}>
                        {Object.entries(analytics.level_distribution).map(([level, count]) => (
                          <Stack key={level} direction="row" justifyContent="space-between" alignItems="center">
                            <Chip 
                              label={level}
                              color={getLevelColor(level) as any}
                              size="small"
                            />
                            <Typography fontWeight="medium">{count}</Typography>
                          </Stack>
                        ))}
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} lg={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Popular Badges</Typography>
                      <Stack spacing={2}>
                        {Object.entries(analytics.badge_distribution)
                          .sort(([,a], [,b]) => (b as number) - (a as number))
                          .slice(0, 5)
                          .map(([badge, count]) => (
                          <Stack key={badge} direction="row" justifyContent="space-between" alignItems="center">
                            <Typography variant="body2">{badge}</Typography>
                            <Typography fontWeight="medium">{count}</Typography>
                          </Stack>
                        ))}
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Stack>
          )}
        </CustomTabPanel>
      </Stack>
    </Container>
  );
};

export default SupplierLeaderboard;
