import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  LayoutDashboard, 
  FileText, 
  Gavel, 
  Shield, 
  Trophy,
  Brain,
  Users,
  Crosshair as Target,
  Building,
  Settings,
  User
} from 'lucide-react';
import { cn } from '../../../../src/lib/utils';
import { useAuth } from '../../../../src/hooks/useAuth';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
  badge?: string;
  isNew?: boolean;
}

const MainNavigation: React.FC = () => {
  const location = useLocation();
  const { user } = useAuth();

  // Determine user type for conditional navigation
  const userType = user?.user_metadata?.user_type || 'bidder';

  const navigationItems: NavigationItem[] = [
    {
      name: 'Dashboard',
      href: '/',
      icon: LayoutDashboard,
      description: 'Overview and insights'
    },
    {
      name: 'Tenders',
      href: '/tenders',
      icon: FileText,
      description: 'Browse and manage tenders'
    },
    {
      name: 'Bids',
      href: '/bids',
      icon: Gavel,
      description: 'Your bidding activity'
    },
    {
      name: 'Compliance',
      href: '/compliance',
      icon: Shield,
      description: 'Compliance management'
    },
    {
      name: 'Gamification',
      href: '/gamification',
      icon: Trophy,
      description: 'Achievements and rewards'
    },
    
    // NEW: Psychological Systems (conditional based on user type)
    ...(userType === 'supplier' || userType === 'sales_rep' ? [{
      name: 'Sales Rep Centre',
      href: '/sales-rep-centre',
      icon: Brain,
      description: 'Psychological sales intelligence',
      badge: 'New',
      isNew: true
    }] : []),
    
    ...(userType === 'contractor' ? [{
      name: 'Supplier Access',
      href: '/contractor-supplier',
      icon: Users,
      description: 'Access supplier network',
      badge: 'New', 
      isNew: true
    }] : []),
    
    // Admin/Management items
    ...(user?.role === 'admin' ? [{
      name: 'User Management',
      href: '/admin/users',
      icon: User,
      description: 'Manage platform users'
    }] : [])
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(href);
  };

  return (
    <nav className="space-y-1">
      {navigationItems.map((item) => {
        const active = isActive(item.href);
        
        return (
          <Link
            key={item.name}
            to={item.href}
            className={cn(
              'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
              active
                ? 'bg-blue-100 text-blue-900 border-r-2 border-blue-600'
                : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
            )}
          >
            <item.icon
              className={cn(
                'mr-3 h-5 w-5 flex-shrink-0',
                active ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-500'
              )}
            />
            
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <span>{item.name}</span>
                {item.badge && (
                  <span className={cn(
                    'ml-2 px-2 py-0.5 text-xs font-medium rounded-full',
                    item.isNew 
                      ? 'bg-green-100 text-green-800'
                      : 'bg-blue-100 text-blue-800'
                  )}>
                    {item.badge}
                  </span>
                )}
              </div>
              
              {item.description && (
                <p className="text-xs text-gray-500 mt-0.5">
                  {item.description}
                </p>
              )}
            </div>
          </Link>
        );
      })}
    </nav>
  );
};

export default MainNavigation;
