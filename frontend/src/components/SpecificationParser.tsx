import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import {
  <PERSON>,
  CardContent,
  <PERSON>ton,
  LinearProgress,
  <PERSON>,
  Tabs,
  Tab,
  Alert,
  Box,
  Typography,
  Grid,
  Container,
  Paper,
  Stack,
  Checkbox,
  FormControlLabel,
  Divider
} from '@mui/material';
import {
  Upload,
  Description as <PERSON><PERSON><PERSON><PERSON>,
  CheckCircle,
  Error as <PERSON>ertCircle,
  Refresh as Loader2,
  Visibility as Eye,
  Download,
  Settings
} from '@mui/icons-material';

interface ParsedSpecification {
  spec_id: string;
  parsing_status: string;
  confidence_score: number;
  project_metadata: {
    project_name?: string;
    tender_number?: string;
    date?: string;
  };
  province: string;
  boq_items_count: number;
  sow_requirements_count: number;
  standards_count: number;
  processing_time_ms: number;
}

interface BOQItem {
  line_number: number;
  quantity?: number;
  unit: string;
  description: string;
  unit_price?: number;
  total_price?: number;
  category?: string;
  confidence_score: number;
}

interface SOWRequirement {
  category: string;
  requirement_type: string;
  description: string;
  priority: string;
  compliance_level: string;
}

interface SpecificationStandard {
  standard_type: string;
  standard_code: string;
  standard_title?: string;
  compliance_level: string;
  equivalents_allowed: boolean;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const SpecificationParser: React.FC = () => {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [parsing, setParsing] = useState(false);
  const [parsedSpec, setParsedSpec] = useState<ParsedSpecification | null>(null);
  const [specDetails, setSpecDetails] = useState<{
    boq_items: BOQItem[];
    sow_requirements: SOWRequirement[];
    standards: SpecificationStandard[];
  } | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [parseOptions, setParseOptions] = useState({
    extract_boq: true,
    extract_sow: true,
    extract_standards: true,
    document_type: 'pdf'
  });
  const [tabValue, setTabValue] = useState(0);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      setUploadedFile(file);
      setError(null);
      
      // Auto-detect document type
      const extension = file.name.split('.').pop()?.toLowerCase();
      if (extension === 'pdf') {
        setParseOptions(prev => ({ ...prev, document_type: 'pdf' }));
      } else if (extension === 'docx') {
        setParseOptions(prev => ({ ...prev, document_type: 'docx' }));
      } else if (extension === 'txt') {
        setParseOptions(prev => ({ ...prev, document_type: 'text' }));
      }
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt']
    },
    maxFiles: 1,
    maxSize: 50 * 1024 * 1024 // 50MB
  });

  const parseDocument = async () => {
    if (!uploadedFile) return;

    setParsing(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('file', uploadedFile);
      formData.append('document_type', parseOptions.document_type);
      formData.append('extract_boq', parseOptions.extract_boq.toString());
      formData.append('extract_sow', parseOptions.extract_sow.toString());
      formData.append('extract_standards', parseOptions.extract_standards.toString());

      const response = await fetch('/api/specifications/parse', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to parse document');
      }

      const result: ParsedSpecification = await response.json();
      setParsedSpec(result);

      // Poll for completion if processing
      if (result.parsing_status === 'processing') {
        pollParsingStatus(result.spec_id);
      } else {
        await fetchSpecificationDetails(result.spec_id);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setParsing(false);
    }
  };

  const pollParsingStatus = async (specId: string) => {
    const maxAttempts = 30; // 30 seconds max
    let attempts = 0;

    const poll = async () => {
      try {
        const response = await fetch(`/api/specifications/${specId}/status`);
        const status = await response.json();

        if (status.parsing_status === 'completed') {
          await fetchSpecificationDetails(specId);
          setParsedSpec(prev => prev ? { ...prev, ...status } : null);
        } else if (status.parsing_status === 'failed') {
          setError('Document parsing failed');
        } else if (attempts < maxAttempts) {
          attempts++;
          setTimeout(poll, 1000);
        } else {
          setError('Parsing timeout - please try again');
        }
      } catch (err) {
        setError('Failed to check parsing status');
      }
    };

    poll();
  };

  const fetchSpecificationDetails = async (specId: string) => {
    try {
      const response = await fetch(`/api/specifications/${specId}`);
      const details = await response.json();
      setSpecDetails({
        boq_items: details.boq_items || [],
        sow_requirements: details.sow_requirements || [],
        standards: details.standards || []
      });
    } catch (err) {
      console.error('Failed to fetch specification details:', err);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'processing': return 'info';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const getConfidenceColor = (score: number) => {
    if (score >= 0.8) return 'success.main';
    if (score >= 0.6) return 'warning.main';
    return 'error.main';
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <Stack spacing={3}>
        <Box textAlign="center">
          <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
            Tender Document Parser
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Upload tender documents to extract specifications, BOQ items, and compliance requirements
          </Typography>
        </Box>

        {/* File Upload */}
        <Card>
          <CardContent>
            <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 2 }}>
              <Upload />
              <Typography variant="h6">Document Upload</Typography>
            </Stack>
            
            <Paper
              {...getRootProps()}
              sx={{
                border: 2,
                borderStyle: 'dashed',
                borderColor: isDragActive ? 'primary.main' : 'grey.300',
                bgcolor: isDragActive ? 'primary.light' : 'transparent',
                p: 4,
                textAlign: 'center',
                cursor: 'pointer',
                transition: 'all 0.3s',
                '&:hover': {
                  borderColor: 'grey.400'
                }
              }}
            >
              <input {...getInputProps()} />
              <FileText sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
              {uploadedFile ? (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    {uploadedFile.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                  </Typography>
                </Box>
              ) : (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Drop your tender document here, or click to browse
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Supports PDF, DOCX, and TXT files up to 50MB
                  </Typography>
                </Box>
              )}
            </Paper>

            {uploadedFile && (
              <Box sx={{ mt: 2 }}>
                <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between">
                  <Stack direction="row" spacing={2}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={parseOptions.extract_boq}
                          onChange={(e) => setParseOptions(prev => ({ 
                            ...prev, 
                            extract_boq: e.target.checked 
                          }))}
                        />
                      }
                      label="Extract BOQ"
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={parseOptions.extract_sow}
                          onChange={(e) => setParseOptions(prev => ({ 
                            ...prev, 
                            extract_sow: e.target.checked 
                          }))}
                        />
                      }
                      label="Extract SOW"
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={parseOptions.extract_standards}
                          onChange={(e) => setParseOptions(prev => ({ 
                            ...prev, 
                            extract_standards: e.target.checked 
                          }))}
                        />
                      }
                      label="Extract Standards"
                    />
                  </Stack>
                  <Button 
                    variant="contained"
                    onClick={parseDocument} 
                    disabled={parsing}
                    startIcon={parsing ? <Loader2 /> : <Settings />}
                  >
                    {parsing ? 'Parsing...' : 'Parse Document'}
                  </Button>
                </Stack>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Alert severity="error" icon={<AlertCircle />}>
            {error}
          </Alert>
        )}

        {/* Parsing Results */}
        {parsedSpec && (
          <Card>
            <CardContent>
              <Stack direction="row" spacing={1} alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
                <Stack direction="row" spacing={1} alignItems="center">
                  <CheckCircle sx={{ color: 'success.main' }} />
                  <Typography variant="h6">Parsing Results</Typography>
                </Stack>
                <Chip 
                  label={parsedSpec.parsing_status} 
                  color={getStatusColor(parsedSpec.parsing_status) as any}
                />
              </Stack>

              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6} md={3} textAlign="center">
                  <Typography variant="h4" color="primary.main" fontWeight="bold">
                    {parsedSpec.boq_items_count}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">BOQ Items</Typography>
                </Grid>
                <Grid item xs={6} md={3} textAlign="center">
                  <Typography variant="h4" color="success.main" fontWeight="bold">
                    {parsedSpec.sow_requirements_count}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">SOW Requirements</Typography>
                </Grid>
                <Grid item xs={6} md={3} textAlign="center">
                  <Typography variant="h4" color="secondary.main" fontWeight="bold">
                    {parsedSpec.standards_count}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">Standards</Typography>
                </Grid>
                <Grid item xs={6} md={3} textAlign="center">
                  <Typography 
                    variant="h4" 
                    fontWeight="bold"
                    sx={{ color: getConfidenceColor(parsedSpec.confidence_score) }}
                  >
                    {(parsedSpec.confidence_score * 100).toFixed(0)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">Confidence</Typography>
                </Grid>
              </Grid>

              {/* Project Metadata */}
              {parsedSpec.project_metadata && (
                <Paper sx={{ bgcolor: 'grey.50', p: 2, mb: 2 }}>
                  <Typography variant="h6" gutterBottom>Project Information</Typography>
                  <Grid container spacing={2}>
                    {parsedSpec.project_metadata.project_name && (
                      <Grid item xs={12} md={4}>
                        <Typography variant="body2">
                          <strong>Project:</strong> {parsedSpec.project_metadata.project_name}
                        </Typography>
                      </Grid>
                    )}
                    {parsedSpec.project_metadata.tender_number && (
                      <Grid item xs={12} md={4}>
                        <Typography variant="body2">
                          <strong>Tender #:</strong> {parsedSpec.project_metadata.tender_number}
                        </Typography>
                      </Grid>
                    )}
                    <Grid item xs={12} md={4}>
                      <Typography variant="body2">
                        <strong>Province:</strong> {parsedSpec.province}
                      </Typography>
                    </Grid>
                  </Grid>
                </Paper>
              )}

              <LinearProgress 
                variant="determinate" 
                value={parsedSpec.confidence_score * 100} 
                sx={{ mb: 2, height: 8, borderRadius: 4 }}
              />
              
              <Stack direction="row" spacing={1}>
                <Button 
                  variant="outlined" 
                  size="small"
                  onClick={() => fetchSpecificationDetails(parsedSpec.spec_id)}
                  startIcon={<Eye />}
                >
                  View Details
                </Button>
                <Button 
                  variant="outlined" 
                  size="small"
                  startIcon={<Download />}
                >
                  Export
                </Button>
              </Stack>
            </CardContent>
          </Card>
        )}

        {/* Detailed Results */}
        {specDetails && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Extracted Specifications</Typography>
              
              <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs value={tabValue} onChange={handleTabChange}>
                  <Tab label={`BOQ Items (${specDetails.boq_items.length})`} />
                  <Tab label={`SOW Requirements (${specDetails.sow_requirements.length})`} />
                  <Tab label={`Standards (${specDetails.standards.length})`} />
                </Tabs>
              </Box>

              <CustomTabPanel value={tabValue} index={0}>
                <Stack spacing={2}>
                  {specDetails.boq_items.map((item, index) => (
                    <Paper key={index} sx={{ p: 2, border: 1, borderColor: 'grey.300' }}>
                      <Stack direction="row" justifyContent="space-between" alignItems="flex-start" sx={{ mb: 1 }}>
                        <Typography variant="h6">Line {item.line_number}</Typography>
                        <Chip 
                          label={`${(item.confidence_score * 100).toFixed(0)}% confidence`}
                          variant="outlined"
                          size="small"
                        />
                      </Stack>
                      <Typography color="text.secondary" sx={{ mb: 1 }}>
                        {item.description}
                      </Typography>
                      <Grid container spacing={1}>
                        {item.quantity && (
                          <Grid item xs={6} md={3}>
                            <Typography variant="body2">
                              <strong>Qty:</strong> {item.quantity}
                            </Typography>
                          </Grid>
                        )}
                        <Grid item xs={6} md={3}>
                          <Typography variant="body2">
                            <strong>Unit:</strong> {item.unit}
                          </Typography>
                        </Grid>
                        {item.unit_price && (
                          <Grid item xs={6} md={3}>
                            <Typography variant="body2">
                              <strong>Unit Price:</strong> R{item.unit_price}
                            </Typography>
                          </Grid>
                        )}
                        {item.category && (
                          <Grid item xs={6} md={3}>
                            <Typography variant="body2">
                              <strong>Category:</strong> {item.category}
                            </Typography>
                          </Grid>
                        )}
                      </Grid>
                    </Paper>
                  ))}
                </Stack>
              </CustomTabPanel>

              <CustomTabPanel value={tabValue} index={1}>
                <Stack spacing={2}>
                  {specDetails.sow_requirements.map((req, index) => (
                    <Paper key={index} sx={{ p: 2, border: 1, borderColor: 'grey.300' }}>
                      <Stack direction="row" justifyContent="space-between" alignItems="flex-start" sx={{ mb: 1 }}>
                        <Typography variant="h6">{req.category}</Typography>
                        <Stack direction="row" spacing={1}>
                          <Chip label={req.priority} variant="outlined" size="small" />
                          <Chip 
                            label={req.compliance_level}
                            color={req.compliance_level === 'mandatory' ? 'primary' : 'default'}
                            size="small"
                          />
                        </Stack>
                      </Stack>
                      <Typography color="text.secondary">{req.description}</Typography>
                    </Paper>
                  ))}
                </Stack>
              </CustomTabPanel>

              <CustomTabPanel value={tabValue} index={2}>
                <Stack spacing={2}>
                  {specDetails.standards.map((standard, index) => (
                    <Paper key={index} sx={{ p: 2, border: 1, borderColor: 'grey.300' }}>
                      <Stack direction="row" justifyContent="space-between" alignItems="flex-start" sx={{ mb: 1 }}>
                        <Typography variant="h6">
                          {standard.standard_type} {standard.standard_code}
                        </Typography>
                        <Stack direction="row" spacing={1}>
                          <Chip 
                            label={standard.compliance_level}
                            color={standard.compliance_level === 'mandatory' ? 'primary' : 'default'}
                            size="small"
                          />
                          {standard.equivalents_allowed && (
                            <Chip label="Equivalents OK" variant="outlined" size="small" />
                          )}
                        </Stack>
                      </Stack>
                      {standard.standard_title && (
                        <Typography color="text.secondary">{standard.standard_title}</Typography>
                      )}
                    </Paper>
                  ))}
                </Stack>
              </CustomTabPanel>
            </CardContent>
          </Card>
        )}
      </Stack>
    </Container>
  );
};

export default SpecificationParser;
