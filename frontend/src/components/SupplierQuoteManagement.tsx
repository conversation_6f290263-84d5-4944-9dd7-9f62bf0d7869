import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Chip,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Dialog,
  DialogContent,
  DialogTitle,
  Alert,
  Grid,
  Checkbox,
  FormControlLabel,
  CircularProgress,
  IconButton,
  Divider,
  Paper,
  DialogActions
} from '@mui/material';
import {
  AttachMoney,
  Description,
  Schedule,
  EmojiEvents,
  TrendingUp,
  Add,
  Edit,
  Visibility,
  Download,
  CheckCircle,
  Warning,
  CalendarToday,
  Calculate,
  Link as BlockchainIcon,
  Psychology,
  LocationOn,
  GpsFixed,
  FlashOn
} from '@mui/icons-material';

interface Quote {
  id: string;
  quote_id: string;
  tender_id: string;
  supplier_id: string;
  rfq_id?: string;
  quote_number?: string;
  total_amount: number;
  currency: string;
  line_items?: LineItem[];
  delivery_timeframe?: string;
  delivery_cost: number;
  delivery_location?: string;
  validity_period_days: number;
  payment_terms?: string;
  warranty_terms?: string;
  compliance_documents?: string[];
  certifications?: string[];
  bbbee_certificate_url?: string;
  tax_clearance_url?: string;
  status: 'draft' | 'submitted' | 'under_review' | 'accepted' | 'rejected' | 'expired' | 'withdrawn';
  submission_date?: string;
  review_date?: string;
  decision_date?: string;
  expiry_date?: string;
  evaluation_score?: number;
  technical_score?: number;
  commercial_score?: number;
  compliance_score?: number;
  evaluation_notes?: string;
  evaluator_id?: string;
  trust_score: number;
  competitive_score: number;
  ai_analysis?: Record<string, any>;
  commission_rate: number;
  estimated_commission: number;
  commission_status: 'pending' | 'calculated' | 'approved' | 'paid';
  smart_contract_enabled: boolean;
  smart_contract_address?: string;
  blockchain_hash?: string;
  quote_document_url?: string;
  supporting_documents?: QuoteDocument[];
  supplier_name?: string;
  supplier_location?: string;
  supplier_bbbee_level?: number;
  view_count: number;
  download_count: number;
  version: number;
  parent_quote_id?: string;
  is_latest_version: boolean;
  notes?: string;
  internal_notes?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

interface LineItem {
  id?: string;
  description: string;
  quantity: number;
  unit_price: number;
  total: number;
  unit_of_measurement?: string;
  specifications?: Record<string, any>;
}

interface QuoteDocument {
  id: string;
  name: string;
  type: string;
  url: string;
  size?: number;
}

interface QuoteFormData {
  tender_id: string;
  total_amount: string;
  currency: string;
  delivery_timeframe: string;
  validity_period_days: number;
  line_items: Array<{
    description: string;
    quantity: number;
    unit_price: number;
    total: number;
  }>;
  terms_and_conditions: string;
  payment_terms: string;
  warranty_terms: string;
  smart_contract_enabled: boolean;
}

interface CommissionCalculation {
  quote_amount: number;
  commission_rate: number;
  commission_amount: number;
  platform_fee: number;
  supplier_net: number;
  payment_schedule: {
    on_award: number;
    on_delivery: number;
    on_completion: number;
  };
}

interface TenderIntelligence {
  id: string;
  title: string;
  value: number;
  location: string;
  urgency: 'critical' | 'high' | 'medium' | 'low';
  matchScore: number;
  requirements: string[];
  psychTrigger: string;
  rfqCount: number;
  competitorCount: number;
  estimatedRevenue: number;
}

const SupplierQuoteManagement: React.FC = () => {
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [loading, setLoading] = useState(false);
  const [showQuoteForm, setShowQuoteForm] = useState(false);
  const [selectedQuote, setSelectedQuote] = useState<Quote | null>(null);
  const [commissionCalc, setCommissionCalc] = useState<CommissionCalculation | null>(null);
  const [tenderIntelligence, setTenderIntelligence] = useState<TenderIntelligence[]>([
    {
      id: 'tender-001',
      title: 'Municipal Infrastructure - R15.6M',
      value: 15600000,
      location: 'Johannesburg, Gauteng (25km)',
      urgency: 'critical',
      matchScore: 92,
      requirements: ['Construction Materials', 'Steel Supplies', 'Concrete'],
      psychTrigger: 'RFQ EXPLOSION: 12 new RFQs for your products this week!',
      rfqCount: 12,
      competitorCount: 8,
      estimatedRevenue: 1250000
    },
    {
      id: 'tender-002',
      title: 'Office Building Construction - R8.5M',
      value: 8500000,
      location: 'Pretoria, Gauteng (45km)',
      urgency: 'high',
      matchScore: 87,
      requirements: ['Office Supplies', 'Furniture', 'IT Equipment'],
      psychTrigger: 'VOLUME OPPORTUNITY: Large order potential from multiple bidders!',
      rfqCount: 8,
      competitorCount: 12,
      estimatedRevenue: 780000
    }
  ]);
  const [formData, setFormData] = useState<QuoteFormData>({
    tender_id: '',
    total_amount: '',
    currency: 'ZAR',
    delivery_timeframe: '',
    validity_period_days: 30,
    line_items: [{ description: '', quantity: 1, unit_price: 0, total: 0 }],
    terms_and_conditions: '',
    payment_terms: '',
    warranty_terms: '',
    smart_contract_enabled: false
  });

  useEffect(() => {
    loadQuotes();
  }, []);

  const loadQuotes = async () => {
    setLoading(true);
    try {
      // Get current user's supplier ID (would come from auth context)
      const supplierId = 'current-supplier-id'; // Replace with actual auth
      
      const response = await fetch(`/api/quotes/supplier/${supplierId}`);
      if (response.ok) {
        const data = await response.json();
        setQuotes(data.quotes || []);
      }
    } catch (err) {
      console.error('Failed to load quotes:', err);
    } finally {
      setLoading(false);
    }
  };

  const submitQuote = async () => {
    try {
      setLoading(true);
      
      const quoteData = {
        tender_id: formData.tender_id,
        supplier_id: 'current-supplier-id', // Replace with actual auth
        total_amount: parseFloat(formData.total_amount),
        currency: formData.currency,
        delivery_timeframe: formData.delivery_timeframe,
        validity_period_days: formData.validity_period_days,
        line_items: formData.line_items,
        terms_and_conditions: formData.terms_and_conditions,
        payment_terms: formData.payment_terms,
        warranty_terms: formData.warranty_terms,
        smart_contract_enabled: formData.smart_contract_enabled,
        compliance_documents: []
      };

      const response = await fetch('/api/quotes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(quoteData),
      });

      if (response.ok) {
        const newQuote = await response.json();
        setQuotes(prev => [newQuote, ...prev]);
        setShowQuoteForm(false);
        resetForm();
      } else {
        throw new Error('Failed to submit quote');
      }
    } catch (err) {
      console.error('Error submitting quote:', err);
    } finally {
      setLoading(false);
    }
  };

  const calculateCommission = async (quoteId: string) => {
    try {
      const response = await fetch(`/api/quotes/${quoteId}/calculate-commission`, {
        method: 'POST'
      });
      
      if (response.ok) {
        const calc = await response.json();
        setCommissionCalc(calc);
      }
    } catch (err) {
      console.error('Error calculating commission:', err);
    }
  };

  const resetForm = () => {
    setFormData({
      tender_id: '',
      total_amount: '',
      currency: 'ZAR',
      delivery_timeframe: '',
      validity_period_days: 30,
      line_items: [{ description: '', quantity: 1, unit_price: 0, total: 0 }],
      terms_and_conditions: '',
      payment_terms: '',
      warranty_terms: '',
      smart_contract_enabled: false
    });
  };

  const addLineItem = () => {
    setFormData(prev => ({
      ...prev,
      line_items: [...prev.line_items, { description: '', quantity: 1, unit_price: 0, total: 0 }]
    }));
  };

  const updateLineItem = (index: number, field: string, value: any) => {
    setFormData(prev => {
      const newItems = [...prev.line_items];
      newItems[index] = { ...newItems[index], [field]: value };
      
      // Recalculate total for this line item
      if (field === 'quantity' || field === 'unit_price') {
        newItems[index].total = newItems[index].quantity * newItems[index].unit_price;
      }
      
      return { ...prev, line_items: newItems };
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accepted': return 'success';
      case 'submitted': return 'primary';
      case 'rejected': return 'error';
      case 'expired': return 'default';
      default: return 'warning';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'accepted': return <CheckCircle />;
      case 'submitted': return <Schedule />;
      case 'rejected': return <Warning />;
      default: return <Description />;
    }
  };

  const getTrustScoreColor = (score: number) => {
    if (score >= 0.8) return 'success.main';
    if (score >= 0.6) return 'warning.main';
    return 'error.main';
  };

  const totalQuoteValue = formData.line_items.reduce((sum, item) => sum + item.total, 0);

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `R${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `R${(value / 1000).toFixed(0)}k`;
    return `R${value}`;
  };

  const stats = {
    totalQuotes: quotes.length,
    totalValue: quotes.reduce((sum, q) => sum + q.total_amount, 0),
    avgQuoteValue: quotes.length > 0 ? quotes.reduce((sum, q) => sum + q.total_amount, 0) / quotes.length : 0,
    acceptedQuotes: quotes.filter(q => q.status === 'accepted').length,
    pendingQuotes: quotes.filter(q => q.status === 'submitted').length,
    totalCommissions: quotes.reduce((sum, q) => sum + q.estimated_commission, 0)
  };

  return (
    <Box sx={{ maxWidth: '1200px', mx: 'auto', p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h3" component="h1" fontWeight="bold">
            🧠 Supplier Intelligence Dashboard
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Tender-driven quote opportunities and performance tracking
          </Typography>
        </Box>
        <Button
          variant="contained"
          onClick={() => setShowQuoteForm(true)}
          startIcon={<Add />}
        >
          Submit Quote
        </Button>
      </Box>

      {/* Tender Intelligence Section */}
      <Card sx={{ mb: 3, border: 2, borderColor: 'primary.light', background: 'linear-gradient(45deg, #e3f2fd, #f3e5f5)' }}>
        <CardHeader>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Psychology color="primary" />
            Tender Intelligence - RFQ Opportunities
          </Typography>
        </CardHeader>
        <CardContent>
          <Grid container spacing={2}>
            {tenderIntelligence.map((tender) => (
              <Grid item xs={12} lg={6} key={tender.id}>
                <Card sx={{ 
                  border: 2, 
                  borderColor: tender.urgency === 'critical' ? 'error.light' : 'primary.light',
                  bgcolor: tender.urgency === 'critical' ? 'error.50' : 'primary.50'
                }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="h6" fontWeight="bold">
                          {tender.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                          <LocationOn fontSize="small" />
                          {tender.location}
                        </Typography>
                        <Typography variant="body2" color="primary" fontWeight="bold" sx={{ mt: 1 }}>
                          {tender.psychTrigger}
                        </Typography>
                      </Box>
                      <Box sx={{ textAlign: 'right' }}>
                        <Chip 
                          label={tender.urgency.toUpperCase()}
                          color={getUrgencyColor(tender.urgency) as any}
                          size="small"
                          sx={{ mb: 1 }}
                        />
                        <Typography variant="body2" fontWeight="bold" color="success.main">
                          {tender.matchScore}% MATCH
                        </Typography>
                      </Box>
                    </Box>

                    <Grid container spacing={2} sx={{ mb: 2 }}>
                      <Grid item xs={6}>
                        <Typography variant="caption" color="text.secondary">Tender Value</Typography>
                        <Typography variant="body2" fontWeight="bold" color="success.main">
                          {formatCurrency(tender.value)}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="caption" color="text.secondary">Est. Revenue</Typography>
                        <Typography variant="body2" fontWeight="bold" color="success.main">
                          {formatCurrency(tender.estimatedRevenue)}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="caption" color="text.secondary">RFQs Available</Typography>
                        <Typography variant="body2" fontWeight="bold" color="primary">
                          {tender.rfqCount} RFQs
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="caption" color="text.secondary">Competitors</Typography>
                        <Typography variant="body2" fontWeight="bold" color="warning.main">
                          {tender.competitorCount} suppliers
                        </Typography>
                      </Grid>
                    </Grid>

                    <Box sx={{ mb: 2 }}>
                      <Typography variant="caption" color="text.secondary">Requirements Match:</Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                        {tender.requirements.map((req, index) => (
                          <Chip key={index} label={req} variant="outlined" size="small" />
                        ))}
                      </Box>
                    </Box>

                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        size="small"
                        variant="outlined"
                        sx={{ flexGrow: 1 }}
                        startIcon={<Visibility />}
                        onClick={() => {
                          setFormData(prev => ({ ...prev, tender_id: tender.id }));
                          setShowQuoteForm(true);
                        }}
                      >
                        View RFQs
                      </Button>
                      <Button
                        size="small"
                        variant="contained"
                        color={tender.urgency === 'critical' ? 'error' : 'primary'}
                        sx={{ flexGrow: 1 }}
                        startIcon={<FlashOn />}
                        onClick={() => {
                          setFormData(prev => ({ ...prev, tender_id: tender.id }));
                          setShowQuoteForm(true);
                        }}
                      >
                        {tender.urgency === 'critical' ? 'QUOTE NOW!' : 'Create Quote'}
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          <Alert severity="success" sx={{ mt: 2 }} icon={<GpsFixed />}>
            <Typography variant="body2">
              <strong>🎯 SUPPLIER SUCCESS:</strong> Tender-driven intelligence increases quote success by 220%!
              RFQ opportunities matched to your product categories and location.
            </Typography>
          </Alert>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="body2" color="text.secondary">Total Quotes</Typography>
                  <Typography variant="h4" fontWeight="bold">{stats.totalQuotes}</Typography>
                </Box>
                <Description color="primary" sx={{ fontSize: 32 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={2}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="body2" color="text.secondary">Total Value</Typography>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    R{stats.totalValue.toLocaleString()}
                  </Typography>
                </Box>
                <AttachMoney color="success" sx={{ fontSize: 32 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={2}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="body2" color="text.secondary">Avg Quote</Typography>
                  <Typography variant="h4" fontWeight="bold" color="secondary">
                    R{stats.avgQuoteValue.toLocaleString()}
                  </Typography>
                </Box>
                <Calculate color="secondary" sx={{ fontSize: 32 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={2}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="body2" color="text.secondary">Accepted</Typography>
                  <Typography variant="h4" fontWeight="bold" color="success.main">{stats.acceptedQuotes}</Typography>
                </Box>
                <EmojiEvents color="success" sx={{ fontSize: 32 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={2}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="body2" color="text.secondary">Pending</Typography>
                  <Typography variant="h4" fontWeight="bold" color="primary">{stats.pendingQuotes}</Typography>
                </Box>
                <Schedule color="primary" sx={{ fontSize: 32 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={2}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="body2" color="text.secondary">Commissions</Typography>
                  <Typography variant="h4" fontWeight="bold" color="warning.main">
                    R{stats.totalCommissions.toLocaleString()}
                  </Typography>
                </Box>
                <TrendingUp color="warning" sx={{ fontSize: 32 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Quote List */}
      <Card>
        <CardHeader>
          <Typography variant="h6">Your Quotes</Typography>
        </CardHeader>
        <CardContent>
          {loading ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <CircularProgress />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                Loading quotes...
              </Typography>
            </Box>
          ) : quotes.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Description sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
              <Typography variant="h6" gutterBottom>No Quotes Yet</Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Start by submitting your first quote
              </Typography>
              <Button variant="contained" onClick={() => setShowQuoteForm(true)} startIcon={<Add />}>
                Submit Quote
              </Button>
            </Box>
          ) : (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {quotes.map((quote) => (
                <Paper key={quote.quote_id} sx={{ p: 2, border: 1, borderColor: 'divider' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box>
                      <Typography variant="h6">Tender: {quote.tender_id}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Submitted: {quote.submission_date ? new Date(quote.submission_date).toLocaleDateString() : 'N/A'}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Chip 
                        icon={getStatusIcon(quote.status)}
                        label={quote.status}
                        color={getStatusColor(quote.status) as any}
                        size="small"
                      />
                      {quote.smart_contract_address && (
                        <Chip 
                          icon={<BlockchainIcon />}
                          label="Smart Contract"
                          variant="outlined"
                          size="small"
                          color="secondary"
                        />
                      )}
                    </Box>
                  </Box>

                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={12} md={3}>
                      <Typography variant="body2" color="text.secondary">Quote Amount:</Typography>
                      <Typography variant="h6" color="success.main">
                        {quote.currency} {quote.total_amount.toLocaleString()}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <Typography variant="body2" color="text.secondary">Commission:</Typography>
                      <Typography variant="h6" color="warning.main">
                        R{quote.estimated_commission.toLocaleString()}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <Typography variant="body2" color="text.secondary">Trust Score:</Typography>
                      <Typography variant="h6" sx={{ color: getTrustScoreColor(quote.trust_score) }}>
                        {(quote.trust_score * 100).toFixed(0)}%
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <Typography variant="body2" color="text.secondary">Expires:</Typography>
                      <Typography variant="body2">
                        {quote.expiry_date ? new Date(quote.expiry_date).toLocaleDateString() : 'N/A'}
                      </Typography>
                    </Grid>
                  </Grid>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Chip label={`Delivery: ${quote.delivery_timeframe}`} variant="outlined" size="small" />
                    </Box>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button 
                        variant="outlined" 
                        size="small"
                        startIcon={<Calculate />}
                        onClick={() => calculateCommission(quote.quote_id)}
                      >
                        Commission
                      </Button>
                      <Button variant="outlined" size="small" startIcon={<Visibility />}>
                        View
                      </Button>
                      <Button variant="outlined" size="small" startIcon={<Edit />}>
                        Edit
                      </Button>
                    </Box>
                  </Box>
                </Paper>
              ))}
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Quote Submission Dialog */}
      <Dialog open={showQuoteForm} onClose={() => setShowQuoteForm(false)} maxWidth="md" fullWidth>
        <DialogTitle>Submit New Quote</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 2 }}>
            {/* Basic Information */}
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Tender ID"
                  value={formData.tender_id}
                  onChange={(e) => setFormData(prev => ({ ...prev, tender_id: e.target.value }))}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Currency</InputLabel>
                  <Select
                    value={formData.currency}
                    label="Currency"
                    onChange={(e) => setFormData(prev => ({ ...prev, currency: e.target.value }))}
                  >
                    <MenuItem value="ZAR">ZAR (South African Rand)</MenuItem>
                    <MenuItem value="USD">USD (US Dollar)</MenuItem>
                    <MenuItem value="EUR">EUR (Euro)</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Delivery Timeframe"
                  value={formData.delivery_timeframe}
                  onChange={(e) => setFormData(prev => ({ ...prev, delivery_timeframe: e.target.value }))}
                  placeholder="e.g., 30 days"
                  fullWidth
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Validity Period (Days)"
                  type="number"
                  value={formData.validity_period_days}
                  onChange={(e) => setFormData(prev => ({ ...prev, validity_period_days: parseInt(e.target.value) }))}
                  fullWidth
                />
              </Grid>
            </Grid>

            {/* Line Items */}
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Line Items</Typography>
                <Button variant="outlined" size="small" onClick={addLineItem} startIcon={<Add />}>
                  Add Item
                </Button>
              </Box>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {formData.line_items.map((item, index) => (
                  <Paper key={index} sx={{ p: 2, border: 1, borderColor: 'divider' }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={4}>
                        <TextField
                          label="Description"
                          value={item.description}
                          onChange={(e) => updateLineItem(index, 'description', e.target.value)}
                          fullWidth
                          size="small"
                        />
                      </Grid>
                      <Grid item xs={12} md={2}>
                        <TextField
                          label="Quantity"
                          type="number"
                          value={item.quantity}
                          onChange={(e) => updateLineItem(index, 'quantity', parseFloat(e.target.value) || 0)}
                          fullWidth
                          size="small"
                        />
                      </Grid>
                      <Grid item xs={12} md={3}>
                        <TextField
                          label="Unit Price"
                          type="number"
                          value={item.unit_price}
                          onChange={(e) => updateLineItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                          fullWidth
                          size="small"
                        />
                      </Grid>
                      <Grid item xs={12} md={3}>
                        <Typography variant="h6" sx={{ pt: 1 }}>
                          R{item.total.toLocaleString()}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Paper>
                ))}
              </Box>
              
              <Box sx={{ textAlign: 'right', mt: 2 }}>
                <Typography variant="h6">
                  Total: {formData.currency} {totalQuoteValue.toLocaleString()}
                </Typography>
              </Box>
            </Box>

            {/* Terms and Conditions */}
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Payment Terms"
                  value={formData.payment_terms}
                  onChange={(e) => setFormData(prev => ({ ...prev, payment_terms: e.target.value }))}
                  multiline
                  rows={3}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Warranty Terms"
                  value={formData.warranty_terms}
                  onChange={(e) => setFormData(prev => ({ ...prev, warranty_terms: e.target.value }))}
                  multiline
                  rows={3}
                  fullWidth
                />
              </Grid>
            </Grid>

            <TextField
              label="Terms and Conditions"
              value={formData.terms_and_conditions}
              onChange={(e) => setFormData(prev => ({ ...prev, terms_and_conditions: e.target.value }))}
              multiline
              rows={4}
              fullWidth
            />

            {/* Smart Contract Option */}
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.smart_contract_enabled}
                  onChange={(e) => setFormData(prev => ({ ...prev, smart_contract_enabled: e.target.checked }))}
                />
              }
              label="Enable Smart Contract (Blockchain execution)"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowQuoteForm(false)}>Cancel</Button>
          <Button onClick={submitQuote} disabled={loading} variant="contained">
            {loading ? 'Submitting...' : 'Submit Quote'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Commission Calculator Dialog */}
      {commissionCalc && (
        <Dialog open={!!commissionCalc} onClose={() => setCommissionCalc(null)}>
          <DialogTitle>Commission Breakdown</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Quote Amount:</Typography>
                <Typography variant="h6">R{commissionCalc.quote_amount.toLocaleString()}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Commission Rate:</Typography>
                <Typography variant="h6">{(commissionCalc.commission_rate * 100).toFixed(2)}%</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Commission Amount:</Typography>
                <Typography variant="h6" color="warning.main">R{commissionCalc.commission_amount.toLocaleString()}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Your Net Amount:</Typography>
                <Typography variant="h6" color="success.main">R{commissionCalc.supplier_net.toLocaleString()}</Typography>
              </Grid>
            </Grid>

            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle1" gutterBottom>Payment Schedule</Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography>On Award (30%):</Typography>
                  <Typography fontWeight="medium">R{commissionCalc.payment_schedule.on_award.toLocaleString()}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography>On Delivery (50%):</Typography>
                  <Typography fontWeight="medium">R{commissionCalc.payment_schedule.on_delivery.toLocaleString()}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography>On Completion (20%):</Typography>
                  <Typography fontWeight="medium">R{commissionCalc.payment_schedule.on_completion.toLocaleString()}</Typography>
                </Box>
              </Box>
            </Box>
          </DialogContent>
        </Dialog>
      )}
    </Box>
  );
};

export default SupplierQuoteManagement;
