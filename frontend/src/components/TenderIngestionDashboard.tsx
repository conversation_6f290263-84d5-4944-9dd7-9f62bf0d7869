import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Chip,
  LinearProgress,
  Tabs,
  Tab,
  Dialog,
  DialogContent,
  DialogT<PERSON>le,
  Alert,
  Grid,
  IconButton,
  <PERSON>ge,
  Di<PERSON>r,
  Paper
} from '@mui/material';
import {
  Download,
  Refresh,
  Language,
  Description,
  Schedule,
  CheckCircle,
  Warning,
  Cancel,
  PlayArrow,
  Pause,
  Settings,
  Visibility,
  Storage,
  SmartToy,
  Timeline
} from '@mui/icons-material';

interface TenderSource {
  source_id: string;
  name: string;
  url: string;
  source_type: string;
  scraping_enabled: boolean;
  last_scraped?: string;
  scraping_frequency: number;
}

interface IngestionJob {
  job_id: string;
  source_id: string;
  status: string;
  started_at?: string;
  completed_at?: string;
  tenders_found: number;
  tenders_processed: number;
  documents_downloaded: number;
  errors: string[];
  queen_bee_assignments: string[];
}

interface ScrapedTender {
  external_id: string;
  title: string;
  organization: string;
  category: string;
  estimated_value?: number;
  location: string;
  closing_date: string;
  source_id: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const TenderIngestionDashboard: React.FC = () => {
  const [sources, setSources] = useState<TenderSource[]>([]);
  const [jobs, setJobs] = useState<IngestionJob[]>([]);
  const [selectedJob, setSelectedJob] = useState<IngestionJob | null>(null);
  const [testResults, setTestResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [tabValue, setTabValue] = useState(0);

  useEffect(() => {
    loadDashboardData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(() => {
      if (autoRefresh) {
        loadDashboardData();
      }
    }, 30000);
    
    return () => clearInterval(interval);
  }, [autoRefresh]);

  const loadDashboardData = async () => {
    try {
      // Load sources
      const sourcesResponse = await fetch('/api/ingestion/sources');
      if (sourcesResponse.ok) {
        const sourcesData = await sourcesResponse.json();
        setSources(sourcesData.sources || []);
      }

      // Load jobs
      const jobsResponse = await fetch('/api/ingestion/jobs');
      if (jobsResponse.ok) {
        const jobsData = await jobsResponse.json();
        setJobs(jobsData.jobs || []);
      }
    } catch (err) {
      console.error('Failed to load dashboard data:', err);
    }
  };

  const startIngestion = async (sourceIds?: string[]) => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/ingestion/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ source_ids: sourceIds }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Ingestion started:', result);
        
        // Refresh jobs list
        setTimeout(() => loadDashboardData(), 2000);
      } else {
        throw new Error('Failed to start ingestion');
      }
    } catch (err) {
      console.error('Error starting ingestion:', err);
    } finally {
      setLoading(false);
    }
  };

  const testScrapeSource = async (sourceId: string) => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/ingestion/test-scrape/${sourceId}`, {
        method: 'POST'
      });

      if (response.ok) {
        const result = await response.json();
        setTestResults(result);
      } else {
        throw new Error('Failed to test scraping');
      }
    } catch (err) {
      console.error('Error testing scrape:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'running': return 'info';
      case 'failed': return 'error';
      case 'pending': return 'warning';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle sx={{ fontSize: 16 }} />;
      case 'running': return <Refresh sx={{ fontSize: 16, animation: 'spin 1s linear infinite' }} />;
      case 'failed': return <Cancel sx={{ fontSize: 16 }} />;
      case 'pending': return <Schedule sx={{ fontSize: 16 }} />;
      default: return <Schedule sx={{ fontSize: 16 }} />;
    }
  };

  const getSourceTypeColor = (type: string) => {
    switch (type) {
      case 'government': return 'info';
      case 'municipal': return 'success';
      case 'parastatal': return 'secondary';
      default: return 'default';
    }
  };

  const formatDuration = (startTime: string, endTime?: string) => {
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const duration = Math.round((end.getTime() - start.getTime()) / 1000);
    
    if (duration < 60) return `${duration}s`;
    if (duration < 3600) return `${Math.round(duration / 60)}m`;
    return `${Math.round(duration / 3600)}h`;
  };

  const runningJobs = jobs.filter(job => job.status === 'running');
  const recentJobs = jobs.slice(0, 10);
  const totalTendersToday = jobs
    .filter(job => job.started_at && new Date(job.started_at).toDateString() === new Date().toDateString())
    .reduce((sum, job) => sum + job.tenders_processed, 0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box sx={{ maxWidth: '1200px', mx: 'auto', p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h3" component="h1" sx={{ fontWeight: 'bold', color: 'text.primary', mb: 1 }}>
            Tender Ingestion System
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Automated government tender scraping and Queen Bee AI integration
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button 
            variant="outlined" 
            onClick={() => setAutoRefresh(!autoRefresh)}
            sx={{ bgcolor: autoRefresh ? 'success.light' : 'transparent' }}
            startIcon={autoRefresh ? <Pause /> : <PlayArrow />}
          >
            Auto Refresh
          </Button>
          <Button variant="outlined" onClick={loadDashboardData} startIcon={<Refresh />}>
            Refresh
          </Button>
          <Button 
            variant="contained" 
            onClick={() => startIngestion()} 
            disabled={loading}
            startIcon={<Download />}
          >
            {loading ? 'Starting...' : 'Run Ingestion'}
          </Button>
        </Box>
      </Box>

      {/* Status Overview */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ pt: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                    Active Sources
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                    {sources.filter(s => s.scraping_enabled).length}
                  </Typography>
                </Box>
                <Language sx={{ fontSize: 32, color: 'info.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ pt: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                    Running Jobs
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                    {runningJobs.length}
                  </Typography>
                </Box>
                <Timeline sx={{ fontSize: 32, color: 'warning.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ pt: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                    Tenders Today
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                    {totalTendersToday}
                  </Typography>
                </Box>
                <Description sx={{ fontSize: 32, color: 'success.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ pt: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                    Queen Bee Tasks
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'secondary.main' }}>
                    {jobs.reduce((sum, job) => sum + job.queen_bee_assignments.length, 0)}
                  </Typography>
                </Box>
                <SmartToy sx={{ fontSize: 32, color: 'secondary.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Running Jobs Alert */}
      {runningJobs.length > 0 && (
        <Alert severity="info" sx={{ mb: 3 }} icon={<Timeline />}>
          {runningJobs.length} ingestion job(s) currently running. 
          {runningJobs.map(job => (
            <span key={job.job_id} style={{ marginLeft: 8 }}>
              {job.source_id} ({job.tenders_processed}/{job.tenders_found} processed)
            </span>
          ))}
        </Alert>
      )}

      <Box sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="ingestion dashboard tabs">
            <Tab label="Tender Sources" />
            <Tab label="Ingestion Jobs" />
            <Tab label="Monitoring" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={2}>
            {sources.map((source) => (
              <Grid item xs={12} md={6} lg={4} key={source.source_id}>
                <Card>
                  <CardHeader
                    title={
                      <Typography variant="h6" component="div">
                        {source.name}
                      </Typography>
                    }
                    subheader={
                      <Chip 
                        label={source.source_type} 
                        color={getSourceTypeColor(source.source_type) as any}
                        size="small"
                      />
                    }
                    action={
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        <IconButton 
                          size="small"
                          onClick={() => testScrapeSource(source.source_id)}
                          disabled={loading}
                        >
                          <Visibility />
                        </IconButton>
                        <IconButton 
                          size="small"
                          onClick={() => startIngestion([source.source_id])}
                          disabled={loading}
                        >
                          <Download />
                        </IconButton>
                      </Box>
                    }
                  />
                  <CardContent>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="body2">Status:</Typography>
                        <Chip 
                          label={source.scraping_enabled ? 'Enabled' : 'Disabled'}
                          color={source.scraping_enabled ? 'success' : 'default'}
                          size="small"
                        />
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="body2">Frequency:</Typography>
                        <Typography variant="body2">{source.scraping_frequency}min</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="body2">Last Scraped:</Typography>
                        <Typography variant="body2">
                          {source.last_scraped 
                            ? new Date(source.last_scraped).toLocaleDateString()
                            : 'Never'
                          }
                        </Typography>
                      </Box>
                      <Typography variant="caption" color="text.secondary" sx={{ wordBreak: 'break-all' }}>
                        {source.url}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {recentJobs.map((job) => (
              <Card key={job.job_id}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <Chip 
                          icon={getStatusIcon(job.status)}
                          label={job.status}
                          color={getStatusColor(job.status) as any}
                          size="small"
                        />
                        <Typography variant="body2" color="text.secondary">
                          Sources: {job.source_id}
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        Started: {job.started_at ? new Date(job.started_at).toLocaleString() : 'Unknown'}
                        {job.completed_at && (
                          <span style={{ marginLeft: 16 }}>
                            Duration: {formatDuration(job.started_at!, job.completed_at)}
                          </span>
                        )}
                      </Typography>
                    </Box>
                    <Button 
                      variant="outlined" 
                      size="small"
                      onClick={() => setSelectedJob(job)}
                      startIcon={<Visibility />}
                    >
                      Details
                    </Button>
                  </Box>

                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={6} md={3}>
                      <Typography variant="body2" color="text.secondary">Found:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>{job.tenders_found}</Typography>
                    </Grid>
                    <Grid item xs={6} md={3}>
                      <Typography variant="body2" color="text.secondary">Processed:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>{job.tenders_processed}</Typography>
                    </Grid>
                    <Grid item xs={6} md={3}>
                      <Typography variant="body2" color="text.secondary">Documents:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>{job.documents_downloaded}</Typography>
                    </Grid>
                    <Grid item xs={6} md={3}>
                      <Typography variant="body2" color="text.secondary">Queen Bee:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>{job.queen_bee_assignments.length}</Typography>
                    </Grid>
                  </Grid>

                  {job.status === 'running' && job.tenders_found > 0 && (
                    <Box sx={{ mb: 2 }}>
                      <LinearProgress 
                        variant="determinate"
                        value={(job.tenders_processed / job.tenders_found) * 100}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                        {job.tenders_processed} of {job.tenders_found} tenders processed
                      </Typography>
                    </Box>
                  )}

                  {job.errors.length > 0 && (
                    <Alert severity="warning" icon={<Warning />}>
                      {job.errors.length} error(s) occurred during processing
                    </Alert>
                  )}
                </CardContent>
              </Card>
            ))}
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12} lg={6}>
              <Card>
                <CardHeader title="System Health" />
                <CardContent>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography>Scraping Engine:</Typography>
                      <Chip 
                        icon={<CheckCircle sx={{ fontSize: 12 }} />}
                        label="Healthy"
                        color="success"
                        size="small"
                      />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography>Document Download:</Typography>
                      <Chip 
                        icon={<CheckCircle sx={{ fontSize: 12 }} />}
                        label="Healthy"
                        color="success"
                        size="small"
                      />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography>Queen Bee Integration:</Typography>
                      <Chip 
                        icon={<CheckCircle sx={{ fontSize: 12 }} />}
                        label="Healthy"
                        color="success"
                        size="small"
                      />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography>Database Connection:</Typography>
                      <Chip 
                        icon={<CheckCircle sx={{ fontSize: 12 }} />}
                        label="Healthy"
                        color="success"
                        size="small"
                      />
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} lg={6}>
              <Card>
                <CardHeader title="Performance Metrics" />
                <CardContent>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography>Avg Scraping Time:</Typography>
                      <Typography sx={{ fontWeight: 500 }}>2.3 minutes</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography>Success Rate:</Typography>
                      <Typography sx={{ fontWeight: 500, color: 'success.main' }}>94.2%</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography>Documents/Hour:</Typography>
                      <Typography sx={{ fontWeight: 500 }}>156</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography>Queen Bee Response:</Typography>
                      <Typography sx={{ fontWeight: 500 }}>1.8s avg</Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
      </Box>

      {/* Job Details Dialog */}
      {selectedJob && (
        <Dialog 
          open={!!selectedJob} 
          onClose={() => setSelectedJob(null)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Ingestion Job Details</DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 1 }}>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>Job ID:</Typography>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>{selectedJob.job_id}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>Status:</Typography>
                  <Chip 
                    label={selectedJob.status}
                    color={getStatusColor(selectedJob.status) as any}
                    size="small"
                  />
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>Sources:</Typography>
                  <Typography variant="body2">{selectedJob.source_id}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>Duration:</Typography>
                  <Typography variant="body2">
                    {selectedJob.started_at && selectedJob.completed_at
                      ? formatDuration(selectedJob.started_at, selectedJob.completed_at)
                      : 'In progress'
                    }
                  </Typography>
                </Grid>
              </Grid>

              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6} md={3} sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                    {selectedJob.tenders_found}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">Tenders Found</Typography>
                </Grid>
                <Grid item xs={6} md={3} sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                    {selectedJob.tenders_processed}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">Processed</Typography>
                </Grid>
                <Grid item xs={6} md={3} sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'secondary.main' }}>
                    {selectedJob.documents_downloaded}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">Documents</Typography>
                </Grid>
                <Grid item xs={6} md={3} sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                    {selectedJob.queen_bee_assignments.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">Queen Bee Tasks</Typography>
                </Grid>
              </Grid>

              {selectedJob.errors.length > 0 && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 500, mb: 1 }}>
                    Errors ({selectedJob.errors.length})
                  </Typography>
                  <Box sx={{ maxHeight: 128, overflowY: 'auto' }}>
                    {selectedJob.errors.map((error, index) => (
                      <Alert key={index} severity="error" sx={{ mb: 1 }}>
                        {error}
                      </Alert>
                    ))}
                  </Box>
                </Box>
              )}

              {selectedJob.queen_bee_assignments.length > 0 && (
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 500, mb: 1 }}>
                    Queen Bee Assignments
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedJob.queen_bee_assignments.length} tasks assigned to Queen Bee workers
                  </Typography>
                </Box>
              )}
            </Box>
          </DialogContent>
        </Dialog>
      )}

      {/* Test Results Dialog */}
      {testResults && (
        <Dialog 
          open={!!testResults} 
          onClose={() => setTestResults(null)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Scraping Test Results</DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 1 }}>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>Source:</Typography>
                  <Typography variant="body2">{testResults.source_id}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>Tenders Found:</Typography>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                    {testResults.tenders_found}
                  </Typography>
                </Grid>
              </Grid>

              {testResults.sample_tenders && testResults.sample_tenders.length > 0 && (
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 500, mb: 2 }}>Sample Tenders</Typography>
                  <Box sx={{ maxHeight: 264, overflowY: 'auto' }}>
                    {testResults.sample_tenders.map((tender: ScrapedTender, index: number) => (
                      <Paper key={index} sx={{ p: 2, mb: 1, border: 1, borderColor: 'divider' }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>{tender.title}</Typography>
                        <Box sx={{ mt: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            {tender.organization} • {tender.category}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {tender.location} • Closes: {new Date(tender.closing_date).toLocaleDateString()}
                          </Typography>
                          {tender.estimated_value && (
                            <Typography variant="body2" color="text.secondary">
                              Value: R{tender.estimated_value.toLocaleString()}
                            </Typography>
                          )}
                        </Box>
                      </Paper>
                    ))}
                  </Box>
                </Box>
              )}
            </Box>
          </DialogContent>
        </Dialog>
      )}
    </Box>
  );
};

export default TenderIngestionDashboard;
