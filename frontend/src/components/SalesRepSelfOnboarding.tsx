import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Button,
  Chip,
  LinearProgress,
  TextField,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  Box,
  Typography,
  Grid,
  Container,
  Paper,
  Stack,
  Divider
} from '@mui/material';
import {
  Psychology as Brain,
  GpsFixed as Target,
  EmojiEvents as Trophy,
  People as Users,
  Bolt as Zap,
  ArrowForward as ArrowRight,
  CheckCircle,
  Star,
  WorkspacePremium as Crown,
  Rocket,
  Favorite as Heart,
  <PERSON><PERSON>hart as BarChart3,
  Timer,
  CardGiftcard as Gift,
  AutoAwesome as Sparkles,
  Business as Building,
  PersonAdd as UserPlus
} from '@mui/icons-material';

interface QuizQuestion {
  id: string;
  question: string;
  type: 'single' | 'multiple' | 'scale';
  options: Array<{
    value: string;
    label: string;
    description?: string;
  }>;
  psychological_focus: string;
}

interface OnboardingState {
  step: 'discovery' | 'quiz' | 'profile' | 'target' | 'dashboard' | 'company_pathway';
  quiz_responses: Array<{
    question_id: string;
    response: string | number;
    response_time_ms: number;
  }>;
  psychological_profile?: any;
  rep_id?: string;
  feature_tier: 'solo_rep' | 'team_rep' | 'enterprise_rep';
}

const SalesRepSelfOnboarding: React.FC = () => {
  const [onboardingState, setOnboardingState] = useState<OnboardingState>({
    step: 'discovery',
    quiz_responses: [],
    feature_tier: 'solo_rep'
  });

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [questionStartTime, setQuestionStartTime] = useState(Date.now());
  const [personalInfo, setPersonalInfo] = useState({
    email: '',
    first_name: '',
    last_name: '',
    phone: ''
  });
  const [loading, setLoading] = useState(false);

  // Psychological quiz questions
  const quizQuestions: QuizQuestion[] = [
    {
      id: 'motivation_primary',
      question: 'What motivates you most in sales?',
      type: 'single',
      options: [
        { value: 'recognition', label: '🏆 Recognition & Status', description: 'Being acknowledged as a top performer' },
        { value: 'money', label: '💰 Financial Rewards', description: 'Maximizing commission and earnings' },
        { value: 'relationships', label: '🤝 Client Relationships', description: 'Building lasting partnerships' },
        { value: 'data', label: '📊 Data & Insights', description: 'Understanding trends and optimization' }
      ],
      psychological_focus: 'Primary motivation archetype'
    },
    {
      id: 'work_style',
      question: 'How do you prefer to work?',
      type: 'single',
      options: [
        { value: 'competitive', label: '⚡ Competitive Environment', description: 'Thrive on competition and rankings' },
        { value: 'collaborative', label: '👥 Team Collaboration', description: 'Work best with team support' },
        { value: 'analytical', label: '🔍 Data-Driven Approach', description: 'Focus on metrics and analysis' },
        { value: 'independent', label: '🎯 Independent Operation', description: 'Prefer autonomy and self-direction' }
      ],
      psychological_focus: 'Work style preference'
    },
    {
      id: 'stress_response',
      question: 'How do you handle high-pressure situations?',
      type: 'single',
      options: [
        { value: 'thrive', label: '🚀 Thrive Under Pressure', description: 'Perform better with deadlines' },
        { value: 'manage', label: '⚖️ Manage Pressure Well', description: 'Stay calm and focused' },
        { value: 'struggle', label: '😰 Find Pressure Challenging', description: 'Prefer steady, predictable pace' }
      ],
      psychological_focus: 'Stress tolerance assessment'
    },
    {
      id: 'goal_setting',
      question: 'What type of goals energize you most?',
      type: 'single',
      options: [
        { value: 'stretch', label: '🎯 Ambitious Stretch Goals', description: 'Big challenges that push limits' },
        { value: 'achievable', label: '✅ Realistic Achievable Goals', description: 'Steady progress with regular wins' },
        { value: 'team', label: '👥 Team-Based Goals', description: 'Collective achievements and collaboration' },
        { value: 'personal', label: '🌟 Personal Development Goals', description: 'Skill building and growth' }
      ],
      psychological_focus: 'Goal orientation preference'
    },
    {
      id: 'feedback_preference',
      question: 'How do you prefer to receive feedback?',
      type: 'single',
      options: [
        { value: 'immediate', label: '⚡ Real-time Feedback', description: 'Instant updates and notifications' },
        { value: 'weekly', label: '📅 Weekly Check-ins', description: 'Regular scheduled reviews' },
        { value: 'monthly', label: '📊 Monthly Deep Dives', description: 'Comprehensive performance analysis' },
        { value: 'peer', label: '👥 Peer Comparison', description: 'See how you stack against others' }
      ],
      psychological_focus: 'Feedback and recognition style'
    }
  ];

  const currentQuestion = quizQuestions[currentQuestionIndex];

  const handleQuizResponse = (response: string) => {
    const responseTime = Date.now() - questionStartTime;

    const newResponse = {
      question_id: currentQuestion.id,
      response: response,
      response_time_ms: responseTime
    };

    const updatedResponses = [...onboardingState.quiz_responses, newResponse];

    setOnboardingState(prev => ({
      ...prev,
      quiz_responses: updatedResponses
    }));

    if (currentQuestionIndex < quizQuestions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
      setQuestionStartTime(Date.now());
    } else {
      // Quiz completed, move to profile step
      setOnboardingState(prev => ({ ...prev, step: 'profile' }));
    }
  };

  const handlePersonalInfoSubmit = async () => {
    try {
      setLoading(true);

      const onboardingData = {
        ...personalInfo,
        quiz_responses: onboardingState.quiz_responses,
        referral_source: 'direct',
        utm_data: {}
      };

      const response = await fetch('/api/sales-rep/instant-onboard', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(onboardingData)
      });

      if (response.ok) {
        const result = await response.json();

        setOnboardingState(prev => ({
          ...prev,
          step: 'target',
          psychological_profile: result.psychological_profile,
          rep_id: result.rep_id
        }));
      } else {
        throw new Error('Onboarding failed');
      }
    } catch (err) {
      console.error('Error during onboarding:', err);
    } finally {
      setLoading(false);
    }
  };

  const getArchetypeIcon = (archetype: string) => {
    const icons = {
      'achiever': <Crown sx={{ color: 'purple.main' }} />,
      'hunter': <Target sx={{ color: 'error.main' }} />,
      'relationship_builder': <Heart sx={{ color: 'primary.main' }} />,
      'analyst': <Brain sx={{ color: 'success.main' }} />
    };
    return icons[archetype as keyof typeof icons] || <Star />;
  };

  const getArchetypeDescription = (archetype: string) => {
    const descriptions = {
      'achiever': 'You thrive on recognition, competition, and status. You\'re motivated by achievements and public acknowledgment.',
      'hunter': 'You\'re driven by financial rewards and results. Commission and revenue targets energize you most.',
      'relationship_builder': 'You excel at building lasting client relationships. Customer satisfaction and trust drive your success.',
      'analyst': 'You love data, trends, and optimization. You find insights and process improvement highly motivating.'
    };
    return descriptions[archetype as keyof typeof descriptions] || '';
  };

  const renderDiscoveryStep = () => (
    <Container maxWidth="lg" sx={{ textAlign: 'center', py: 4 }}>
      <Stack spacing={4}>
        <Box>
          <Typography variant="h2" component="h1" gutterBottom fontWeight="bold">
            🎯 Discover Your Sales Superpower
          </Typography>
          <Typography variant="h5" color="text.secondary">
            Join 2,847 sales reps already crushing their targets with psychological insights
          </Typography>
        </Box>

        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card sx={{ border: 2, borderColor: 'purple.200', '&:hover': { borderColor: 'purple.400' }, transition: 'border-color 0.3s' }}>
              <CardContent sx={{ p: 3, textAlign: 'center' }}>
                <Brain sx={{ fontSize: 48, color: 'purple.main', mb: 2 }} />
                <Typography variant="h6" gutterBottom fontWeight="semibold">
                  Psychological Profiling
                </Typography>
                <Typography color="text.secondary">
                  Discover your unique sales archetype in 2 minutes
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card sx={{ border: 2, borderColor: 'primary.light', '&:hover': { borderColor: 'primary.main' }, transition: 'border-color 0.3s' }}>
              <CardContent sx={{ p: 3, textAlign: 'center' }}>
                <Target sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                <Typography variant="h6" gutterBottom fontWeight="semibold">
                  Smart Target Setting
                </Typography>
                <Typography color="text.secondary">
                  AI-calibrated goals based on your psychology
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card sx={{ border: 2, borderColor: 'success.light', '&:hover': { borderColor: 'success.main' }, transition: 'border-color 0.3s' }}>
              <CardContent sx={{ p: 3, textAlign: 'center' }}>
                <Trophy sx={{ fontSize: 48, color: 'success.main', mb: 2 }} />
                <Typography variant="h6" gutterBottom fontWeight="semibold">
                  Achievement System
                </Typography>
                <Typography color="text.secondary">
                  Unlock rewards and level up your performance
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Stack spacing={2} alignItems="center">
          <Button
            size="large"
            variant="contained"
            sx={{ px: 4, py: 2, fontSize: '1.1rem' }}
            onClick={() => setOnboardingState(prev => ({ ...prev, step: 'quiz' }))}
            endIcon={<ArrowRight />}
          >
            Start Your 2-Minute Assessment
          </Button>

          <Stack direction="row" spacing={3} alignItems="center">
            <Stack direction="row" spacing={1} alignItems="center">
              <Timer fontSize="small" />
              <Typography variant="body2" color="text.secondary">2 minutes</Typography>
            </Stack>
            <Stack direction="row" spacing={1} alignItems="center">
              <Gift fontSize="small" />
              <Typography variant="body2" color="text.secondary">Instant results</Typography>
            </Stack>
            <Stack direction="row" spacing={1} alignItems="center">
              <Sparkles fontSize="small" />
              <Typography variant="body2" color="text.secondary">Free forever</Typography>
            </Stack>
          </Stack>
        </Stack>
      </Stack>
    </Container>
  );

  const renderQuizStep = () => (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Stack spacing={3}>
        <Box textAlign="center">
          <Typography variant="h4" gutterBottom fontWeight="bold">
            Sales Psychology Assessment
          </Typography>
          <Typography color="text.secondary" gutterBottom>
            Question {currentQuestionIndex + 1} of {quizQuestions.length}
          </Typography>
          <LinearProgress 
            variant="determinate" 
            value={((currentQuestionIndex + 1) / quizQuestions.length) * 100} 
            sx={{ width: '100%', height: 8, borderRadius: 4 }}
          />
        </Box>

        <Card>
          <CardContent sx={{ p: 4 }}>
            <Typography variant="h5" gutterBottom>
              {currentQuestion.question}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {currentQuestion.psychological_focus}
            </Typography>
            
            <Stack spacing={2} sx={{ mt: 3 }}>
              {currentQuestion.options.map((option) => (
                <Button
                  key={option.value}
                  variant="outlined"
                  sx={{ 
                    p: 2, 
                    textAlign: 'left', 
                    justifyContent: 'flex-start',
                    '&:hover': { borderColor: 'purple.main' }
                  }}
                  onClick={() => handleQuizResponse(option.value)}
                >
                  <Box>
                    <Typography fontWeight="medium">{option.label}</Typography>
                    {option.description && (
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                        {option.description}
                      </Typography>
                    )}
                  </Box>
                </Button>
              ))}
            </Stack>
          </CardContent>
        </Card>
      </Stack>
    </Container>
  );

  const renderProfileStep = () => (
    <Container maxWidth="sm" sx={{ py: 4 }}>
      <Stack spacing={3}>
        <Box textAlign="center">
          <Typography variant="h4" gutterBottom fontWeight="bold">
            Almost There!
          </Typography>
          <Typography color="text.secondary">
            Just a few details to personalize your experience
          </Typography>
        </Box>

        <Card>
          <CardContent sx={{ p: 3 }}>
            <Stack spacing={3}>
              <TextField
                fullWidth
                label="Email Address"
                type="email"
                value={personalInfo.email}
                onChange={(e) => setPersonalInfo(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
              />

              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="First Name"
                    value={personalInfo.first_name}
                    onChange={(e) => setPersonalInfo(prev => ({ ...prev, first_name: e.target.value }))}
                    placeholder="John"
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Last Name"
                    value={personalInfo.last_name}
                    onChange={(e) => setPersonalInfo(prev => ({ ...prev, last_name: e.target.value }))}
                    placeholder="Doe"
                  />
                </Grid>
              </Grid>

              <TextField
                fullWidth
                label="Phone (Optional)"
                type="tel"
                value={personalInfo.phone}
                onChange={(e) => setPersonalInfo(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="+27 123 456 789"
              />

              <Button
                fullWidth
                variant="contained"
                size="large"
                onClick={handlePersonalInfoSubmit}
                disabled={!personalInfo.email || !personalInfo.first_name || !personalInfo.last_name || loading}
                endIcon={<Rocket />}
              >
                {loading ? 'Creating Your Profile...' : 'Complete Setup'}
              </Button>
            </Stack>
          </CardContent>
        </Card>
      </Stack>
    </Container>
  );

  const renderTargetStep = () => (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Stack spacing={4}>
        <Box textAlign="center">
          <Stack direction="row" spacing={1} justifyContent="center" alignItems="center" sx={{ mb: 2 }}>
            <CheckCircle sx={{ color: 'success.main', fontSize: 32 }} />
            <Typography variant="h4" fontWeight="bold">
              Welcome to BidBeez!
            </Typography>
          </Stack>

          {onboardingState.psychological_profile && (
            <Card sx={{ border: 2, borderColor: 'purple.light', background: 'linear-gradient(to right, #f3e5f5, #e3f2fd)' }}>
              <CardContent sx={{ p: 3, textAlign: 'center' }}>
                <Stack direction="row" spacing={2} justifyContent="center" alignItems="center" sx={{ mb: 2 }}>
                  {getArchetypeIcon(onboardingState.psychological_profile.archetype)}
                  <Box>
                    <Typography variant="h5" fontWeight="bold" sx={{ textTransform: 'capitalize' }}>
                      {onboardingState.psychological_profile.archetype.replace('_', ' ')} Sales Rep
                    </Typography>
                    <Chip label="Your Sales Archetype" variant="outlined" sx={{ mt: 1 }} />
                  </Box>
                </Stack>
                <Typography color="text.secondary">
                  {getArchetypeDescription(onboardingState.psychological_profile.archetype)}
                </Typography>
              </CardContent>
            </Card>
          )}
        </Box>

        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent sx={{ p: 3, textAlign: 'center' }}>
                <Target sx={{ fontSize: 32, color: 'primary.main', mb: 2 }} />
                <Typography variant="h6" fontWeight="semibold" gutterBottom>
                  Solo Rep Access
                </Typography>
                <Stack spacing={1} sx={{ textAlign: 'left' }}>
                  <Typography variant="body2">✅ Personal target tracking</Typography>
                  <Typography variant="body2">✅ 5 supplier quotes/month</Typography>
                  <Typography variant="body2">✅ Basic achievements</Typography>
                  <Typography variant="body2">✅ Anonymous leaderboard</Typography>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card sx={{ border: 2, borderColor: 'primary.main' }}>
              <CardContent sx={{ p: 3, textAlign: 'center' }}>
                <Users sx={{ fontSize: 32, color: 'primary.main', mb: 2 }} />
                <Typography variant="h6" fontWeight="semibold" gutterBottom>
                  Team Features Preview
                </Typography>
                <Stack spacing={1} sx={{ textAlign: 'left' }}>
                  <Typography variant="body2">🔒 25 supplier quotes/month</Typography>
                  <Typography variant="body2">🔒 Team achievements</Typography>
                  <Typography variant="body2">🔒 Advanced analytics</Typography>
                  <Typography variant="body2">🔒 Team collaboration</Typography>
                </Stack>
                <Chip label="Unlock with Company" sx={{ mt: 2 }} />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent sx={{ p: 3, textAlign: 'center' }}>
                <Crown sx={{ fontSize: 32, color: 'purple.main', mb: 2 }} />
                <Typography variant="h6" fontWeight="semibold" gutterBottom>
                  Enterprise Features
                </Typography>
                <Stack spacing={1} sx={{ textAlign: 'left' }}>
                  <Typography variant="body2">🔒 Unlimited quotes</Typography>
                  <Typography variant="body2">🔒 Custom branding</Typography>
                  <Typography variant="body2">🔒 API access</Typography>
                  <Typography variant="body2">🔒 White-label options</Typography>
                </Stack>
                <Chip label="Coming Soon" variant="outlined" sx={{ mt: 2 }} />
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Box textAlign="center">
          <Stack spacing={2} alignItems="center">
            <Button
              size="large"
              variant="contained"
              onClick={() => setOnboardingState(prev => ({ ...prev, step: 'dashboard' }))}
              endIcon={<ArrowRight />}
            >
              Enter Your Sales Command Center
            </Button>

            <Typography variant="body2" color="text.secondary">
              You can upgrade to team features anytime by adding your company
            </Typography>
          </Stack>
        </Box>
      </Stack>
    </Container>
  );

  const renderDashboardStep = () => (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Stack spacing={4}>
        <Box textAlign="center">
          <Typography variant="h3" gutterBottom fontWeight="bold">
            🎉 You're All Set!
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Your personalized sales dashboard is ready
          </Typography>
        </Box>

        <Alert severity="info" icon={<Sparkles />}>
          <Typography>
            <strong>Achievement Unlocked:</strong> Welcome Aboard! You've earned your first 100 XP and discovered your sales archetype.
          </Typography>
        </Alert>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 2 }}>
                  <Target />
                  <Typography variant="h6">Next Steps</Typography>
                </Stack>
                <Stack spacing={2}>
                  <Stack direction="row" spacing={2} alignItems="center">
                    <Box sx={{ width: 24, height: 24, borderRadius: '50%', bgcolor: 'primary.main', color: 'white', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '0.75rem' }}>1</Box>
                    <Typography>Set your first monthly target</Typography>
                  </Stack>
                  <Stack direction="row" spacing={2} alignItems="center">
                    <Box sx={{ width: 24, height: 24, borderRadius: '50%', bgcolor: 'grey.300', color: 'white', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '0.75rem' }}>2</Box>
                    <Typography>Request your first supplier quote</Typography>
                  </Stack>
                  <Stack direction="row" spacing={2} alignItems="center">
                    <Box sx={{ width: 24, height: 24, borderRadius: '50%', bgcolor: 'grey.300', color: 'white', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '0.75rem' }}>3</Box>
                    <Typography>Explore the leaderboard</Typography>
                  </Stack>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 2 }}>
                  <Building />
                  <Typography variant="h6">Want More Features?</Typography>
                </Stack>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Unlock team features by adding your company:
                </Typography>
                <Stack spacing={1} sx={{ mb: 2 }}>
                  <Typography variant="body2">• 5x more supplier quotes (25/month)</Typography>
                  <Typography variant="body2">• Team achievements and collaboration</Typography>
                  <Typography variant="body2">• Advanced analytics and insights</Typography>
                  <Typography variant="body2">• Team vs team competition</Typography>
                </Stack>
                <Button
                  variant="outlined"
                  fullWidth
                  onClick={() => setOnboardingState(prev => ({ ...prev, step: 'company_pathway' }))}
                  startIcon={<UserPlus />}
                >
                  Add My Company
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Box textAlign="center">
          <Button 
            size="large" 
            variant="contained"
            onClick={() => window.location.href = '/sales-rep-centre'}
            endIcon={<ArrowRight />}
          >
            Go to Dashboard
          </Button>
        </Box>
      </Stack>
    </Container>
  );

  const renderCompanyPathwayStep = () => (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Stack spacing={4}>
        <Box textAlign="center">
          <Typography variant="h4" gutterBottom fontWeight="bold">
            🚀 Unlock Team Superpowers
          </Typography>
          <Typography color="text.secondary">
            Add your company to access premium features
          </Typography>
        </Box>

        <Alert severity="success" icon={<Gift />}>
          <Typography>
            <strong>Special Offer:</strong> Get instant team access while we verify your company (usually within 24 hours)
          </Typography>
        </Alert>

        <Card>
          <CardContent sx={{ p: 3 }}>
            <Stack spacing={3}>
              <TextField
                fullWidth
                label="Company Name"
                placeholder="Your Company Ltd"
              />

              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <InputLabel>Industry</InputLabel>
                    <Select label="Industry">
                      <MenuItem value="construction">Construction</MenuItem>
                      <MenuItem value="manufacturing">Manufacturing</MenuItem>
                      <MenuItem value="technology">Technology</MenuItem>
                      <MenuItem value="services">Professional Services</MenuItem>
                      <MenuItem value="other">Other</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <InputLabel>Company Size</InputLabel>
                    <Select label="Company Size">
                      <MenuItem value="1-10">1-10 employees</MenuItem>
                      <MenuItem value="11-50">11-50 employees</MenuItem>
                      <MenuItem value="51-200">51-200 employees</MenuItem>
                      <MenuItem value="201-1000">201-1000 employees</MenuItem>
                      <MenuItem value="1000+">1000+ employees</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>

              <FormControl fullWidth>
                <InputLabel>Your Role</InputLabel>
                <Select label="Your Role">
                  <MenuItem value="sales_rep">Sales Representative</MenuItem>
                  <MenuItem value="sales_manager">Sales Manager</MenuItem>
                  <MenuItem value="sales_director">Sales Director</MenuItem>
                  <MenuItem value="vp_sales">VP of Sales</MenuItem>
                </Select>
              </FormControl>

              <Button 
                fullWidth 
                variant="contained" 
                size="large"
                endIcon={<Rocket />}
              >
                Unlock Team Features Now
              </Button>

              <Box textAlign="center">
                <Button
                  variant="text"
                  onClick={() => setOnboardingState(prev => ({ ...prev, step: 'dashboard' }))}
                >
                  Maybe Later
                </Button>
              </Box>
            </Stack>
          </CardContent>
        </Card>
      </Stack>
    </Container>
  );

  return (
    <Box sx={{ 
      minHeight: '100vh', 
      background: 'linear-gradient(135deg, #e3f2fd 0%, #ffffff 50%, #f3e5f5 100%)',
      py: 6,
      px: 2
    }}>
      {onboardingState.step === 'discovery' && renderDiscoveryStep()}
      {onboardingState.step === 'quiz' && renderQuizStep()}
      {onboardingState.step === 'profile' && renderProfileStep()}
      {onboardingState.step === 'target' && renderTargetStep()}
      {onboardingState.step === 'dashboard' && renderDashboardStep()}
      {onboardingState.step === 'company_pathway' && renderCompanyPathwayStep()}
    </Box>
  );
};

export default SalesRepSelfOnboarding;