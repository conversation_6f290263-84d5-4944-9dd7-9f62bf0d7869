import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  <PERSON><PERSON>graphy,
  Button,
  Chip,
  LinearProgress,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Tabs,
  Tab,
  Alert,
  Grid,
  Checkbox,
  FormControlLabel,
  CircularProgress,
  IconButton,
  Divider,
  Paper
} from '@mui/material';
import {
  Search,
  FilterList,
  Star,
  LocationOn,
  EmojiEvents,
  TrendingUp,
  People,
  CheckCircle,
  Warning,
  Visibility,
  Chat,
  Download
} from '@mui/icons-material';

interface SupplierMatch {
  supplier_id: string;
  supplier_name: string;
  overall_score: number;
  boq_match_score: number;
  sow_match_score: number;
  compliance_score: number;
  geographic_score: number;
  trust_score: number;
  bee_bonus: number;
  provincial_bonus: number;
  confidence_level: string;
  match_details: {
    boq_matches: number;
    sow_matches: number;
    standards_compliance: number;
    product_count: number;
  };
  contact_info: {
    email: string;
    company_name: string;
    location: any;
  };
  capabilities: string[];
  certifications: any[];
}

interface MatchResponse {
  spec_id: string;
  total_suppliers_evaluated: number;
  matches_returned: number;
  processing_time_ms: number;
  matches: SupplierMatch[];
  algorithm_version: string;
}

interface SupplierMatchingDashboardProps {
  specificationId: string;
}

// Custom TabPanel component for MUI Tabs
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const SupplierMatchingDashboard: React.FC<SupplierMatchingDashboardProps> = ({ 
  specificationId 
}) => {
  const [matches, setMatches] = useState<SupplierMatch[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    minScore: 0.3,
    maxSuppliers: 20,
    includeBeeWeighting: true,
    includeGeographicWeighting: true,
    provincePreference: '',
    categoryFilter: ''
  });
  const [sortBy, setSortBy] = useState<'overall_score' | 'bee_bonus' | 'geographic_score'>('overall_score');
  const [selectedSupplier, setSelectedSupplier] = useState<SupplierMatch | null>(null);
  const [tabValue, setTabValue] = useState(0);

  useEffect(() => {
    if (specificationId) {
      loadExistingMatches();
    }
  }, [specificationId]);

  const loadExistingMatches = async () => {
    try {
      const response = await fetch(`/api/matching/matches/${specificationId}`);
      if (response.ok) {
        const data = await response.json();
        setMatches(data.matches || []);
      }
    } catch (err) {
      console.error('Failed to load existing matches:', err);
    }
  };

  const runMatching = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/matching/match', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          spec_id: specificationId,
          max_suppliers: filters.maxSuppliers,
          min_score: filters.minScore,
          include_bee_weighting: filters.includeBeeWeighting,
          include_geographic_weighting: filters.includeGeographicWeighting,
          province_preference: filters.provincePreference || null,
          category_filter: filters.categoryFilter || null
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to run supplier matching');
      }

      const result: MatchResponse = await response.json();
      setMatches(result.matches);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 0.8) return 'success.main';
    if (score >= 0.6) return 'warning.main';
    return 'error.main';
  };

  const getConfidenceColor = (level: string) => {
    switch (level) {
      case 'high': return 'success';
      case 'medium': return 'warning';
      case 'low': return 'error';
      default: return 'error';
    }
  };

  const sortedMatches = [...matches].sort((a, b) => {
    return b[sortBy] - a[sortBy];
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box sx={{ maxWidth: '1200px', mx: 'auto', p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h3" component="h1" fontWeight="bold" color="text.primary">
            Supplier Matching
          </Typography>
          <Typography variant="body1" color="text.secondary">
            AI-powered supplier discovery and matching
          </Typography>
        </Box>
        <Button 
          variant="contained"
          onClick={runMatching} 
          disabled={loading}
          startIcon={loading ? <CircularProgress size={16} /> : <Search />}
        >
          {loading ? 'Matching...' : 'Run Matching'}
        </Button>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardHeader>
          <Typography variant="h6" component="div" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FilterList />
            Matching Filters
          </Typography>
        </CardHeader>
        <CardContent>
          <Grid container spacing={2}>
            <Grid item xs={12} md={2}>
              <TextField
                label="Min Score"
                type="number"
                inputProps={{ min: 0, max: 1, step: 0.1 }}
                value={filters.minScore}
                onChange={(e) => setFilters(prev => ({ 
                  ...prev, 
                  minScore: parseFloat(e.target.value) 
                }))}
                fullWidth
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                label="Max Suppliers"
                type="number"
                inputProps={{ min: 1, max: 50 }}
                value={filters.maxSuppliers}
                onChange={(e) => setFilters(prev => ({ 
                  ...prev, 
                  maxSuppliers: parseInt(e.target.value) 
                }))}
                fullWidth
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Province</InputLabel>
                <Select
                  value={filters.provincePreference}
                  label="Province"
                  onChange={(e) => setFilters(prev => ({ 
                    ...prev, 
                    provincePreference: e.target.value 
                  }))}
                >
                  <MenuItem value="">Any Province</MenuItem>
                  <MenuItem value="Gauteng">Gauteng</MenuItem>
                  <MenuItem value="Western Cape">Western Cape</MenuItem>
                  <MenuItem value="KwaZulu-Natal">KwaZulu-Natal</MenuItem>
                  <MenuItem value="Eastern Cape">Eastern Cape</MenuItem>
                  <MenuItem value="Free State">Free State</MenuItem>
                  <MenuItem value="Limpopo">Limpopo</MenuItem>
                  <MenuItem value="Mpumalanga">Mpumalanga</MenuItem>
                  <MenuItem value="North West">North West</MenuItem>
                  <MenuItem value="Northern Cape">Northern Cape</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                label="Category"
                placeholder="e.g., Construction"
                value={filters.categoryFilter}
                onChange={(e) => setFilters(prev => ({ 
                  ...prev, 
                  categoryFilter: e.target.value 
                }))}
                fullWidth
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={filters.includeBeeWeighting}
                      onChange={(e) => setFilters(prev => ({ 
                        ...prev, 
                        includeBeeWeighting: e.target.checked 
                      }))}
                      size="small"
                    />
                  }
                  label="B-BBEE Weighting"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={filters.includeGeographicWeighting}
                      onChange={(e) => setFilters(prev => ({ 
                        ...prev, 
                        includeGeographicWeighting: e.target.checked 
                      }))}
                      size="small"
                    />
                  }
                  label="Geographic Weighting"
                />
              </Box>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Sort By</InputLabel>
                <Select
                  value={sortBy}
                  label="Sort By"
                  onChange={(e) => setSortBy(e.target.value as any)}
                >
                  <MenuItem value="overall_score">Overall Score</MenuItem>
                  <MenuItem value="bee_bonus">B-BBEE Bonus</MenuItem>
                  <MenuItem value="geographic_score">Geographic Score</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} icon={<Warning />}>
          {error}
        </Alert>
      )}

      {/* Results Summary */}
      {matches.length > 0 && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={3} sx={{ textAlign: 'center' }}>
                <Typography variant="h4" fontWeight="bold" color="primary">
                  {matches.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Suppliers Matched
                </Typography>
              </Grid>
              <Grid item xs={12} md={3} sx={{ textAlign: 'center' }}>
                <Typography variant="h4" fontWeight="bold" color="success.main">
                  {matches.filter(m => m.overall_score >= 0.8).length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  High Confidence
                </Typography>
              </Grid>
              <Grid item xs={12} md={3} sx={{ textAlign: 'center' }}>
                <Typography variant="h4" fontWeight="bold" color="secondary">
                  {matches.filter(m => m.bee_bonus > 0).length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  B-BBEE Qualified
                </Typography>
              </Grid>
              <Grid item xs={12} md={3} sx={{ textAlign: 'center' }}>
                <Typography variant="h4" fontWeight="bold" color="warning.main">
                  {Math.round(matches.reduce((sum, m) => sum + m.overall_score, 0) / matches.length * 100)}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Avg Match Score
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Supplier Matches */}
      <Grid container spacing={3}>
        <Grid item xs={12} lg={6}>
          <Typography variant="h5" component="h2" sx={{ mb: 2 }}>
            Matched Suppliers
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {sortedMatches.map((match, index) => (
              <Card 
                key={match.supplier_id}
                sx={{ 
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  '&:hover': { boxShadow: 3 },
                  border: selectedSupplier?.supplier_id === match.supplier_id ? 2 : 1,
                  borderColor: selectedSupplier?.supplier_id === match.supplier_id ? 'primary.main' : 'divider'
                }}
                onClick={() => setSelectedSupplier(match)}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box>
                      <Typography variant="h6" component="h3">
                        {match.supplier_name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {match.contact_info.email}
                      </Typography>
                    </Box>
                    <Box sx={{ textAlign: 'right' }}>
                      <Typography 
                        variant="h4" 
                        fontWeight="bold"
                        color={getScoreColor(match.overall_score)}
                      >
                        {(match.overall_score * 100).toFixed(0)}%
                      </Typography>
                      <Chip 
                        label={match.confidence_level}
                        size="small"
                        color={getConfidenceColor(match.confidence_level) as any}
                      />
                    </Box>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">BOQ Match</Typography>
                      <Typography 
                        variant="body2"
                        color={getScoreColor(match.boq_match_score)}
                      >
                        {(match.boq_match_score * 100).toFixed(0)}%
                      </Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={match.boq_match_score * 100} 
                      sx={{ height: 6, borderRadius: 3, mb: 1 }}
                    />
                    
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">SOW Match</Typography>
                      <Typography 
                        variant="body2"
                        color={getScoreColor(match.sow_match_score)}
                      >
                        {(match.sow_match_score * 100).toFixed(0)}%
                      </Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={match.sow_match_score * 100} 
                      sx={{ height: 6, borderRadius: 3, mb: 1 }}
                    />
                    
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Compliance</Typography>
                      <Typography 
                        variant="body2"
                        color={getScoreColor(match.compliance_score)}
                      >
                        {(match.compliance_score * 100).toFixed(0)}%
                      </Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={match.compliance_score * 100} 
                      sx={{ height: 6, borderRadius: 3 }}
                    />
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      {match.bee_bonus > 0 && (
                        <Chip 
                          icon={<EmojiEvents />}
                          label="B-BBEE"
                          variant="outlined"
                          size="small"
                          color="success"
                        />
                      )}
                      {match.provincial_bonus > 0 && (
                        <Chip 
                          icon={<LocationOn />}
                          label="Local"
                          variant="outlined"
                          size="small"
                          color="primary"
                        />
                      )}
                    </Box>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <IconButton size="small">
                        <Visibility />
                      </IconButton>
                      <IconButton size="small">
                        <Chat />
                      </IconButton>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            ))}
          </Box>
        </Grid>

        {/* Supplier Details */}
        <Grid item xs={12} lg={6}>
          <Typography variant="h5" component="h2" sx={{ mb: 2 }}>
            Supplier Details
          </Typography>
          {selectedSupplier ? (
            <Card>
              <CardHeader>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="h6">{selectedSupplier.supplier_name}</Typography>
                  <Chip 
                    label={`${selectedSupplier.confidence_level} confidence`}
                    color={getConfidenceColor(selectedSupplier.confidence_level) as any}
                  />
                </Box>
              </CardHeader>
              <CardContent>
                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                  <Tabs value={tabValue} onChange={handleTabChange}>
                    <Tab label="Overview" />
                    <Tab label="Capabilities" />
                    <Tab label="Certifications" />
                  </Tabs>
                </Box>

                <TabPanel value={tabValue} index={0}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                        Contact Information
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {selectedSupplier.contact_info.email}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {selectedSupplier.contact_info.company_name}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                        Match Details
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                        <Typography variant="body2">BOQ Matches: {selectedSupplier.match_details.boq_matches}</Typography>
                        <Typography variant="body2">SOW Matches: {selectedSupplier.match_details.sow_matches}</Typography>
                        <Typography variant="body2">Standards: {selectedSupplier.match_details.standards_compliance}</Typography>
                        <Typography variant="body2">Products: {selectedSupplier.match_details.product_count}</Typography>
                      </Box>
                    </Grid>
                  </Grid>

                  <Box sx={{ mt: 3 }}>
                    <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                      Score Breakdown
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      {[
                        { label: 'BOQ Match', score: selectedSupplier.boq_match_score },
                        { label: 'SOW Match', score: selectedSupplier.sow_match_score },
                        { label: 'Compliance', score: selectedSupplier.compliance_score },
                        { label: 'Geographic', score: selectedSupplier.geographic_score },
                        { label: 'Trust', score: selectedSupplier.trust_score }
                      ].map(({ label, score }) => (
                        <Box key={label} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="body2">{label}</Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '120px' }}>
                            <LinearProgress 
                              variant="determinate" 
                              value={score * 100} 
                              sx={{ flexGrow: 1, height: 6, borderRadius: 3 }}
                            />
                            <Typography variant="body2" sx={{ minWidth: '40px', textAlign: 'right' }}>
                              {(score * 100).toFixed(0)}%
                            </Typography>
                          </Box>
                        </Box>
                      ))}
                    </Box>
                  </Box>
                </TabPanel>

                <TabPanel value={tabValue} index={1}>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {selectedSupplier.capabilities.map((capability, index) => (
                      <Chip 
                        key={index} 
                        label={capability}
                        variant="outlined"
                        size="small"
                      />
                    ))}
                  </Box>
                </TabPanel>

                <TabPanel value={tabValue} index={2}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {selectedSupplier.certifications.map((cert, index) => (
                      <Paper key={index} sx={{ p: 2, border: 1, borderColor: 'divider' }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                          <Box>
                            <Typography variant="subtitle2" fontWeight="medium">
                              {cert.standard_type} {cert.standard_code}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {cert.issuing_authority}
                            </Typography>
                          </Box>
                          <Chip 
                            label={cert.verification_status}
                            color={cert.verification_status === 'verified' ? 'success' : 'default'}
                            size="small"
                          />
                        </Box>
                      </Paper>
                    ))}
                  </Box>
                </TabPanel>

                <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                  <Button 
                    variant="contained" 
                    startIcon={<Chat />}
                    sx={{ flexGrow: 1 }}
                  >
                    Contact Supplier
                  </Button>
                  <Button 
                    variant="outlined"
                    startIcon={<Download />}
                  >
                    Export
                  </Button>
                </Box>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 6 }}>
                <People sx={{ fontSize: 48, opacity: 0.5, mb: 2 }} />
                <Typography color="text.secondary">
                  Select a supplier to view detailed information
                </Typography>
              </CardContent>
            </Card>
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default SupplierMatchingDashboard;
