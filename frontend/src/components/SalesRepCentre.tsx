import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  LinearProgress,
  Tabs,
  Tab,
  Box,
  Avatar,
  Alert,
  Grid,
  Paper,
  IconButton,
  CircularProgress,
  Container,
  Stack,
  Divider
} from '@mui/material';
import {
  GpsFixed as Target,
  TrendingUp,
  EmojiEvents as Award,
  People as Users,
  AttachMoney as DollarSign,
  CalendarToday as Calendar,
  EmojiEvents as Trophy,
  FlashOn as Zap,
  Psychology as Brain,
  Favorite as Heart,
  Star,
  LocalFireDepartment as Flame,
  WorkspacePremium as Crown,
  ExpandLess as ChevronUp,
  ExpandMore as ChevronDown,
  Settings,
  Notifications as <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Timeline as ActivityIcon
} from '@mui/icons-material';

interface SalesTarget {
  target_id: string;
  target_type: string;
  period: string;
  target_value: number;
  current_value: number;
  unit: string;
  start_date: string;
  end_date: string;
  completion_percentage: number;
  days_remaining: number;
  psychological_calibration: any;
}

interface PsychologicalState {
  stress_level: number;
  motivation_level: number;
  confidence_level: number;
  urgency_response: number;
  competitive_spirit: number;
  financial_motivation: number;
  cognitive_load: number;
  engagement_level: number;
}

interface SalesRepProfile {
  rep_id: string;
  archetype: string;
  motivation_factors: string[];
  psychological_state: PsychologicalState;
  target_preferences: any;
  performance_history: any;
}

interface BehavioralNudge {
  nudge_id: string;
  trigger_type: string;
  message: string;
  intensity: string;
  psychological_principle: string;
  expires_at?: string;
}

interface Achievement {
  achievement_id: string;
  name: string;
  description: string;
  icon: string;
  tier: string;
  category: string;
  xp_reward: number;
  psychological_benefit: string;
  unlocked_at: string;
}

const SalesRepCentre: React.FC = () => {
  const [profile, setProfile] = useState<SalesRepProfile | null>(null);
  const [targets, setTargets] = useState<SalesTarget[]>([]);
  const [nudges, setNudges] = useState<BehavioralNudge[]>([]);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState(0); // 0 = monthly, 1 = quarterly, 2 = yearly

  useEffect(() => {
    loadRepData();
    
    // Auto-refresh every 30 seconds for real-time psychological engagement
    const interval = setInterval(loadRepData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadRepData = async () => {
    try {
      const repId = 'current-rep-id'; // Replace with actual auth
      
      // Load profile
      const profileResponse = await fetch(`/api/sales-rep/${repId}/profile`);
      if (profileResponse.ok) {
        const profileData = await profileResponse.json();
        setProfile(profileData);
      }

      // Load targets
      const periodMap = ['monthly', 'quarterly', 'yearly'];
      const targetsResponse = await fetch(`/api/sales-rep/${repId}/targets?period=${periodMap[selectedPeriod]}`);
      if (targetsResponse.ok) {
        const targetsData = await targetsResponse.json();
        setTargets(targetsData.targets || []);
      }

      // Generate behavioral nudges
      const context = {
        target_completion: targets[0]?.completion_percentage || 0,
        days_left: targets[0]?.days_remaining || 30,
        rank_dropped: false, // Would calculate from leaderboard
        commission_opportunity: 15000,
        potential_commission: 15000
      };

      const nudgesResponse = await fetch(`/api/sales-rep/${repId}/nudges`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(context)
      });

      if (nudgesResponse.ok) {
        const nudgesData = await nudgesResponse.json();
        setNudges(nudgesData.nudges || []);
      }

      // Load achievements
      const achievementsResponse = await fetch(`/api/sales-rep/${repId}/achievements`);
      if (achievementsResponse.ok) {
        const achievementsData = await achievementsResponse.json();
        setAchievements(achievementsData.achievements || []);
      }

    } catch (err) {
      console.error('Failed to load rep data:', err);
    }
  };

  const getArchetypeColor = (archetype: string) => {
    const colors = {
      'achiever': 'secondary',
      'hunter': 'error',
      'relationship_builder': 'primary',
      'analyst': 'success'
    };
    return colors[archetype as keyof typeof colors] || 'default';
  };

  const getArchetypeIcon = (archetype: string) => {
    const icons = {
      'achiever': <Crown sx={{ fontSize: 16 }} />,
      'hunter': <Target sx={{ fontSize: 16 }} />,
      'relationship_builder': <Heart sx={{ fontSize: 16 }} />,
      'analyst': <Brain sx={{ fontSize: 16 }} />
    };
    return icons[archetype as keyof typeof icons] || <Star sx={{ fontSize: 16 }} />;
  };

  const getTierColor = (tier: string) => {
    const colors = {
      'bronze': '#cd7f32',
      'silver': '#c0c0c0',
      'gold': '#ffd700',
      'platinum': '#e5e4e2',
      'diamond': '#b9f2ff'
    };
    return colors[tier as keyof typeof colors] || '#666';
  };

  const getNudgeIntensityColor = (intensity: string) => {
    const colors = {
      'low': 'info',
      'medium': 'warning',
      'high': 'error'
    };
    return colors[intensity as keyof typeof colors] || 'info';
  };

  const getPsychologicalStateColor = (value: number) => {
    if (value >= 0.8) return 'success.main';
    if (value >= 0.6) return 'warning.main';
    if (value >= 0.4) return 'orange.main';
    return 'error.main';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const periodMap = ['monthly', 'quarterly', 'yearly'];
  const currentTarget = targets.find(t => t.period === periodMap[selectedPeriod]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedPeriod(newValue);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <Stack spacing={3}>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h3" component="h1" fontWeight="bold" color="text.primary">
              Sales Rep Centre
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Your personalized sales intelligence dashboard
            </Typography>
          </Box>
          <Stack direction="row" spacing={1}>
            <Button variant="outlined" onClick={loadRepData} startIcon={<ActivityIcon />}>
              Refresh
            </Button>
            <Button variant="outlined" startIcon={<Settings />}>
              Settings
            </Button>
          </Stack>
        </Box>

        {/* Psychological Profile Card */}
        {profile && (
          <Card sx={{ border: 2, borderColor: 'secondary.light', background: 'linear-gradient(45deg, #f3e5f5 30%, #e3f2fd 90%)' }}>
            <CardContent>
              <Typography variant="h6" component="h2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Brain color="secondary" />
                Your Sales Psychology Profile
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={3} textAlign="center">
                  <Box mb={1}>
                    <Chip
                      icon={getArchetypeIcon(profile.archetype)}
                      label={profile.archetype.replace('_', ' ').toUpperCase()}
                      color={getArchetypeColor(profile.archetype) as any}
                      variant="filled"
                    />
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Sales Archetype
                  </Typography>
                </Grid>

                <Grid item xs={12} md={3} textAlign="center">
                  <Typography variant="h4" fontWeight="bold" sx={{ color: getPsychologicalStateColor(profile.psychological_state.motivation_level) }}>
                    {(profile.psychological_state.motivation_level * 100).toFixed(0)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Motivation Level
                  </Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={profile.psychological_state.motivation_level * 100} 
                    sx={{ mt: 1 }}
                  />
                </Grid>

                <Grid item xs={12} md={3} textAlign="center">
                  <Typography variant="h4" fontWeight="bold" sx={{ color: getPsychologicalStateColor(profile.psychological_state.confidence_level) }}>
                    {(profile.psychological_state.confidence_level * 100).toFixed(0)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Confidence Level
                  </Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={profile.psychological_state.confidence_level * 100} 
                    sx={{ mt: 1 }}
                  />
                </Grid>

                <Grid item xs={12} md={3} textAlign="center">
                  <Typography variant="h4" fontWeight="bold" sx={{ color: getPsychologicalStateColor(1 - profile.psychological_state.stress_level) }}>
                    {((1 - profile.psychological_state.stress_level) * 100).toFixed(0)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Wellness Score
                  </Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={(1 - profile.psychological_state.stress_level) * 100} 
                    sx={{ mt: 1 }}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        )}

        {/* Behavioral Nudges */}
        {nudges.length > 0 && (
          <Box>
            <Typography variant="h5" component="h2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Zap color="warning" />
              Smart Insights & Nudges
            </Typography>
            <Stack spacing={2}>
              {nudges.map((nudge) => (
                <Alert 
                  key={nudge.nudge_id} 
                  severity={getNudgeIntensityColor(nudge.intensity) as any}
                  icon={<Bell />}
                >
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                    <Box>
                      <Typography variant="body1" fontWeight="medium">
                        {nudge.message}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Psychology: {nudge.psychological_principle}
                      </Typography>
                    </Box>
                    <Chip label={nudge.intensity} variant="outlined" size="small" sx={{ ml: 1 }} />
                  </Box>
                </Alert>
              ))}
            </Stack>
          </Box>
        )}

        {/* Tabs for Target Periods */}
        <Box>
          <Tabs value={selectedPeriod} onChange={handleTabChange} sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tab label="Monthly Targets" />
            <Tab label="Quarterly Targets" />
            <Tab label="Annual Targets" />
          </Tabs>

          {/* Target Progress */}
          {currentTarget && (
            <Box mt={3}>
              <Card>
                <CardContent>
                  <Typography variant="h6" component="h3" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Target />
                    {periodMap[selectedPeriod].charAt(0).toUpperCase() + periodMap[selectedPeriod].slice(1)} Target Progress
                  </Typography>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={4} textAlign="center">
                      <Box position="relative" display="inline-flex">
                        <CircularProgress
                          variant="determinate"
                          value={currentTarget.completion_percentage}
                          size={120}
                          thickness={4}
                          color={currentTarget.completion_percentage >= 100 ? "success" : 
                                 currentTarget.completion_percentage >= 80 ? "warning" : "error"}
                        />
                        <Box
                          position="absolute"
                          top={0}
                          left={0}
                          bottom={0}
                          right={0}
                          display="flex"
                          alignItems="center"
                          justifyContent="center"
                          flexDirection="column"
                        >
                          <Typography variant="h5" fontWeight="bold">
                            {currentTarget.completion_percentage.toFixed(0)}%
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Complete
                          </Typography>
                        </Box>
                      </Box>
                      <Typography variant="body2" color="text.secondary" mt={2}>
                        Target Achievement
                      </Typography>
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <Stack spacing={2}>
                        <Box>
                          <Box display="flex" justifyContent="space-between" mb={1}>
                            <Typography variant="body2">Current Progress</Typography>
                            <Typography variant="body2" fontWeight="medium">
                              {currentTarget.unit === 'ZAR' 
                                ? formatCurrency(currentTarget.current_value)
                                : currentTarget.current_value.toLocaleString()
                              }
                            </Typography>
                          </Box>
                          <Box display="flex" justifyContent="space-between" mb={1}>
                            <Typography variant="body2">Target</Typography>
                            <Typography variant="body2" fontWeight="medium">
                              {currentTarget.unit === 'ZAR' 
                                ? formatCurrency(currentTarget.target_value)
                                : currentTarget.target_value.toLocaleString()
                              }
                            </Typography>
                          </Box>
                          <LinearProgress 
                            variant="determinate" 
                            value={currentTarget.completion_percentage} 
                            sx={{ height: 8, borderRadius: 4 }}
                          />
                        </Box>

                        <Grid container spacing={2} textAlign="center">
                          <Grid item xs={6}>
                            <Typography variant="h6" fontWeight="bold" color="primary.main">
                              {currentTarget.days_remaining}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Days Left
                            </Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="h6" fontWeight="bold" color="success.main">
                              {currentTarget.unit === 'ZAR' 
                                ? formatCurrency(currentTarget.target_value - currentTarget.current_value)
                                : (currentTarget.target_value - currentTarget.current_value).toLocaleString()
                              }
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              To Go
                            </Typography>
                          </Grid>
                        </Grid>
                      </Stack>
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                        Psychological Insights
                      </Typography>
                      <Stack spacing={1}>
                        <Box display="flex" justifyContent="space-between">
                          <Typography variant="body2">Stress Level:</Typography>
                          <Typography variant="body2" sx={{ color: getPsychologicalStateColor(1 - (profile?.psychological_state.stress_level || 0)) }}>
                            {profile ? ((1 - profile.psychological_state.stress_level) * 100).toFixed(0) : 0}%
                          </Typography>
                        </Box>
                        <Box display="flex" justifyContent="space-between">
                          <Typography variant="body2">Motivation:</Typography>
                          <Typography variant="body2" sx={{ color: getPsychologicalStateColor(profile?.psychological_state.motivation_level || 0) }}>
                            {profile ? (profile.psychological_state.motivation_level * 100).toFixed(0) : 0}%
                          </Typography>
                        </Box>
                        <Box display="flex" justifyContent="space-between">
                          <Typography variant="body2">Confidence:</Typography>
                          <Typography variant="body2" sx={{ color: getPsychologicalStateColor(profile?.psychological_state.confidence_level || 0) }}>
                            {profile ? (profile.psychological_state.confidence_level * 100).toFixed(0) : 0}%
                          </Typography>
                        </Box>
                      </Stack>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Box>
          )}

          {/* Achievements */}
          <Box mt={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="h3" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Trophy />
                  Recent Achievements
                </Typography>
                {achievements.length === 0 ? (
                  <Box textAlign="center" py={4}>
                    <Award sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
                    <Typography variant="h6" fontWeight="medium" gutterBottom>
                      No Achievements Yet
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      Start hitting your targets to unlock achievements!
                    </Typography>
                  </Box>
                ) : (
                  <Grid container spacing={2}>
                    {achievements.slice(0, 6).map((achievement) => (
                      <Grid item xs={12} md={6} lg={4} key={achievement.achievement_id}>
                        <Paper 
                          elevation={1} 
                          sx={{ p: 2, '&:hover': { boxShadow: 3 }, transition: 'box-shadow 0.3s' }}
                        >
                          <Box display="flex" alignItems="center" gap={2} mb={1}>
                            <Typography variant="h4" sx={{ color: getTierColor(achievement.tier) }}>
                              {achievement.icon}
                            </Typography>
                            <Box>
                              <Typography variant="subtitle1" fontWeight="medium">
                                {achievement.name}
                              </Typography>
                              <Chip 
                                label={achievement.tier} 
                                size="small" 
                                sx={{ color: getTierColor(achievement.tier) }}
                              />
                            </Box>
                          </Box>
                          <Typography variant="body2" color="text.secondary" mb={1}>
                            {achievement.description}
                          </Typography>
                          <Box display="flex" justifyContent="space-between" alignItems="center">
                            <Typography variant="caption" color="secondary.main">
                              +{achievement.xp_reward} XP
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {new Date(achievement.unlocked_at).toLocaleDateString()}
                            </Typography>
                          </Box>
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                )}
              </CardContent>
            </Card>
          </Box>
        </Box>
      </Stack>
    </Container>
  );
};

export default SalesRepCentre;
