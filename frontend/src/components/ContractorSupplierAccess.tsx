import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  Typography,
  Button,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Alert,
  Box,
  Grid,
  Rating,
  Divider
} from '@mui/material';
import {
  Search,
  People as Users,
  LocationOn as MapPin,
  Star,
  FilterList as Filter,
  Send,
  Schedule as Clock,
  CheckCircle,
  Warning as AlertCircle,
  Business as Building,
  EmojiEvents as Award,
  TrendingUp
} from '@mui/icons-material';
import usePsychologicalSystems from '../hooks/usePsychologicalSystems';

interface Supplier {
  supplier_id: string;
  company_name: string;
  specializations: string[];
  location: {
    city: string;
    province: string;
  };
  rating: number;
  bbbee_level: number;
  certifications: string[];
  capacity: {
    monthly_volume: number;
    project_size: string;
  };
  match_score: number;
  distance_km: number;
  response_time_avg: string;
  quote_acceptance_rate: number;
}

interface QuoteRequest {
  supplier_id: string;
  category: string;
  specifications: string;
  quantity: number;
  delivery_location: string;
  delivery_deadline: string;
  budget_range: {
    min_budget: number;
    max_budget: number;
  };
  special_requirements: string[];
}

const ContractorSupplierAccess: React.FC = () => {
  const { findSuppliers, requestQuote, loading, error } = usePsychologicalSystems();
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [searchCriteria, setSearchCriteria] = useState({
    category: '',
    location: '',
    max_distance: 50,
    min_bbbee_level: 1,
    min_rating: 0
  });
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);
  const [quoteRequest, setQuoteRequest] = useState<Partial<QuoteRequest>>({});
  const [activeTab, setActiveTab] = useState(0);

  const contractorId = 'current-contractor-id'; // Would get from auth context

  const handleSearch = async () => {
    try {
      const results = await findSuppliers(contractorId, searchCriteria);
      setSuppliers(results);
      setActiveTab(1);
    } catch (err) {
      console.error('Search failed:', err);
    }
  };

  const handleRequestQuote = async () => {
    if (!selectedSupplier) return;

    try {
      const request = {
        ...quoteRequest,
        supplier_id: selectedSupplier.supplier_id
      };
      
      await requestQuote(contractorId, request);
      setActiveTab(3);
      setSelectedSupplier(null);
      setQuoteRequest({});
    } catch (err) {
      console.error('Quote request failed:', err);
    }
  };

  const getBBBEEColor = (level: number) => {
    if (level <= 2) return 'success';
    if (level <= 4) return 'info';
    if (level <= 6) return 'warning';
    return 'default';
  };

  const getMatchScoreColor = (score: number) => {
    if (score >= 80) return 'success.main';
    if (score >= 60) return 'warning.main';
    return 'error.main';
  };

  return (
    <Box sx={{ maxWidth: '1200px', mx: 'auto', p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            Supplier Network Access
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Find and connect with verified suppliers for your projects
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Chip
            icon={<Users />}
            label="Premium Access"
            variant="outlined"
            color="primary"
          />
          <Chip
            icon={<CheckCircle />}
            label="25 Quotes/Month"
            variant="outlined"
            color="success"
          />
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Box sx={{ width: '100%' }}>
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
          <Tab label="Search Suppliers" />
          <Tab label={`Results (${suppliers.length})`} />
          <Tab label="Request Quote" />
          <Tab label="My Requests" />
        </Tabs>

        {/* Search Tab */}
        {activeTab === 0 && (
          <Box sx={{ mt: 3 }}>
            <Card>
              <CardHeader
                title={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Search />
                    <Typography variant="h6">Find Suppliers</Typography>
                  </Box>
                }
              />
              <CardContent>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>Category</InputLabel>
                      <Select
                        value={searchCriteria.category}
                        label="Category"
                        onChange={(e) => setSearchCriteria(prev => ({ ...prev, category: e.target.value }))}
                      >
                        <MenuItem value="construction">Construction Materials</MenuItem>
                        <MenuItem value="electrical">Electrical Supplies</MenuItem>
                        <MenuItem value="plumbing">Plumbing Supplies</MenuItem>
                        <MenuItem value="equipment">Equipment Rental</MenuItem>
                        <MenuItem value="services">Professional Services</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Location"
                      placeholder="City or Province"
                      value={searchCriteria.location}
                      onChange={(e) => setSearchCriteria(prev => ({ ...prev, location: e.target.value }))}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>Max Distance</InputLabel>
                      <Select
                        value={searchCriteria.max_distance.toString()}
                        label="Max Distance"
                        onChange={(e) => setSearchCriteria(prev => ({ ...prev, max_distance: parseInt(e.target.value) }))}
                      >
                        <MenuItem value="25">25 km</MenuItem>
                        <MenuItem value="50">50 km</MenuItem>
                        <MenuItem value="100">100 km</MenuItem>
                        <MenuItem value="250">250 km</MenuItem>
                        <MenuItem value="500">500 km</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Min B-BBEE Level</InputLabel>
                      <Select
                        value={searchCriteria.min_bbbee_level.toString()}
                        label="Min B-BBEE Level"
                        onChange={(e) => setSearchCriteria(prev => ({ ...prev, min_bbbee_level: parseInt(e.target.value) }))}
                      >
                        <MenuItem value="1">Level 1</MenuItem>
                        <MenuItem value="2">Level 2</MenuItem>
                        <MenuItem value="3">Level 3</MenuItem>
                        <MenuItem value="4">Level 4</MenuItem>
                        <MenuItem value="8">Any Level</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Min Rating</InputLabel>
                      <Select
                        value={searchCriteria.min_rating.toString()}
                        label="Min Rating"
                        onChange={(e) => setSearchCriteria(prev => ({ ...prev, min_rating: parseFloat(e.target.value) }))}
                      >
                        <MenuItem value="0">Any Rating</MenuItem>
                        <MenuItem value="3">3+ Stars</MenuItem>
                        <MenuItem value="4">4+ Stars</MenuItem>
                        <MenuItem value="4.5">4.5+ Stars</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>

                <Button
                  fullWidth
                  variant="contained"
                  onClick={handleSearch}
                  disabled={loading || !searchCriteria.category}
                  startIcon={<Search />}
                  sx={{ mt: 3 }}
                >
                  {loading ? 'Searching...' : 'Find Suppliers'}
                </Button>
              </CardContent>
            </Card>
          </Box>
        )}

        {/* Results Tab */}
        {activeTab === 1 && (
          <Box sx={{ mt: 3 }}>
            {suppliers.length === 0 ? (
              <Card>
                <CardContent sx={{ textAlign: 'center', py: 8 }}>
                  <Search sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    No suppliers found
                  </Typography>
                  <Typography color="text.secondary">
                    Try adjusting your search criteria
                  </Typography>
                </CardContent>
              </Card>
            ) : (
              <Grid container spacing={3}>
                {suppliers.map((supplier) => (
                  <Grid item xs={12} lg={6} key={supplier.supplier_id}>
                    <Card sx={{ height: '100%', '&:hover': { boxShadow: 4 } }}>
                      <CardContent sx={{ p: 3 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                          <Box>
                            <Typography variant="h6" gutterBottom>
                              {supplier.company_name}
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <MapPin sx={{ fontSize: 16, color: 'text.secondary' }} />
                              <Typography variant="body2" color="text.secondary">
                                {supplier.location.city}, {supplier.location.province} ({supplier.distance_km}km)
                              </Typography>
                            </Box>
                          </Box>
                          <Box sx={{ textAlign: 'right' }}>
                            <Typography
                              variant="h6"
                              fontWeight="bold"
                              sx={{ color: getMatchScoreColor(supplier.match_score) }}
                            >
                              {supplier.match_score}% Match
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <Rating value={supplier.rating} readOnly size="small" />
                              <Typography variant="body2" fontWeight="medium">
                                {supplier.rating}
                              </Typography>
                            </Box>
                          </Box>
                        </Box>

                        <Box sx={{ mb: 2 }}>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
                            {supplier.specializations.slice(0, 3).map((spec) => (
                              <Chip key={spec} label={spec} size="small" variant="outlined" />
                            ))}
                          </Box>
                        </Box>

                        <Grid container spacing={2} sx={{ mb: 2 }}>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="text.secondary">
                              B-BBEE Level:
                            </Typography>
                            <Chip
                              label={`Level ${supplier.bbbee_level}`}
                              size="small"
                              color={getBBBEEColor(supplier.bbbee_level)}
                            />
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="text.secondary">
                              Response Time:
                            </Typography>
                            <Typography variant="body2" fontWeight="medium">
                              {supplier.response_time_avg}
                            </Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="text.secondary">
                              Acceptance Rate:
                            </Typography>
                            <Typography variant="body2" fontWeight="medium">
                              {supplier.quote_acceptance_rate}%
                            </Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="text.secondary">
                              Capacity:
                            </Typography>
                            <Typography variant="body2" fontWeight="medium">
                              {supplier.capacity.project_size}
                            </Typography>
                          </Grid>
                        </Grid>

                        <Box sx={{ display: 'flex', gap: 1, pt: 1 }}>
                          <Button
                            variant="outlined"
                            size="small"
                            fullWidth
                            startIcon={<Send />}
                            onClick={() => {
                              setSelectedSupplier(supplier);
                              setActiveTab(2);
                            }}
                          >
                            Request Quote
                          </Button>
                          <Button variant="text" size="small">
                            View Profile
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        )}

        {/* Quote Tab */}
        {activeTab === 2 && (
          <Box sx={{ mt: 3 }}>
            {selectedSupplier ? (
              <Card>
                <CardHeader
                  title={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Send />
                      <Typography variant="h6">
                        Request Quote from {selectedSupplier.company_name}
                      </Typography>
                    </Box>
                  }
                />
                <CardContent>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Category"
                        value={quoteRequest.category || ''}
                        onChange={(e) => setQuoteRequest(prev => ({ ...prev, category: e.target.value }))}
                        placeholder="e.g., Construction Materials"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Quantity"
                        type="number"
                        value={quoteRequest.quantity || ''}
                        onChange={(e) => setQuoteRequest(prev => ({ ...prev, quantity: parseInt(e.target.value) }))}
                        placeholder="e.g., 100"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Specifications"
                        multiline
                        rows={4}
                        value={quoteRequest.specifications || ''}
                        onChange={(e) => setQuoteRequest(prev => ({ ...prev, specifications: e.target.value }))}
                        placeholder="Detailed specifications and requirements..."
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Delivery Location"
                        value={quoteRequest.delivery_location || ''}
                        onChange={(e) => setQuoteRequest(prev => ({ ...prev, delivery_location: e.target.value }))}
                        placeholder="Delivery address"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Delivery Deadline"
                        type="date"
                        value={quoteRequest.delivery_deadline || ''}
                        onChange={(e) => setQuoteRequest(prev => ({ ...prev, delivery_deadline: e.target.value }))}
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Min Budget (ZAR)"
                        type="number"
                        value={quoteRequest.budget_range?.min_budget || ''}
                        onChange={(e) => setQuoteRequest(prev => ({
                          ...prev,
                          budget_range: {
                            min_budget: parseInt(e.target.value),
                            max_budget: prev.budget_range?.max_budget || 0
                          }
                        }))}
                        placeholder="Minimum budget"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Max Budget (ZAR)"
                        type="number"
                        value={quoteRequest.budget_range?.max_budget || ''}
                        onChange={(e) => setQuoteRequest(prev => ({
                          ...prev,
                          budget_range: {
                            min_budget: prev.budget_range?.min_budget || 0,
                            max_budget: parseInt(e.target.value)
                          }
                        }))}
                        placeholder="Maximum budget"
                      />
                    </Grid>
                  </Grid>

                  <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                    <Button
                      variant="contained"
                      onClick={handleRequestQuote}
                      disabled={loading}
                      startIcon={<Send />}
                      fullWidth
                    >
                      {loading ? 'Sending...' : 'Send Quote Request'}
                    </Button>
                    <Button variant="outlined" onClick={() => setSelectedSupplier(null)}>
                      Cancel
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent sx={{ textAlign: 'center', py: 8 }}>
                  <Send sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    No supplier selected
                  </Typography>
                  <Typography color="text.secondary">
                    Select a supplier from the results to request a quote
                  </Typography>
                </CardContent>
              </Card>
            )}
          </Box>
        )}

        {/* Requests Tab */}
        {activeTab === 3 && (
          <Box sx={{ mt: 3 }}>
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 8 }}>
                <Clock sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Quote Requests
                </Typography>
                <Typography color="text.secondary">
                  Your quote requests will appear here
                </Typography>
              </CardContent>
            </Card>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default ContractorSupplierAccess;
