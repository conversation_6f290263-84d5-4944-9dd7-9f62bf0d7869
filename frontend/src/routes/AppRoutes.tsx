import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../../../src/hooks/useAuth';

// Available components in frontend
import ProductSpecifications from '../pages/ProductSpecifications';

// Main src components (using correct paths from root)
import Dashboard from '../../../src/pages/dashboard/Dashboard';
import BidManagement from '../../../src/pages/bids/BidManagement';
import GamificationHub from '../../../src/pages/gamification/GamificationHub';
import TenderDiscovery from '../../../src/pages/tenders/TenderDiscovery';

// Psychological components from main src
import SalesRepCentre from '../../../src/components/psychological/SalesRepCentre';
import SalesRepSelfOnboarding from '../../../src/components/psychological/SalesRepSelfOnboarding';
import ContractorSupplierAccess from '../../../src/components/psychological/ContractorSupplierAccess';

// Authentication components
import Login from '../../../src/pages/auth/Login';
import Register from '../../../src/pages/auth/Register';

// Compliance components
import ComplianceOverview from '../../../src/pages/compliance/ComplianceOverview';
import ProtestWizard from '../../../src/pages/compliance/ProtestWizard';

const AppRoutes: React.FC = () => {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <Routes>
      {/* Public Routes */}
      <Route path="/auth/login" element={<Login />} />
      <Route path="/auth/register" element={<Register />} />

      {/* Sales Rep Self-Onboarding (Public) */}
      <Route path="/sales-rep/onboard" element={<SalesRepSelfOnboarding />} />

      {/* Protected Routes */}
      {user ? (
        <>
          {/* Main Dashboard */}
          <Route path="/" element={<Dashboard />} />
          
          {/* Existing BidBeez Features */}
          <Route path="/tenders" element={<TenderDiscovery />} />
          <Route path="/bids" element={<BidManagement />} />
          <Route path="/compliance" element={<ComplianceOverview />} />
          <Route path="/compliance/protest" element={<ProtestWizard />} />
          <Route path="/gamification" element={<GamificationHub />} />
          <Route path="/product-specs" element={<ProductSpecifications />} />
          
          {/* NEW: Psychological Systems */}
          <Route path="/sales-rep-centre" element={<SalesRepCentre />} />
          <Route path="/contractor-supplier" element={<ContractorSupplierAccess />} />
          
          {/* User Profile & Settings */}
          <Route path="/profile" element={<div>Profile Page</div>} />
          <Route path="/settings" element={<div>Settings Page</div>} />
          
          {/* Catch all - redirect to dashboard */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </>
      ) : (
        /* Redirect unauthenticated users to login */
        <Route path="*" element={<Navigate to="/auth/login" replace />} />
      )}
    </Routes>
  );
};

export default AppRoutes;
