import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Button,
  Chip,
  Tabs,
  Tab,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  CircularProgress,
  Paper
} from '@mui/material';
import {
  Description,
  Search,
  Settings,
  BarChart,
  People,
  CheckCircle,
  Schedule,
  Warning,
  Add,
  FilterList
} from '@mui/icons-material';

import SpecificationParser from '../components/SpecificationParser';
import SupplierMatchingDashboard from '../components/SupplierMatchingDashboard';

interface ParsedSpecification {
  id: string;
  tender_id?: string;
  project_name?: string;
  tender_number?: string;
  province: string;
  parsing_status: string;
  confidence_score: number;
  created_at: string;
  boq_items_count?: number;
  sow_requirements_count?: number;
  standards_count?: number;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const ProductSpecifications: React.FC = () => {
  const [specifications, setSpecifications] = useState<ParsedSpecification[]>([]);
  const [selectedSpec, setSelectedSpec] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    status: '',
    province: '',
    search: ''
  });

  useEffect(() => {
    loadSpecifications();
  }, [filters]);

  const loadSpecifications = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (filters.status) params.append('status', filters.status);
      if (filters.province) params.append('province', filters.province);
      
      const response = await fetch(`/api/specifications?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        let specs = data.specifications || [];
        
        // Apply search filter
        if (filters.search) {
          specs = specs.filter((spec: ParsedSpecification) => 
            spec.project_name?.toLowerCase().includes(filters.search.toLowerCase()) ||
            spec.tender_number?.toLowerCase().includes(filters.search.toLowerCase())
          );
        }
        
        setSpecifications(specs);
      }
    } catch (err) {
      console.error('Failed to load specifications:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'processing': return 'info';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle sx={{ fontSize: 16 }} />;
      case 'processing': return <Schedule sx={{ fontSize: 16 }} />;
      case 'failed': return <Warning sx={{ fontSize: 16 }} />;
      default: return <Schedule sx={{ fontSize: 16 }} />;
    }
  };

  const getConfidenceColor = (score: number) => {
    if (score >= 0.8) return 'success.main';
    if (score >= 0.6) return 'warning.main';
    return 'error.main';
  };

  const stats = {
    total: specifications.length,
    completed: specifications.filter(s => s.parsing_status === 'completed').length,
    processing: specifications.filter(s => s.parsing_status === 'processing').length,
    avgConfidence: specifications.length > 0 
      ? specifications.reduce((sum, s) => sum + s.confidence_score, 0) / specifications.length 
      : 0
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* Header */}
      <Paper sx={{ borderRadius: 0, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ maxWidth: '1200px', mx: 'auto', px: 3, py: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold', color: 'text.primary' }}>
                Product Specifications
              </Typography>
              <Typography variant="body1" color="text.secondary">
                AI-powered tender document parsing and supplier matching
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button 
                variant="outlined" 
                onClick={() => setActiveTab(0)}
                sx={{ bgcolor: activeTab === 0 ? 'primary.light' : 'transparent' }}
                startIcon={<Add />}
              >
                Parse Document
              </Button>
              <Button 
                variant="outlined"
                onClick={() => setActiveTab(2)}
                sx={{ bgcolor: activeTab === 2 ? 'primary.light' : 'transparent' }}
                startIcon={<Description />}
              >
                Specification Library
              </Button>
            </Box>
          </Box>
        </Box>
      </Paper>

      <Box sx={{ maxWidth: '1200px', mx: 'auto', p: 3 }}>
        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ pt: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                      Total Specifications
                    </Typography>
                    <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'text.primary' }}>
                      {stats.total}
                    </Typography>
                  </Box>
                  <Description sx={{ fontSize: 32, color: 'primary.main' }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ pt: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                      Completed
                    </Typography>
                    <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                      {stats.completed}
                    </Typography>
                  </Box>
                  <CheckCircle sx={{ fontSize: 32, color: 'success.main' }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ pt: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                      Processing
                    </Typography>
                    <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                      {stats.processing}
                    </Typography>
                  </Box>
                  <Schedule sx={{ fontSize: 32, color: 'info.main' }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ pt: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                      Avg Confidence
                    </Typography>
                    <Typography 
                      variant="h4" 
                      sx={{ 
                        fontWeight: 'bold', 
                        color: getConfidenceColor(stats.avgConfidence)
                      }}
                    >
                      {(stats.avgConfidence * 100).toFixed(0)}%
                    </Typography>
                  </Box>
                  <BarChart sx={{ fontSize: 32, color: 'secondary.main' }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Main Content */}
        <Box sx={{ width: '100%' }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={activeTab} onChange={handleTabChange} aria-label="specification tabs">
              <Tab 
                icon={<Description />} 
                label="Document Parser" 
                iconPosition="start"
              />
              <Tab 
                icon={<Search />} 
                label="Supplier Matching" 
                iconPosition="start"
              />
              <Tab 
                icon={<Settings />} 
                label="Specification Library" 
                iconPosition="start"
              />
            </Tabs>
          </Box>

          <TabPanel value={activeTab} index={0}>
            <SpecificationParser />
          </TabPanel>

          <TabPanel value={activeTab} index={1}>
            {selectedSpec ? (
              <SupplierMatchingDashboard specificationId={selectedSpec} />
            ) : (
              <Card>
                <CardContent sx={{ pt: 3, textAlign: 'center' }}>
                  <People sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
                  <Typography variant="h6" sx={{ fontWeight: 500, mb: 1 }}>
                    Select a Specification
                  </Typography>
                  <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                    Choose a parsed specification from the library to run supplier matching
                  </Typography>
                  <Button variant="contained" onClick={() => setActiveTab(2)}>
                    Browse Specifications
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabPanel>

          <TabPanel value={activeTab} index={2}>
            <Card>
              <CardHeader
                title="Specification Library"
                action={
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <TextField
                      placeholder="Search specifications..."
                      value={filters.search}
                      onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                      size="small"
                      sx={{ width: 250 }}
                    />
                    <FormControl size="small" sx={{ minWidth: 120 }}>
                      <InputLabel>Status</InputLabel>
                      <Select
                        value={filters.status}
                        label="Status"
                        onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                      >
                        <MenuItem value="">All Status</MenuItem>
                        <MenuItem value="completed">Completed</MenuItem>
                        <MenuItem value="processing">Processing</MenuItem>
                        <MenuItem value="failed">Failed</MenuItem>
                      </Select>
                    </FormControl>
                    <FormControl size="small" sx={{ minWidth: 140 }}>
                      <InputLabel>Province</InputLabel>
                      <Select
                        value={filters.province}
                        label="Province"
                        onChange={(e) => setFilters(prev => ({ ...prev, province: e.target.value }))}
                      >
                        <MenuItem value="">All Provinces</MenuItem>
                        <MenuItem value="Gauteng">Gauteng</MenuItem>
                        <MenuItem value="Western Cape">Western Cape</MenuItem>
                        <MenuItem value="KwaZulu-Natal">KwaZulu-Natal</MenuItem>
                        <MenuItem value="Eastern Cape">Eastern Cape</MenuItem>
                        <MenuItem value="Free State">Free State</MenuItem>
                        <MenuItem value="Limpopo">Limpopo</MenuItem>
                        <MenuItem value="Mpumalanga">Mpumalanga</MenuItem>
                        <MenuItem value="North West">North West</MenuItem>
                        <MenuItem value="Northern Cape">Northern Cape</MenuItem>
                      </Select>
                    </FormControl>
                  </Box>
                }
              />
              <CardContent>
                {loading ? (
                  <Box sx={{ textAlign: 'center', py: 4 }}>
                    <CircularProgress />
                    <Typography variant="body1" color="text.secondary" sx={{ mt: 1 }}>
                      Loading specifications...
                    </Typography>
                  </Box>
                ) : specifications.length === 0 ? (
                  <Box sx={{ textAlign: 'center', py: 4 }}>
                    <Description sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
                    <Typography variant="h6" sx={{ fontWeight: 500, mb: 1 }}>
                      No Specifications Found
                    </Typography>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                      Start by parsing your first tender document
                    </Typography>
                    <Button variant="contained" onClick={() => setActiveTab(0)} startIcon={<Add />}>
                      Parse Document
                    </Button>
                  </Box>
                ) : (
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {specifications.map((spec) => (
                      <Paper
                        key={spec.id}
                        sx={{
                          p: 2,
                          cursor: 'pointer',
                          transition: 'all 0.2s',
                          border: selectedSpec === spec.id ? 2 : 1,
                          borderColor: selectedSpec === spec.id ? 'primary.main' : 'divider',
                          bgcolor: selectedSpec === spec.id ? 'primary.light' : 'background.paper',
                          '&:hover': {
                            boxShadow: 2
                          }
                        }}
                        onClick={() => setSelectedSpec(spec.id)}
                      >
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                          <Box>
                            <Typography variant="h6" sx={{ fontWeight: 600 }}>
                              {spec.project_name || 'Untitled Project'}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {spec.tender_number && `Tender: ${spec.tender_number} • `}
                              Province: {spec.province}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Chip 
                              icon={getStatusIcon(spec.parsing_status)}
                              label={spec.parsing_status}
                              color={getStatusColor(spec.parsing_status) as any}
                              size="small"
                            />
                            <Typography 
                              variant="h6" 
                              sx={{ 
                                fontWeight: 'bold', 
                                color: getConfidenceColor(spec.confidence_score)
                              }}
                            >
                              {(spec.confidence_score * 100).toFixed(0)}%
                            </Typography>
                          </Box>
                        </Box>

                        <Grid container spacing={2} sx={{ mb: 2 }}>
                          <Grid item xs={4}>
                            <Typography variant="body2" color="text.secondary">
                              <strong>BOQ Items:</strong> {spec.boq_items_count || 0}
                            </Typography>
                          </Grid>
                          <Grid item xs={4}>
                            <Typography variant="body2" color="text.secondary">
                              <strong>SOW Requirements:</strong> {spec.sow_requirements_count || 0}
                            </Typography>
                          </Grid>
                          <Grid item xs={4}>
                            <Typography variant="body2" color="text.secondary">
                              <strong>Standards:</strong> {spec.standards_count || 0}
                            </Typography>
                          </Grid>
                        </Grid>

                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="caption" color="text.secondary">
                            Created: {new Date(spec.created_at).toLocaleDateString()}
                          </Typography>
                          <Button 
                            variant="outlined" 
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedSpec(spec.id);
                              setActiveTab(1);
                            }}
                            startIcon={<Search />}
                          >
                            Match Suppliers
                          </Button>
                        </Box>
                      </Paper>
                    ))}
                  </Box>
                )}
              </CardContent>
            </Card>
          </TabPanel>
        </Box>
      </Box>
    </Box>
  );
};

export default ProductSpecifications;
