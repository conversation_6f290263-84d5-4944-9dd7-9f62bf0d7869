<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - User Profile</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .profile-card { transition: all 0.3s ease; }
        .profile-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .skill-badge { transition: all 0.3s ease; }
        .skill-badge:hover { transform: scale(1.05); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="/dashboard.html" class="text-blue-600 hover:text-blue-800 mr-4">← Back to Dashboard</a>
                    <h1 class="text-3xl font-bold text-blue-600">👤 User Profile</h1>
                    <span class="ml-3 text-sm text-gray-500">Personal Settings & Information</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-green-600">🧠 Psychological Profile: Active</span>
                    <button onclick="saveProfile()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">💾 Save Changes</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Profile Overview -->
            <div class="lg:col-span-1">
                <div class="profile-card bg-white rounded-lg shadow p-6">
                    <div class="text-center mb-6">
                        <div class="w-32 h-32 bg-blue-500 rounded-full flex items-center justify-center text-white text-4xl font-bold mx-auto mb-4">
                            JD
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900">John Doe</h2>
                        <p class="text-gray-600">Senior Bid Manager</p>
                        <p class="text-sm text-gray-500">Member since Jan 2023</p>
                    </div>
                    
                    <!-- Quick Stats -->
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">68%</div>
                            <div class="text-xs text-gray-600">Success Rate</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">47</div>
                            <div class="text-xs text-gray-600">Total Bids</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">R8.4M</div>
                            <div class="text-xs text-gray-600">Value Won</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-orange-600">8.7/10</div>
                            <div class="text-xs text-gray-600">Psych Score</div>
                        </div>
                    </div>

                    <!-- Psychological Profile -->
                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                        <h3 class="font-medium text-purple-900 mb-2">🧠 Psychological Profile</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between text-sm">
                                <span>Analytical Thinking</span>
                                <span class="font-medium">92%</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span>Risk Assessment</span>
                                <span class="font-medium">87%</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span>Strategic Planning</span>
                                <span class="font-medium">89%</span>
                            </div>
                        </div>
                        <div class="mt-3 text-center">
                            <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
                                Analytical Bidder
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Details -->
            <div class="lg:col-span-2">
                <div class="space-y-6">
                    <!-- Personal Information -->
                    <div class="profile-card bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-900">📋 Personal Information</h2>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                    <input type="text" value="John" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                    <input type="text" value="Doe" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                    <input type="email" value="<EMAIL>" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                                    <input type="tel" value="+27 11 123 4567" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Company</label>
                                    <input type="text" value="ABC Construction Ltd" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Bio</label>
                                    <textarea rows="3" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Tell us about yourself...">Experienced bid manager with 10+ years in construction and IT sectors. Specialized in large-scale government tenders and strategic partnerships.</textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Skills & Certifications -->
                    <div class="profile-card bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-900">🎓 Skills & Certifications</h2>
                        </div>
                        <div class="p-6">
                            <div class="mb-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-3">Verified Skills</h3>
                                <div class="flex flex-wrap gap-2">
                                    <span class="skill-badge bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">Project Management (PMP)</span>
                                    <span class="skill-badge bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">BBBEE Compliance</span>
                                    <span class="skill-badge bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">Quality Management (ISO 9001)</span>
                                    <span class="skill-badge bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium">Construction Management</span>
                                    <span class="skill-badge bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm font-medium">Risk Management</span>
                                </div>
                            </div>
                            
                            <div class="mb-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-3">Skills in Progress</h3>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm font-medium">Advanced IT Security (CISSP)</span>
                                        <div class="flex items-center">
                                            <div class="w-32 bg-gray-200 rounded-full h-2 mr-2">
                                                <div class="bg-blue-600 h-2 rounded-full" style="width: 65%"></div>
                                            </div>
                                            <span class="text-sm text-gray-600">65%</span>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm font-medium">Environmental Management</span>
                                        <div class="flex items-center">
                                            <div class="w-32 bg-gray-200 rounded-full h-2 mr-2">
                                                <div class="bg-green-600 h-2 rounded-full" style="width: 30%"></div>
                                            </div>
                                            <span class="text-sm text-gray-600">30%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <button onclick="manageSkills()" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">🎓 Manage Skills</button>
                        </div>
                    </div>

                    <!-- Preferences -->
                    <div class="profile-card bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-900">⚙️ Preferences</h2>
                        </div>
                        <div class="p-6">
                            <div class="space-y-6">
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-3">Notification Preferences</h3>
                                    <div class="space-y-3">
                                        <label class="flex items-center">
                                            <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-gray-700">Email notifications for new tenders</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-gray-700">WhatsApp alerts for urgent bids</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-gray-700">SMS notifications for deadlines</span>
                                        </label>
                                    </div>
                                </div>

                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-3">AI Optimization</h3>
                                    <div class="space-y-3">
                                        <label class="flex items-center">
                                            <input type="checkbox" checked class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                                            <span class="ml-2 text-sm text-gray-700">Enable psychological profiling</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" checked class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                                            <span class="ml-2 text-sm text-gray-700">Auto-optimize bid recommendations</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                                            <span class="ml-2 text-sm text-gray-700">Share anonymous data for AI improvement</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function saveProfile() {
            alert('💾 Profile saved successfully!\n\n✅ Personal information updated\n✅ Skills and certifications synced\n✅ Preferences applied\n✅ Psychological profile recalibrated\n\nYour profile is now optimized for better bid matching!');
        }

        function manageSkills() {
            alert('🎓 Opening SkillSync Dashboard...\n\nYou can:\n• Add new skills and certifications\n• Track learning progress\n• Find training opportunities\n• Verify existing skills\n• Connect with training providers');
            setTimeout(() => {
                window.open('/skillsync-dashboard.html', '_blank');
            }, 1500);
        }

        // Simulate real-time profile updates
        setInterval(() => {
            console.log(`[${new Date().toLocaleTimeString()}] User Profile: Monitoring skill progress and psychological optimization`);
        }, 30000);
    </script>
</body>
</html>
