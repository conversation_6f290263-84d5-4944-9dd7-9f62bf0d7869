<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - Tool Marketplace</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .marketplace-card { transition: all 0.3s ease; }
        .marketplace-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .featured-badge { animation: pulse 2s infinite; }
        .hot-deal { border: 2px solid #F59E0B; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="/toolsync-dashboard.html" class="text-orange-600 hover:text-orange-800 mr-4">← Back to ToolSync</a>
                    <h1 class="text-3xl font-bold text-orange-600">🛒 Tool Marketplace</h1>
                    <span class="ml-3 text-sm text-gray-500">Rent, Share & Purchase Equipment</span>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="listMyTools()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">💰 List My Tools</button>
                    <button onclick="viewMyListings()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">📋 My Listings</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Search and Filters -->
    <div class="bg-white border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex flex-wrap items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <input type="text" placeholder="Search tools and equipment..." class="border border-gray-300 rounded px-4 py-2 text-sm w-80" id="search-input">
                    <button onclick="searchMarketplace()" class="bg-orange-600 text-white px-6 py-2 rounded hover:bg-orange-700">🔍 Search</button>
                </div>
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">Type:</label>
                    <select id="type-filter" class="border border-gray-300 rounded px-3 py-2 text-sm" onchange="filterMarketplace()">
                        <option value="all">All Types</option>
                        <option value="rent">For Rent</option>
                        <option value="share">Tool Sharing</option>
                        <option value="purchase">For Sale</option>
                    </select>
                </div>
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">Location:</label>
                    <select id="location-filter" class="border border-gray-300 rounded px-3 py-2 text-sm" onchange="filterMarketplace()">
                        <option value="all">All Locations</option>
                        <option value="johannesburg">Johannesburg</option>
                        <option value="cape-town">Cape Town</option>
                        <option value="durban">Durban</option>
                        <option value="pretoria">Pretoria</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Featured Deals -->
        <div class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-900 mb-6">🔥 Featured Deals</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Featured Deal 1 -->
                <div class="marketplace-card bg-white rounded-lg shadow hot-deal">
                    <div class="relative">
                        <div class="h-48 bg-gradient-to-r from-blue-400 to-blue-600 rounded-t-lg flex items-center justify-center">
                            <div class="text-6xl text-white">🏗️</div>
                        </div>
                        <div class="absolute top-4 right-4 featured-badge bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                            HOT DEAL
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Caterpillar 320 Excavator</h3>
                        <p class="text-gray-600 mb-4">20-ton excavator, perfect for construction projects</p>
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <div class="text-2xl font-bold text-green-600">R8,500/day</div>
                                <div class="text-sm text-gray-500">R45,000/month</div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-gray-600">📍 Johannesburg</div>
                                <div class="text-sm text-green-600">✅ Available Now</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">BC</div>
                                <div class="ml-2">
                                    <div class="text-sm font-medium">BuildCorp Ltd</div>
                                    <div class="text-xs text-gray-500">⭐ 4.9 (127 reviews)</div>
                                </div>
                            </div>
                            <button onclick="rentTool('excavator-001')" class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700">Rent Now</button>
                        </div>
                    </div>
                </div>

                <!-- Featured Deal 2 -->
                <div class="marketplace-card bg-white rounded-lg shadow">
                    <div class="relative">
                        <div class="h-48 bg-gradient-to-r from-purple-400 to-purple-600 rounded-t-lg flex items-center justify-center">
                            <div class="text-6xl text-white">💻</div>
                        </div>
                        <div class="absolute top-4 right-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                            SHARE
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Network Testing Suite</h3>
                        <p class="text-gray-600 mb-4">Complete network analysis and testing equipment</p>
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <div class="text-2xl font-bold text-blue-600">R150/day</div>
                                <div class="text-sm text-gray-500">Tool sharing network</div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-gray-600">📍 Cape Town</div>
                                <div class="text-sm text-blue-600">🤝 Shared Access</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold">TS</div>
                                <div class="ml-2">
                                    <div class="text-sm font-medium">TechShare Co-op</div>
                                    <div class="text-xs text-gray-500">⭐ 4.7 (89 reviews)</div>
                                </div>
                            </div>
                            <button onclick="joinShare('network-suite-001')" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Join Share</button>
                        </div>
                    </div>
                </div>

                <!-- Featured Deal 3 -->
                <div class="marketplace-card bg-white rounded-lg shadow">
                    <div class="relative">
                        <div class="h-48 bg-gradient-to-r from-green-400 to-green-600 rounded-t-lg flex items-center justify-center">
                            <div class="text-6xl text-white">🔬</div>
                        </div>
                        <div class="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                            FOR SALE
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Quality Testing Lab</h3>
                        <p class="text-gray-600 mb-4">Complete materials testing and quality control setup</p>
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <div class="text-2xl font-bold text-red-600">R125,000</div>
                                <div class="text-sm text-gray-500">One-time purchase</div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-gray-600">📍 Durban</div>
                                <div class="text-sm text-red-600">💰 For Sale</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold">QL</div>
                                <div class="ml-2">
                                    <div class="text-sm font-medium">Quality Labs SA</div>
                                    <div class="text-xs text-gray-500">⭐ 4.8 (156 reviews)</div>
                                </div>
                            </div>
                            <button onclick="purchaseTool('quality-lab-001')" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Buy Now</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Categories -->
        <div class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-900 mb-6">🏷️ Browse by Category</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <button onclick="browseCategory('construction')" class="bg-white rounded-lg shadow p-6 text-center hover:shadow-lg transition-shadow">
                    <div class="text-3xl mb-2">🏗️</div>
                    <div class="text-sm font-medium">Construction</div>
                    <div class="text-xs text-gray-500">89 items</div>
                </button>
                <button onclick="browseCategory('it')" class="bg-white rounded-lg shadow p-6 text-center hover:shadow-lg transition-shadow">
                    <div class="text-3xl mb-2">💻</div>
                    <div class="text-sm font-medium">IT Equipment</div>
                    <div class="text-xs text-gray-500">45 items</div>
                </button>
                <button onclick="browseCategory('testing')" class="bg-white rounded-lg shadow p-6 text-center hover:shadow-lg transition-shadow">
                    <div class="text-3xl mb-2">🔬</div>
                    <div class="text-sm font-medium">Testing</div>
                    <div class="text-xs text-gray-500">32 items</div>
                </button>
                <button onclick="browseCategory('transportation')" class="bg-white rounded-lg shadow p-6 text-center hover:shadow-lg transition-shadow">
                    <div class="text-3xl mb-2">🚛</div>
                    <div class="text-sm font-medium">Transport</div>
                    <div class="text-xs text-gray-500">23 items</div>
                </button>
                <button onclick="browseCategory('safety')" class="bg-white rounded-lg shadow p-6 text-center hover:shadow-lg transition-shadow">
                    <div class="text-3xl mb-2">🦺</div>
                    <div class="text-sm font-medium">Safety</div>
                    <div class="text-xs text-gray-500">67 items</div>
                </button>
                <button onclick="browseCategory('specialized')" class="bg-white rounded-lg shadow p-6 text-center hover:shadow-lg transition-shadow">
                    <div class="text-3xl mb-2">⚙️</div>
                    <div class="text-sm font-medium">Specialized</div>
                    <div class="text-xs text-gray-500">18 items</div>
                </button>
            </div>
        </div>

        <!-- Tool Sharing Network -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">🤝 Tool Sharing Network</h2>
                <p class="text-sm text-gray-600">Join the collaborative tool sharing community</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <div class="text-2xl">🤝</div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Share Tools</h3>
                        <p class="text-gray-600 text-sm">List your unused tools and earn passive income while helping other contractors</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <div class="text-2xl">💰</div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Earn Money</h3>
                        <p class="text-gray-600 text-sm">Generate revenue from idle equipment with our secure rental platform</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <div class="text-2xl">🔧</div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Access Tools</h3>
                        <p class="text-gray-600 text-sm">Get access to expensive equipment without the upfront investment</p>
                    </div>
                </div>
                <div class="mt-6 text-center">
                    <button onclick="joinNetwork()" class="bg-orange-600 text-white px-8 py-3 rounded-lg hover:bg-orange-700 text-lg font-semibold">
                        🚀 Join Tool Sharing Network
                    </button>
                </div>
            </div>
        </div>

        <!-- Success Stories -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">🏆 Success Stories</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex items-start">
                            <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white font-bold">JM</div>
                            <div class="ml-4">
                                <h4 class="font-semibold text-green-900">John's Construction</h4>
                                <p class="text-sm text-green-700 mb-2">"Saved R200K by renting instead of buying. Won 3 major contracts!"</p>
                                <div class="text-xs text-green-600">⭐⭐⭐⭐⭐ Verified Success Story</div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-start">
                            <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">ST</div>
                            <div class="ml-4">
                                <h4 class="font-semibold text-blue-900">SmartTech Solutions</h4>
                                <p class="text-sm text-blue-700 mb-2">"Tool sharing network helped us access specialized equipment for R50K project"</p>
                                <div class="text-xs text-blue-600">⭐⭐⭐⭐⭐ Verified Success Story</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function searchMarketplace() {
            const searchTerm = document.getElementById('search-input').value;
            alert(`🔍 Searching marketplace for: "${searchTerm}"\n\nFound:\n• 23 rental options\n• 8 sharing opportunities\n• 5 purchase options\n• 12 nearby locations\n\nShowing best matches...`);
        }

        function filterMarketplace() {
            const typeFilter = document.getElementById('type-filter').value;
            const locationFilter = document.getElementById('location-filter').value;
            alert(`🔧 Filtering marketplace:\n\nType: ${typeFilter}\nLocation: ${locationFilter}\n\nUpdating results...`);
        }

        function rentTool(toolId) {
            alert(`🏗️ Renting Tool: ${toolId}\n\n✅ Availability confirmed\n✅ Insurance verified\n✅ Delivery scheduled\n✅ Operator training included\n\nRental agreement ready for signature.\nTotal cost: R8,500/day`);
        }

        function joinShare(toolId) {
            alert(`🤝 Joining Tool Share: ${toolId}\n\n✅ Share agreement prepared\n✅ Usage schedule coordinated\n✅ Cost sharing calculated\n✅ Maintenance responsibilities defined\n\nYour share: R150/day when needed`);
        }

        function purchaseTool(toolId) {
            alert(`💰 Purchasing Tool: ${toolId}\n\n✅ Condition verified\n✅ Warranty included\n✅ Financing options available\n✅ Delivery arranged\n\nTotal price: R125,000\nFinancing: R8,500/month for 18 months`);
        }

        function browseCategory(category) {
            alert(`🏷️ Browsing ${category} category...\n\nShowing all available tools in this category:\n• Rental options\n• Sharing opportunities\n• Purchase listings\n• Nearby locations`);
        }

        function listMyTools() {
            alert('💰 List Your Tools for Rent\n\nEarn passive income:\n• Professional photography\n• Market pricing analysis\n• Insurance coverage\n• Secure payment processing\n• Maintenance support\n\nStart earning today!');
        }

        function viewMyListings() {
            alert('📋 Your Tool Listings\n\nActive listings:\n• 3 tools currently rented\n• 2 tools available\n• 1 tool in maintenance\n\nTotal earnings this month: R15,400');
        }

        function joinNetwork() {
            alert('🚀 Joining Tool Sharing Network!\n\n✅ Network access granted\n✅ Profile created\n✅ Tool catalog synced\n✅ Sharing agreements activated\n\nWelcome to the largest tool sharing network in South Africa!\n\n🎯 You now have access to 1,200+ tools from 247 contractors!');
        }

        // Simulate real-time marketplace updates
        setInterval(() => {
            console.log(`[${new Date().toLocaleTimeString()}] Tool Marketplace: Monitoring 274 active listings and 89 sharing opportunities`);
        }, 30000);
    </script>
</body>
</html>
