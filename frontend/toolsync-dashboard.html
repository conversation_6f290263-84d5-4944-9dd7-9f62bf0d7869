<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - ToolSync Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .tool-card { transition: all 0.3s ease; }
        .tool-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .availability-indicator { animation: pulse 2s infinite; }
        .critical-alert { animation: bounce 1s infinite; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="/skillsync-dashboard.html" class="text-orange-600 hover:text-orange-800 mr-4">← Back to SkillSync</a>
                    <h1 class="text-3xl font-bold text-orange-600">🔧 ToolSync Dashboard</h1>
                    <span class="ml-3 text-sm text-gray-500">Equipment & Tool Management</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-red-600 critical-alert">⚠️ 2 Critical Shortages</span>
                    <button onclick="syncTools()" class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700">🔄 Sync Tools</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Alert Banner -->
    <div class="bg-red-50 border-l-4 border-red-400 p-4">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-red-700">
                        <strong>Tool Shortage Alert:</strong> You're missing critical equipment for 34% of available tenders. Properly equipped bidders win 89% more contracts!
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-orange-100 rounded-lg">
                        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Tools</p>
                        <p class="text-2xl font-semibold text-gray-900">247</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Available</p>
                        <p class="text-2xl font-semibold text-green-600">189</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-red-100 rounded-lg">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Critical Shortages</p>
                        <p class="text-2xl font-semibold text-red-600">2</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Tool Value</p>
                        <p class="text-2xl font-semibold text-blue-600">R1.8M</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Critical Tool Shortages -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">🚨 Critical Tool Shortages</h2>
                <p class="text-sm text-gray-600">Missing equipment that's blocking you from high-value tenders</p>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <!-- Shortage 1 -->
                    <div class="tool-card border border-red-200 rounded-lg p-4 bg-red-50">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center text-white text-xl">🏗️</div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-semibold text-red-900">Heavy Excavator (20-ton)</h3>
                                    <p class="text-sm text-red-700">Required for 8 construction tenders worth R3.2M</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-red-600">CRITICAL</div>
                                <div class="text-xs text-red-500">Blocking R3.2M</div>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div class="bg-white border border-red-200 rounded-lg p-3">
                                <h4 class="font-medium text-red-900 mb-1">🛒 Purchase Options</h4>
                                <p class="text-sm text-red-700">New: R850K | Used: R420K</p>
                                <p class="text-xs text-red-600">ROI: 380% (R3.2M potential)</p>
                            </div>
                            <div class="bg-white border border-red-200 rounded-lg p-3">
                                <h4 class="font-medium text-red-900 mb-1">🤝 Rental Options</h4>
                                <p class="text-sm text-red-700">R12K/month | R2K/day</p>
                                <p class="text-xs text-red-600">3 providers available nearby</p>
                            </div>
                            <div class="bg-white border border-red-200 rounded-lg p-3">
                                <h4 class="font-medium text-red-900 mb-1">👥 Partnership</h4>
                                <p class="text-sm text-red-700">2 contractors have this tool</p>
                                <p class="text-xs text-red-600">Joint venture opportunity</p>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-red-600">
                                <span class="font-medium">Impact:</span> Without this tool, you're automatically disqualified from major construction projects
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="findTool('excavator-20ton')" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">🔍 Find Tool</button>
                                <button onclick="requestPartnership('excavator-20ton')" class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700">🤝 Partner</button>
                            </div>
                        </div>
                    </div>

                    <!-- Shortage 2 -->
                    <div class="tool-card border border-orange-200 rounded-lg p-4 bg-orange-50">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center text-white text-xl">🔌</div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-semibold text-orange-900">Network Testing Equipment</h3>
                                    <p class="text-sm text-orange-700">Required for 5 IT infrastructure tenders worth R1.8M</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-orange-600">HIGH</div>
                                <div class="text-xs text-orange-500">Blocking R1.8M</div>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div class="bg-white border border-orange-200 rounded-lg p-3">
                                <h4 class="font-medium text-orange-900 mb-1">🛒 Purchase Options</h4>
                                <p class="text-sm text-orange-700">Professional Kit: R85K</p>
                                <p class="text-xs text-orange-600">ROI: 2,118% (R1.8M potential)</p>
                            </div>
                            <div class="bg-white border border-orange-200 rounded-lg p-3">
                                <h4 class="font-medium text-orange-900 mb-1">🤝 Rental Options</h4>
                                <p class="text-sm text-orange-700">R3K/month | R150/day</p>
                                <p class="text-xs text-orange-600">Available from IT suppliers</p>
                            </div>
                            <div class="bg-white border border-orange-200 rounded-lg p-3">
                                <h4 class="font-medium text-orange-900 mb-1">🎓 Training Required</h4>
                                <p class="text-sm text-orange-700">Network certification needed</p>
                                <p class="text-xs text-orange-600">Links to SkillSync training</p>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-orange-600">
                                <span class="font-medium">Impact:</span> Essential for all network infrastructure and security projects
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="findTool('network-testing')" class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700">🔍 Find Tool</button>
                                <button onclick="linkToSkillSync('network-cert')" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">🎓 Get Training</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tool Categories -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Available Tools -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900">🔧 Available Tools by Category</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm">🏗️</div>
                                <div class="ml-3">
                                    <div class="font-medium text-gray-900">Construction Equipment</div>
                                    <div class="text-sm text-gray-600">Heavy machinery, tools, safety equipment</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-green-600">89 available</div>
                                <div class="text-xs text-gray-500">12 in use</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm">💻</div>
                                <div class="ml-3">
                                    <div class="font-medium text-gray-900">IT Equipment</div>
                                    <div class="text-sm text-gray-600">Servers, networking, testing tools</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-green-600">45 available</div>
                                <div class="text-xs text-gray-500">8 in use</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm">🔬</div>
                                <div class="ml-3">
                                    <div class="font-medium text-gray-900">Testing & Measurement</div>
                                    <div class="text-sm text-gray-600">Quality control, inspection tools</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-green-600">32 available</div>
                                <div class="text-xs text-gray-500">5 in use</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white text-sm">🚛</div>
                                <div class="ml-3">
                                    <div class="font-medium text-gray-900">Transportation</div>
                                    <div class="text-sm text-gray-600">Vehicles, trailers, logistics</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-green-600">23 available</div>
                                <div class="text-xs text-gray-500">3 in use</div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-6">
                        <button onclick="viewAllTools()" class="w-full bg-orange-600 text-white py-2 px-4 rounded hover:bg-orange-700">View All Tools</button>
                    </div>
                </div>
            </div>

            <!-- Tool Marketplace -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900">🛒 Tool Marketplace</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h3 class="font-medium text-blue-900 mb-2">🔥 Hot Tools This Month</h3>
                            <div class="space-y-2">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-blue-700">Drone Survey Equipment</span>
                                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">+67% demand</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-blue-700">3D Printing Systems</span>
                                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">+45% demand</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-blue-700">Environmental Monitoring</span>
                                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">+38% demand</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h3 class="font-medium text-green-900 mb-2">💰 Rental Opportunities</h3>
                            <p class="text-sm text-green-700 mb-3">Earn R15K/month by renting out unused tools</p>
                            <button onclick="listToolsForRent()" class="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700">List Your Tools</button>
                        </div>
                        
                        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                            <h3 class="font-medium text-purple-900 mb-2">🤝 Tool Sharing Network</h3>
                            <p class="text-sm text-purple-700 mb-3">Connect with 247 contractors for tool sharing</p>
                            <button onclick="joinToolNetwork()" class="w-full bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700">Join Network</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function syncTools() {
            alert('🔄 ToolSync Activated!\n\nAnalyzing:\n• Current tool inventory\n• Market demand trends\n• Tender requirements\n• Rental opportunities\n• Partnership possibilities\n\nTool recommendations updated!');
        }

        function findTool(toolId) {
            const tools = {
                'excavator-20ton': 'Heavy Excavator (20-ton)',
                'network-testing': 'Network Testing Equipment'
            };
            
            alert(`🔍 Finding ${tools[toolId]}...\n\n✅ 3 rental providers found\n✅ 2 purchase options available\n✅ 1 partnership opportunity\n✅ Financing options available\n\nBest option: Rent for R12K/month\nThis will unlock R3.2M in tender opportunities!`);
        }

        function requestPartnership(toolId) {
            alert(`🤝 Requesting partnership for ${toolId}...\n\n✅ Partnership request sent to 2 contractors\n✅ Joint venture proposal prepared\n✅ Cost-sharing agreement drafted\n\nYou'll receive responses within 24 hours.\n\n🎯 This partnership will unlock major construction projects!`);
        }

        function linkToSkillSync(skillId) {
            alert('🎓 Connecting to SkillSync...\n\nRequired training identified:\n• Network Infrastructure Certification\n• Equipment Operation Training\n• Safety Compliance Course\n\nRedirecting to SkillSync for enrollment...');
            setTimeout(() => {
                window.open('/skillsync-dashboard.html', '_blank');
            }, 1500);
        }

        function viewAllTools() {
            alert('🔧 Opening Tool Inventory...\n\nShowing:\n• All 247 tools in inventory\n• Availability status\n• Maintenance schedules\n• Usage analytics\n• Rental opportunities');
        }

        function listToolsForRent() {
            alert('💰 Tool Rental Marketplace\n\nYour tools can earn:\n• Excavator: R12K/month\n• Crane: R8K/month\n• Testing Equipment: R3K/month\n• Vehicles: R5K/month\n\nTotal potential: R28K/month\nList your unused tools now!');
        }

        function joinToolNetwork() {
            alert('🤝 Joining Tool Sharing Network...\n\n✅ Connected to 247 contractors\n✅ Access to 1,200+ tools\n✅ Sharing agreements activated\n✅ Cost reduction: 45%\n\nYou can now access tools from the entire network!');
        }

        // Simulate real-time tool monitoring
        setInterval(() => {
            console.log(`[${new Date().toLocaleTimeString()}] ToolSync: Monitoring tool availability and market opportunities`);
        }, 30000);
    </script>
</body>
</html>
