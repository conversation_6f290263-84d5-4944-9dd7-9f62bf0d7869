<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - WhatsApp Integration Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .message-bubble { transition: all 0.3s ease; }
        .message-bubble:hover { transform: translateY(-1px); }
        .pulse-dot { animation: pulse 2s infinite; }
        .typing-indicator { animation: bounce 1s infinite; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="/dashboard.html" class="text-green-600 hover:text-green-800 mr-4">← Back to Dashboard</a>
                    <h1 class="text-3xl font-bold text-green-600">💬 WhatsApp Integration</h1>
                    <span class="ml-3 text-sm text-gray-500">Auto-Bidding & Notifications</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-green-600 pulse-dot">🟢 Connected</span>
                    <button onclick="sendTestMessage()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">📱 Send Test</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Messages Sent</p>
                        <p class="text-2xl font-semibold text-gray-900">1,247</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Auto-Bids Sent</p>
                        <p class="text-2xl font-semibold text-gray-900">23</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Success Rate</p>
                        <p class="text-2xl font-semibold text-gray-900">78%</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-orange-100 rounded-lg">
                        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Response Time</p>
                        <p class="text-2xl font-semibold text-gray-900">2.3s</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live Chat Interface -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Chat Window -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 bg-green-600 text-white rounded-t-lg">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-white rounded-full flex items-center justify-center text-green-600 font-bold">BB</div>
                        <div class="ml-3">
                            <h2 class="text-lg font-semibold">BidBeez AI Assistant</h2>
                            <p class="text-sm opacity-90">🟢 Online • Auto-bidding active</p>
                        </div>
                    </div>
                </div>
                <div class="p-6 h-96 overflow-y-auto bg-gray-50">
                    <!-- Messages -->
                    <div class="space-y-4">
                        <!-- Incoming message -->
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold">BB</div>
                            <div class="ml-3 message-bubble bg-white rounded-lg p-3 max-w-xs">
                                <p class="text-sm">🚨 New tender alert: Road Maintenance Services - R450K</p>
                                <p class="text-xs text-gray-500 mt-1">2 minutes ago</p>
                            </div>
                        </div>

                        <!-- Outgoing message -->
                        <div class="flex items-start justify-end">
                            <div class="mr-3 message-bubble bg-blue-600 text-white rounded-lg p-3 max-w-xs">
                                <p class="text-sm">Auto-bid: Yes, submit optimized bid</p>
                                <p class="text-xs opacity-75 mt-1">1 minute ago</p>
                            </div>
                            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">You</div>
                        </div>

                        <!-- System message -->
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold">BB</div>
                            <div class="ml-3 message-bubble bg-green-100 rounded-lg p-3 max-w-xs">
                                <p class="text-sm">✅ Bid submitted successfully! Tracking ID: BID-2024-001</p>
                                <p class="text-xs text-gray-500 mt-1">30 seconds ago</p>
                            </div>
                        </div>

                        <!-- Typing indicator -->
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold">BB</div>
                            <div class="ml-3 message-bubble bg-white rounded-lg p-3">
                                <div class="flex space-x-1">
                                    <div class="w-2 h-2 bg-gray-400 rounded-full typing-indicator"></div>
                                    <div class="w-2 h-2 bg-gray-400 rounded-full typing-indicator" style="animation-delay: 0.2s;"></div>
                                    <div class="w-2 h-2 bg-gray-400 rounded-full typing-indicator" style="animation-delay: 0.4s;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="p-4 border-t border-gray-200">
                    <div class="flex space-x-2">
                        <input type="text" placeholder="Type a message..." class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <button onclick="sendMessage()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">Send</button>
                    </div>
                </div>
            </div>

            <!-- Auto-Bid Settings -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900">⚡ Auto-Bid Settings</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-6">
                        <!-- Auto-bid toggle -->
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900">Auto-Bidding</h3>
                                <p class="text-sm text-gray-600">Automatically submit bids based on AI recommendations</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                            </label>
                        </div>

                        <!-- Bid criteria -->
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Maximum Bid Value</label>
                                <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                    <option>R500,000</option>
                                    <option>R1,000,000</option>
                                    <option>R2,000,000</option>
                                    <option>R5,000,000</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Success Probability</label>
                                <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                    <option>60%</option>
                                    <option>70%</option>
                                    <option>80%</option>
                                    <option>90%</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Preferred Categories</label>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                                        <span class="ml-2 text-sm text-gray-700">IT Services</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                                        <span class="ml-2 text-sm text-gray-700">Construction</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                                        <span class="ml-2 text-sm text-gray-700">Consulting</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <button onclick="saveSettings()" class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700">Save Settings</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">📱 Recent WhatsApp Activity</h2>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm text-gray-900">Auto-bid submitted for "Road Maintenance Services" - R450K</p>
                            <p class="text-xs text-gray-500">2 minutes ago • Success probability: 85%</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm text-gray-900">Tender alert sent: "IT Infrastructure Upgrade" - R1.2M</p>
                            <p class="text-xs text-gray-500">15 minutes ago • Awaiting user confirmation</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-purple-400 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm text-gray-900">Bid status update: "Security Services" - Shortlisted!</p>
                            <p class="text-xs text-gray-500">1 hour ago • Moving to final evaluation</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-yellow-400 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm text-gray-900">Deadline reminder: "Consulting Project" - 2 days remaining</p>
                            <p class="text-xs text-gray-500">3 hours ago • Auto-bid scheduled</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function sendTestMessage() {
            alert('📱 Sending test WhatsApp message...\n\n✅ Connection verified\n✅ Message delivered\n✅ Auto-bid system active\n\nTest message: "BidBeez AI is online and ready to help with your bidding!"');
        }

        function sendMessage() {
            const input = document.querySelector('input[placeholder="Type a message..."]');
            if (input.value.trim()) {
                alert(`📤 Message sent: "${input.value}"\n\n✅ Delivered to BidBeez AI\n✅ Processing your request\n✅ Response incoming...`);
                input.value = '';
            }
        }

        function saveSettings() {
            alert('⚙️ Auto-bid settings saved!\n\n✅ Maximum bid value updated\n✅ Success probability threshold set\n✅ Category preferences saved\n✅ WhatsApp notifications configured\n\nYour auto-bidding system is now optimized!');
        }

        // Simulate real-time WhatsApp activity
        setInterval(() => {
            console.log(`[${new Date().toLocaleTimeString()}] WhatsApp: Monitoring tender alerts and auto-bid opportunities`);
        }, 20000);

        // Simulate incoming messages
        setTimeout(() => {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg z-50';
            notification.innerHTML = '💬 New WhatsApp message: Tender alert for Environmental Services - R680K';
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }, 15000);
    </script>
</body>
</html>
