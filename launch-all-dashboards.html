<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - Master Dashboard Launcher</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="config/platform-config.js"></script>
    <style>
        .dashboard-card { transition: all 0.3s ease; }
        .dashboard-card:hover { transform: translateY(-2px); box-shadow: 0 15px 35px rgba(0,0,0,0.1); }
        .launch-indicator { animation: pulse 2s infinite; }
        .status-online { color: #10B981; }
        .status-offline { color: #EF4444; }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-purple-50">
    <!-- Header -->
    <header class="bg-white shadow-lg border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <h1 class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">🚀 BidBeez Master Launcher</h1>
                    <span class="ml-4 text-lg text-gray-500">All Dashboards & Applications</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-green-600 launch-indicator" id="system-status">🟢 All Systems Ready</span>
                    <button onclick="launchAllDashboards()" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700">🚀 Launch All</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Quick Launch Controls -->
    <div class="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-6">
                <h2 class="text-3xl font-bold mb-4">🎯 Master Control Center</h2>
                <p class="text-xl">Launch individual dashboards or all at once</p>
            </div>
            <div class="flex justify-center space-x-4">
                <button onclick="launchCoreSystem()" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-6 py-3 rounded-lg transition-all">
                    🏠 Core System
                </button>
                <button onclick="launchSyncSystems()" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-6 py-3 rounded-lg transition-all">
                    🔄 Sync Systems
                </button>
                <button onclick="launchManagementSystems()" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-6 py-3 rounded-lg transition-all">
                    👑 Management
                </button>
                <button onclick="launchBusinessSystems()" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-6 py-3 rounded-lg transition-all">
                    📊 Business
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Core Dashboards -->
        <div class="mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-8">🏠 Core System Dashboards</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Main Dashboard -->
                <div class="dashboard-card bg-white rounded-lg shadow-lg p-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <div class="text-3xl">🏠</div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Main Dashboard</h3>
                        <p class="text-sm text-gray-600 mb-4">Central command center</p>
                        <div class="flex items-center justify-center mb-4">
                            <span class="status-indicator status-online" id="status-dashboard">● Online</span>
                        </div>
                        <button onclick="launchDashboard('/dashboard.html')" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 w-full">Launch</button>
                    </div>
                </div>

                <!-- Master Index -->
                <div class="dashboard-card bg-white rounded-lg shadow-lg p-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <div class="text-3xl">🗺️</div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Master Index</h3>
                        <p class="text-sm text-gray-600 mb-4">All 99+ pages overview</p>
                        <div class="flex items-center justify-center mb-4">
                            <span class="status-indicator status-online" id="status-index">● Online</span>
                        </div>
                        <button onclick="launchDashboard('/master-index.html')" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 w-full">Launch</button>
                    </div>
                </div>

                <!-- User Profile -->
                <div class="dashboard-card bg-white rounded-lg shadow-lg p-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <div class="text-3xl">👤</div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">User Profile</h3>
                        <p class="text-sm text-gray-600 mb-4">Personal settings & info</p>
                        <div class="flex items-center justify-center mb-4">
                            <span class="status-indicator status-online" id="status-profile">● Online</span>
                        </div>
                        <button onclick="launchDashboard('/user-profile.html')" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 w-full">Launch</button>
                    </div>
                </div>

                <!-- Notifications -->
                <div class="dashboard-card bg-white rounded-lg shadow-lg p-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <div class="text-3xl">🔔</div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Notifications</h3>
                        <p class="text-sm text-gray-600 mb-4">Alerts & updates center</p>
                        <div class="flex items-center justify-center mb-4">
                            <span class="status-indicator status-online" id="status-notifications">● Online</span>
                        </div>
                        <button onclick="launchDashboard('/notifications.html')" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 w-full">Launch</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Management Systems -->
        <div class="mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-8">👑 Management Systems</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- QueenBee Command -->
                <div class="dashboard-card bg-white rounded-lg shadow-lg p-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <div class="text-3xl">👑</div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">QueenBee Command</h3>
                        <p class="text-sm text-gray-600 mb-4">Worker bee management</p>
                        <div class="flex items-center justify-center mb-4">
                            <span class="status-indicator status-online" id="status-queenbee">● Online</span>
                        </div>
                        <button onclick="launchDashboard('/queenbee-dashboard.html')" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 w-full">Launch</button>
                    </div>
                </div>

                <!-- Task Runner -->
                <div class="dashboard-card bg-white rounded-lg shadow-lg p-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <div class="text-3xl">⚡</div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Task Runner</h3>
                        <p class="text-sm text-gray-600 mb-4">Workflow automation</p>
                        <div class="flex items-center justify-center mb-4">
                            <span class="status-indicator status-online" id="status-taskrunner">● Online</span>
                        </div>
                        <button onclick="launchDashboard('/task-runner-dashboard.html')" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 w-full">Launch</button>
                    </div>
                </div>

                <!-- Courier Management -->
                <div class="dashboard-card bg-white rounded-lg shadow-lg p-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <div class="text-3xl">🚚</div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Courier Management</h3>
                        <p class="text-sm text-gray-600 mb-4">Delivery & logistics</p>
                        <div class="flex items-center justify-center mb-4">
                            <span class="status-indicator status-online" id="status-courier">● Online</span>
                        </div>
                        <button onclick="launchDashboard('/courier-dashboard.html')" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 w-full">Launch</button>
                    </div>
                </div>

                <!-- BEE Compliance -->
                <div class="dashboard-card bg-white rounded-lg shadow-lg p-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <div class="text-3xl">🏛️</div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">BEE Compliance</h3>
                        <p class="text-sm text-gray-600 mb-4">Level 4 BEE tracking</p>
                        <div class="flex items-center justify-center mb-4">
                            <span class="status-indicator status-online" id="status-bee">● Online</span>
                        </div>
                        <button onclick="launchDashboard('/bee-compliance-dashboard.html')" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 w-full">Launch</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sync Systems -->
        <div class="mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-8">🔄 Sync Systems</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- SkillSync -->
                <div class="dashboard-card bg-white rounded-lg shadow-lg p-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <div class="text-3xl">🎓</div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">SkillSync</h3>
                        <p class="text-sm text-gray-600 mb-4">Skills & training management</p>
                        <div class="flex items-center justify-center mb-4">
                            <span class="status-indicator status-online" id="status-skillsync">● Online</span>
                        </div>
                        <button onclick="launchDashboard('/skillsync-dashboard.html')" class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 w-full">Launch</button>
                    </div>
                </div>

                <!-- ToolSync -->
                <div class="dashboard-card bg-white rounded-lg shadow-lg p-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <div class="text-3xl">🔧</div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">ToolSync</h3>
                        <p class="text-sm text-gray-600 mb-4">Equipment & tool management</p>
                        <div class="flex items-center justify-center mb-4">
                            <span class="status-indicator status-online" id="status-toolsync">● Online</span>
                        </div>
                        <button onclick="launchDashboard('/toolsync-dashboard.html')" class="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700 w-full">Launch</button>
                    </div>
                </div>

                <!-- ContractorSync -->
                <div class="dashboard-card bg-white rounded-lg shadow-lg p-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <div class="text-3xl">🤝</div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">ContractorSync</h3>
                        <p class="text-sm text-gray-600 mb-4">Partnership management</p>
                        <div class="flex items-center justify-center mb-4">
                            <span class="status-indicator status-online" id="status-contractorsync">● Online</span>
                        </div>
                        <button onclick="launchDashboard('/contractorsync-hub.html')" class="bg-pink-600 text-white px-4 py-2 rounded hover:bg-pink-700 w-full">Launch</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Systems -->
        <div class="mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-8">📊 Business Systems</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Bids Management -->
                <div class="dashboard-card bg-white rounded-lg shadow-lg p-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <div class="text-3xl">📝</div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Bids Management</h3>
                        <p class="text-sm text-gray-600 mb-4">Complete bid lifecycle</p>
                        <div class="flex items-center justify-center mb-4">
                            <span class="status-indicator status-online" id="status-bids">● Online</span>
                        </div>
                        <button onclick="launchDashboard('/bids-dashboard.html')" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 w-full">Launch</button>
                    </div>
                </div>

                <!-- WhatsApp Integration -->
                <div class="dashboard-card bg-white rounded-lg shadow-lg p-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <div class="text-3xl">💬</div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">WhatsApp Hub</h3>
                        <p class="text-sm text-gray-600 mb-4">Auto-bidding & communication</p>
                        <div class="flex items-center justify-center mb-4">
                            <span class="status-indicator status-online" id="status-whatsapp">● Online</span>
                        </div>
                        <button onclick="launchDashboard('/whatsapp-dashboard.html')" class="bg-emerald-600 text-white px-4 py-2 rounded hover:bg-emerald-700 w-full">Launch</button>
                    </div>
                </div>

                <!-- Analytics -->
                <div class="dashboard-card bg-white rounded-lg shadow-lg p-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <div class="text-3xl">📊</div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Analytics</h3>
                        <p class="text-sm text-gray-600 mb-4">Performance & insights</p>
                        <div class="flex items-center justify-center mb-4">
                            <span class="status-indicator status-online" id="status-analytics">● Online</span>
                        </div>
                        <button onclick="launchDashboard('/analytics-dashboard.html')" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 w-full">Launch</button>
                    </div>
                </div>

                <!-- Platform Overview -->
                <div class="dashboard-card bg-white rounded-lg shadow-lg p-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <div class="text-3xl">🎯</div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Platform Overview</h3>
                        <p class="text-sm text-gray-600 mb-4">Complete system status</p>
                        <div class="flex items-center justify-center mb-4">
                            <span class="status-indicator status-online" id="status-overview">● Online</span>
                        </div>
                        <button onclick="launchDashboard('/complete-platform-overview.html')" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded hover:from-blue-700 hover:to-purple-700 w-full">Launch</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Launch Status -->
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">🚀 Launch Status</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-3xl font-bold text-green-600" id="launched-count">0</div>
                    <div class="text-sm text-gray-600">Dashboards Launched</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-blue-600">15</div>
                    <div class="text-sm text-gray-600">Total Available</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-purple-600" id="launch-percentage">0%</div>
                    <div class="text-sm text-gray-600">System Coverage</div>
                </div>
            </div>
            <div class="mt-6">
                <div class="w-full bg-gray-200 rounded-full h-4">
                    <div class="bg-gradient-to-r from-blue-600 to-purple-600 h-4 rounded-full transition-all duration-500" style="width: 0%" id="launch-progress"></div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let launchedDashboards = 0;
        const totalDashboards = 15;

        function launchDashboard(url) {
            window.open(url, '_blank');
            launchedDashboards++;
            updateLaunchStatus();
            
            // Show success notification
            showNotification(`Dashboard launched: ${url}`, 'success');
        }

        function launchAllDashboards() {
            const dashboards = [
                '/dashboard.html',
                '/master-index.html',
                '/user-profile.html',
                '/notifications.html',
                '/queenbee-dashboard.html',
                '/task-runner-dashboard.html',
                '/courier-dashboard.html',
                '/bee-compliance-dashboard.html',
                '/skillsync-dashboard.html',
                '/toolsync-dashboard.html',
                '/contractorsync-hub.html',
                '/bids-dashboard.html',
                '/whatsapp-dashboard.html',
                '/analytics-dashboard.html',
                '/complete-platform-overview.html'
            ];

            dashboards.forEach((url, index) => {
                setTimeout(() => {
                    launchDashboard(url);
                }, index * 500); // Stagger launches by 500ms
            });

            showNotification('🚀 Launching all dashboards...', 'info');
        }

        function launchCoreSystem() {
            const coreDashboards = [
                '/dashboard.html',
                '/master-index.html',
                '/user-profile.html',
                '/notifications.html'
            ];
            
            coreDashboards.forEach((url, index) => {
                setTimeout(() => launchDashboard(url), index * 300);
            });
        }

        function launchSyncSystems() {
            const syncDashboards = [
                '/skillsync-dashboard.html',
                '/toolsync-dashboard.html',
                '/contractorsync-hub.html'
            ];
            
            syncDashboards.forEach((url, index) => {
                setTimeout(() => launchDashboard(url), index * 300);
            });
        }

        function launchManagementSystems() {
            const managementDashboards = [
                '/queenbee-dashboard.html',
                '/task-runner-dashboard.html',
                '/courier-dashboard.html',
                '/bee-compliance-dashboard.html'
            ];
            
            managementDashboards.forEach((url, index) => {
                setTimeout(() => launchDashboard(url), index * 300);
            });
        }

        function launchBusinessSystems() {
            const businessDashboards = [
                '/bids-dashboard.html',
                '/whatsapp-dashboard.html',
                '/analytics-dashboard.html',
                '/complete-platform-overview.html'
            ];
            
            businessDashboards.forEach((url, index) => {
                setTimeout(() => launchDashboard(url), index * 300);
            });
        }

        function updateLaunchStatus() {
            const percentage = Math.round((launchedDashboards / totalDashboards) * 100);
            
            document.getElementById('launched-count').textContent = launchedDashboards;
            document.getElementById('launch-percentage').textContent = `${percentage}%`;
            document.getElementById('launch-progress').style.width = `${percentage}%`;
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const colors = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                info: 'bg-blue-500',
                warning: 'bg-yellow-500'
            };
            
            notification.className = `fixed top-4 right-4 ${colors[type]} text-white p-4 rounded-lg shadow-lg z-50 transform transition-all duration-300`;
            notification.innerHTML = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Initialize system status checks
        document.addEventListener('DOMContentLoaded', function() {
            // Simulate system status checks
            setTimeout(() => {
                showNotification('🎉 All systems online and ready for launch!', 'success');
            }, 1000);
        });

        // Real-time monitoring
        setInterval(() => {
            console.log(`[${new Date().toLocaleTimeString()}] Master Launcher: ${launchedDashboards}/${totalDashboards} dashboards launched`);
        }, 30000);
    </script>
</body>
</html>
