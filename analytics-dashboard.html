<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - Analytics Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .chart-container { transition: all 0.3s ease; }
        .chart-container:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .metric-card { transition: all 0.3s ease; }
        .metric-card:hover { transform: scale(1.02); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="/dashboard.html" class="text-blue-600 hover:text-blue-800 mr-4">← Back to Dashboard</a>
                    <h1 class="text-3xl font-bold text-blue-600">📊 Analytics Dashboard</h1>
                    <span class="ml-3 text-sm text-gray-500">AI-Powered Business Intelligence</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-green-600">🧠 AI Analysis: Active</span>
                    <button onclick="refreshAnalytics()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">🔄 Refresh</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Key Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="metric-card bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Success Rate</p>
                        <p class="text-2xl font-semibold text-gray-900">68%</p>
                        <p class="text-xs text-green-600">+12% vs last month</p>
                    </div>
                </div>
            </div>
            <div class="metric-card bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Value Won</p>
                        <p class="text-2xl font-semibold text-gray-900">R8.4M</p>
                        <p class="text-xs text-blue-600">+23% vs last quarter</p>
                    </div>
                </div>
            </div>
            <div class="metric-card bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Bids</p>
                        <p class="text-2xl font-semibold text-gray-900">12</p>
                        <p class="text-xs text-purple-600">3 closing this week</p>
                    </div>
                </div>
            </div>
            <div class="metric-card bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-orange-100 rounded-lg">
                        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Psychological Score</p>
                        <p class="text-2xl font-semibold text-gray-900">8.7/10</p>
                        <p class="text-xs text-orange-600">Optimal performance</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Bid Performance Chart -->
            <div class="chart-container bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900">📈 Bid Performance Trends</h2>
                </div>
                <div class="p-6">
                    <canvas id="bidPerformanceChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Success Rate by Category -->
            <div class="chart-container bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900">🎯 Success Rate by Category</h2>
                </div>
                <div class="p-6">
                    <canvas id="categoryChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Psychological Analytics -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">🧠 Psychological Performance Analytics</h2>
                <p class="text-sm text-gray-600">AI-powered insights into your bidding behavior and optimization opportunities</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h3 class="font-medium text-blue-900 mb-3">🎯 Bidder Archetype</h3>
                        <div class="text-center mb-3">
                            <div class="text-3xl font-bold text-blue-600">Analytical</div>
                            <div class="text-sm text-blue-700">Strategic & Data-Driven</div>
                        </div>
                        <div class="space-y-2">
                            <div class="flex justify-between text-sm">
                                <span>Analytical Thinking</span>
                                <span class="font-medium">92%</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span>Risk Assessment</span>
                                <span class="font-medium">87%</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span>Strategic Planning</span>
                                <span class="font-medium">89%</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <h3 class="font-medium text-green-900 mb-3">📊 Performance Patterns</h3>
                        <div class="space-y-3">
                            <div class="bg-white rounded p-3">
                                <div class="text-sm font-medium text-green-900">Peak Performance Time</div>
                                <div class="text-lg text-green-700">Tuesday 10-11 AM</div>
                            </div>
                            <div class="bg-white rounded p-3">
                                <div class="text-sm font-medium text-green-900">Optimal Bid Value</div>
                                <div class="text-lg text-green-700">R500K - R2M</div>
                            </div>
                            <div class="bg-white rounded p-3">
                                <div class="text-sm font-medium text-green-900">Best Category</div>
                                <div class="text-lg text-green-700">IT Services (89%)</div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                        <h3 class="font-medium text-purple-900 mb-3">🚀 AI Recommendations</h3>
                        <div class="space-y-3">
                            <div class="bg-white rounded p-3">
                                <div class="text-sm font-medium text-purple-900">Focus Area</div>
                                <div class="text-sm text-purple-700">Increase construction bids by 15%</div>
                            </div>
                            <div class="bg-white rounded p-3">
                                <div class="text-sm font-medium text-purple-900">Timing</div>
                                <div class="text-sm text-purple-700">Submit bids 3-5 days before deadline</div>
                            </div>
                            <div class="bg-white rounded p-3">
                                <div class="text-sm font-medium text-purple-900">Partnership</div>
                                <div class="text-sm text-purple-700">Consider joint ventures for >R5M</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">📋 Recent Analytics Events</h2>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm text-gray-900">Success rate increased to 68% (+12% improvement)</p>
                            <p class="text-xs text-gray-500">2 hours ago</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm text-gray-900">New psychological insights generated for IT category</p>
                            <p class="text-xs text-gray-500">4 hours ago</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-purple-400 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm text-gray-900">AI optimization recommendations updated</p>
                            <p class="text-xs text-gray-500">6 hours ago</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Initialize charts
        function initializeCharts() {
            // Bid Performance Chart
            const bidCtx = document.getElementById('bidPerformanceChart').getContext('2d');
            new Chart(bidCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Success Rate %',
                        data: [45, 52, 58, 61, 65, 68],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Bid Value (R millions)',
                        data: [2.1, 3.2, 4.1, 5.8, 7.2, 8.4],
                        borderColor: 'rgb(16, 185, 129)',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    }
                }
            });

            // Category Success Chart
            const categoryCtx = document.getElementById('categoryChart').getContext('2d');
            new Chart(categoryCtx, {
                type: 'doughnut',
                data: {
                    labels: ['IT Services', 'Construction', 'Consulting', 'Maintenance', 'Other'],
                    datasets: [{
                        data: [89, 67, 78, 56, 62],
                        backgroundColor: [
                            'rgb(59, 130, 246)',
                            'rgb(16, 185, 129)',
                            'rgb(245, 158, 11)',
                            'rgb(239, 68, 68)',
                            'rgb(139, 92, 246)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    }
                }
            });
        }

        function refreshAnalytics() {
            alert('🔄 Refreshing analytics data...\n\n✅ Bid performance updated\n✅ Psychological insights refreshed\n✅ AI recommendations recalculated\n✅ Market trends analyzed\n\nAll analytics are now up to date!');
        }

        // Initialize charts when page loads
        window.addEventListener('load', initializeCharts);

        // Simulate real-time updates
        setInterval(() => {
            console.log(`[${new Date().toLocaleTimeString()}] Analytics: Processing real-time bid performance data`);
        }, 30000);
    </script>
</body>
</html>
