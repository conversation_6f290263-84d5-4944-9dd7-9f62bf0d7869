<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - Bids Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .page-card { transition: all 0.3s ease; }
        .page-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .status-badge { animation: pulse 2s infinite; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="/dashboard.html" class="text-blue-600 hover:text-blue-800 mr-4">← Back</a>
                    <h1 class="text-3xl font-bold text-green-600">📝 Bids Dashboard</h1>
                    <span class="ml-3 text-sm text-gray-500">Complete Bid Management System</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-green-600 status-badge">✅ 12 Active Bids</span>
                    <span class="text-sm text-blue-600">🎯 68% Success Rate</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Bids</p>
                        <p class="text-2xl font-semibold text-gray-900">12</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Success Rate</p>
                        <p class="text-2xl font-semibold text-gray-900">68%</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Value</p>
                        <p class="text-2xl font-semibold text-gray-900">R2.4M</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-orange-100 rounded-lg">
                        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending</p>
                        <p class="text-2xl font-semibold text-gray-900">8</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- All 10 Bid Pages -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-2xl font-semibold text-gray-900">📝 All Bid Management Pages</h2>
                <p class="text-sm text-gray-600">Complete bid lifecycle management - 10 specialized pages</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Create Bid -->
                    <div class="page-card bg-green-50 border border-green-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white text-xl">➕</div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-green-900">Create Bid</h3>
                                <p class="text-sm text-green-700">New bid creation</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Start a new bid with AI-powered assistance and psychological profiling</p>
                        <button onclick="openBidPage('create-bid')" class="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 transition-colors">
                            Open Page
                        </button>
                    </div>

                    <!-- Bid History -->
                    <div class="page-card bg-blue-50 border border-blue-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white text-xl">📚</div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-blue-900">Bid History</h3>
                                <p class="text-sm text-blue-700">Historical bid data</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">View all past bids with detailed analytics and performance metrics</p>
                        <button onclick="openBidPage('bid-history')" class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors">
                            Open Page
                        </button>
                    </div>

                    <!-- Bid Tracking -->
                    <div class="page-card bg-purple-50 border border-purple-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white text-xl">📍</div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-purple-900">Bid Tracking</h3>
                                <p class="text-sm text-purple-700">Real-time bid status</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Monitor bid progress with real-time updates and status notifications</p>
                        <button onclick="openBidPage('bid-tracking')" class="w-full bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700 transition-colors">
                            Open Page
                        </button>
                    </div>

                    <!-- Bid Templates -->
                    <div class="page-card bg-orange-50 border border-orange-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center text-white text-xl">📋</div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-orange-900">Bid Templates</h3>
                                <p class="text-sm text-orange-700">Reusable bid templates</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Create and manage reusable bid templates for faster bid creation</p>
                        <button onclick="openBidPage('bid-templates')" class="w-full bg-orange-600 text-white py-2 px-4 rounded hover:bg-orange-700 transition-colors">
                            Open Page
                        </button>
                    </div>

                    <!-- Bid Collaboration -->
                    <div class="page-card bg-indigo-50 border border-indigo-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center text-white text-xl">👥</div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-indigo-900">Bid Collaboration</h3>
                                <p class="text-sm text-indigo-700">Team collaboration tools</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Collaborate with team members on bid preparation and review</p>
                        <button onclick="openBidPage('bid-collaboration')" class="w-full bg-indigo-600 text-white py-2 px-4 rounded hover:bg-indigo-700 transition-colors">
                            Open Page
                        </button>
                    </div>

                    <!-- Bid Optimization -->
                    <div class="page-card bg-pink-50 border border-pink-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center text-white text-xl">⚡</div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-pink-900">Bid Optimization</h3>
                                <p class="text-sm text-pink-700">AI-powered optimization</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Optimize bids using AI and psychological insights for better success rates</p>
                        <button onclick="openBidPage('bid-optimization')" class="w-full bg-pink-600 text-white py-2 px-4 rounded hover:bg-pink-700 transition-colors">
                            Open Page
                        </button>
                    </div>

                    <!-- Bid Submission -->
                    <div class="page-card bg-teal-50 border border-teal-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-teal-500 rounded-full flex items-center justify-center text-white text-xl">📤</div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-teal-900">Bid Submission</h3>
                                <p class="text-sm text-teal-700">Submit bids securely</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Securely submit bids with digital signatures and compliance checks</p>
                        <button onclick="openBidPage('bid-submission')" class="w-full bg-teal-600 text-white py-2 px-4 rounded hover:bg-teal-700 transition-colors">
                            Open Page
                        </button>
                    </div>

                    <!-- Bid Evaluation -->
                    <div class="page-card bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center text-white text-xl">📊</div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-yellow-900">Bid Evaluation</h3>
                                <p class="text-sm text-yellow-700">Evaluate bid success</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Analyze bid performance and identify improvement opportunities</p>
                        <button onclick="openBidPage('bid-evaluation')" class="w-full bg-yellow-600 text-white py-2 px-4 rounded hover:bg-yellow-700 transition-colors">
                            Open Page
                        </button>
                    </div>

                    <!-- Bid Success -->
                    <div class="page-card bg-emerald-50 border border-emerald-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center text-white text-xl">🏆</div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-emerald-900">Bid Success</h3>
                                <p class="text-sm text-emerald-700">Success metrics</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Track successful bids and celebrate wins with detailed success metrics</p>
                        <button onclick="openBidPage('bid-success')" class="w-full bg-emerald-600 text-white py-2 px-4 rounded hover:bg-emerald-700 transition-colors">
                            Open Page
                        </button>
                    </div>

                    <!-- Main Bid Dashboard (Current Page) -->
                    <div class="page-card bg-gray-50 border border-gray-300 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-gray-500 rounded-full flex items-center justify-center text-white text-xl">🏠</div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-gray-900">Bid Dashboard</h3>
                                <p class="text-sm text-gray-700">Main bid overview</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Central hub for all bid management activities (Current Page)</p>
                        <button class="w-full bg-gray-400 text-white py-2 px-4 rounded cursor-not-allowed" disabled>
                            Current Page
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">Recent Bid Activity</h2>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm text-gray-900">New bid created for "Road Maintenance Services"</p>
                            <p class="text-xs text-gray-500">2 hours ago</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm text-gray-900">Bid submitted for "IT Infrastructure Upgrade"</p>
                            <p class="text-xs text-gray-500">4 hours ago</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-yellow-400 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm text-gray-900">Bid evaluation completed for "Security Services"</p>
                            <p class="text-xs text-gray-500">6 hours ago</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function openBidPage(pageName) {
            // Open the specific bid page
            window.location.href = `/${pageName}.html`;
        }

        // Real-time updates simulation
        setInterval(() => {
            console.log(`[${new Date().toLocaleTimeString()}] Bids Dashboard: Monitoring 12 active bids`);
        }, 10000);
    </script>
</body>
</html>
