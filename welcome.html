<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - Welcome</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 flex items-center justify-center min-h-screen">
    <div class="text-center">
        <div class="mb-8">
            <h1 class="text-4xl font-bold text-blue-600 mb-2">🧠 BidBeez</h1>
            <p class="text-gray-600">Psychological Engagement Platform</p>
        </div>
        
        <div class="bg-white rounded-lg shadow-lg p-8 max-w-md">
            <div class="mb-6">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h2 class="text-xl font-semibold text-gray-900 mb-2">Platform Ready!</h2>
                <p class="text-gray-600 text-sm">All 92+ pages are now accessible</p>
            </div>
            
            <div class="space-y-3">
                <a href="/dashboard.html" class="block w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                    🚀 Open Dashboard
                </a>
                <a href="http://localhost:8000/docs" target="_blank" class="block w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-200 transition-colors">
                    📡 API Documentation
                </a>
                <a href="http://localhost:8000/health" target="_blank" class="block w-full bg-green-100 text-green-700 py-3 px-4 rounded-lg hover:bg-green-200 transition-colors">
                    💊 Health Check
                </a>
            </div>
            
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="flex items-center justify-center space-x-4 text-sm text-gray-500">
                    <span class="flex items-center">
                        <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        API: Running
                    </span>
                    <span class="flex items-center">
                        <span class="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
                        Frontend: Active
                    </span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
