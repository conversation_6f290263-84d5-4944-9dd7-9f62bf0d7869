<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - QueenBee Command Center</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="config/platform-config.js"></script>
    <script src="js/api-service.js"></script>
    <style>
        .bee-card { transition: all 0.3s ease; }
        .bee-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .queen-indicator { animation: pulse 2s infinite; }
        .worker-status { animation: flow 3s infinite; }
        .task-progress { transition: width 0.5s ease; }
        @keyframes flow { 0%, 100% { opacity: 0.7; } 50% { opacity: 1; } }
        .territory-map { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="/dashboard.html" class="text-purple-600 hover:text-purple-800 mr-4">← Back to Dashboard</a>
                    <h1 class="text-3xl font-bold text-purple-600">👑 QueenBee Command Center</h1>
                    <span class="ml-3 text-sm text-gray-500">Territory Management & Worker Coordination</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-purple-600 queen-indicator">👑 Queen Sarah Mthembu</span>
                    <button onclick="assignNewTask()" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">📋 Assign Task</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Territory Status -->
    <div class="bg-purple-50 border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-6">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">24</div>
                        <div class="text-xs text-gray-600">Worker Bees</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">18</div>
                        <div class="text-xs text-gray-600">Active</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">47</div>
                        <div class="text-xs text-gray-600">Active Tasks</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600">12</div>
                        <div class="text-xs text-gray-600">Completed Today</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-red-600">3</div>
                        <div class="text-xs text-gray-600">Quality Checks</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-indigo-600">89%</div>
                        <div class="text-xs text-gray-600">Team Productivity</div>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-sm font-medium text-purple-600">Territory: Johannesburg Central</div>
                    <div class="text-xs text-gray-500">Coverage: 15km radius | 247 active projects</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Queen Bee Profile -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-t-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-3xl mr-4">👑</div>
                        <div>
                            <h2 class="text-2xl font-bold">Queen Sarah Mthembu</h2>
                            <p class="text-purple-100">Senior Project Coordinator | Level 5 Queen Bee</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold">4.9⭐</div>
                        <div class="text-sm text-purple-100">Management Rating</div>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">3.2 years</div>
                        <div class="text-sm text-gray-600">Experience</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">847</div>
                        <div class="text-sm text-gray-600">Tasks Coordinated</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">94%</div>
                        <div class="text-sm text-gray-600">Success Rate</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600">R2.8M</div>
                        <div class="text-sm text-gray-600">Value Delivered</div>
                    </div>
                </div>
                <div class="mt-6 flex flex-wrap gap-2">
                    <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">Construction Management</span>
                    <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">Team Leadership</span>
                    <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">Quality Assurance</span>
                    <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm">BEE Compliance</span>
                    <span class="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm">Risk Management</span>
                </div>
            </div>
        </div>

        <!-- Worker Bees & Tasks Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Worker Bees Management -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900">🐝 Worker Bees (24 Total)</h2>
                    <p class="text-sm text-gray-600">Your managed workforce across the territory</p>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <!-- Worker Bee 1 -->
                        <div class="bee-card border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white font-bold">JM</div>
                                    <div class="ml-3">
                                        <h3 class="font-semibold text-gray-900">John Molefe</h3>
                                        <p class="text-sm text-gray-600">🎯 Specialist Bee | Construction</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <span class="worker-status w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                                        ACTIVE
                                    </span>
                                    <div class="text-xs text-gray-500 mt-1">⭐ 4.8 rating</div>
                                </div>
                            </div>
                            <div class="grid grid-cols-3 gap-4 text-sm">
                                <div>
                                    <span class="font-medium">Current Task:</span>
                                    <div class="text-blue-600">Site Inspection</div>
                                </div>
                                <div>
                                    <span class="font-medium">Progress:</span>
                                    <div class="text-green-600">78%</div>
                                </div>
                                <div>
                                    <span class="font-medium">ETA:</span>
                                    <div class="text-orange-600">2:30 PM</div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="task-progress bg-green-600 h-2 rounded-full" style="width: 78%"></div>
                                </div>
                            </div>
                            <div class="mt-3 flex space-x-2">
                                <button onclick="contactWorker('john-molefe')" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">📞 Contact</button>
                                <button onclick="trackWorker('john-molefe')" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">📍 Track</button>
                                <button onclick="assignTask('john-molefe')" class="bg-purple-600 text-white px-3 py-1 rounded text-sm hover:bg-purple-700">📋 Assign</button>
                            </div>
                        </div>

                        <!-- Worker Bee 2 -->
                        <div class="bee-card border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">LN</div>
                                    <div class="ml-3">
                                        <h3 class="font-semibold text-gray-900">Linda Nkomo</h3>
                                        <p class="text-sm text-gray-600">⭐ Senior Bee | Quality Control</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <span class="w-2 h-2 bg-yellow-400 rounded-full mr-1"></span>
                                        BREAK
                                    </span>
                                    <div class="text-xs text-gray-500 mt-1">⭐ 4.9 rating</div>
                                </div>
                            </div>
                            <div class="grid grid-cols-3 gap-4 text-sm">
                                <div>
                                    <span class="font-medium">Last Task:</span>
                                    <div class="text-gray-600">Quality Audit</div>
                                </div>
                                <div>
                                    <span class="font-medium">Completed:</span>
                                    <div class="text-green-600">100%</div>
                                </div>
                                <div>
                                    <span class="font-medium">Available:</span>
                                    <div class="text-blue-600">1:00 PM</div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 100%"></div>
                                </div>
                            </div>
                            <div class="mt-3 flex space-x-2">
                                <button onclick="contactWorker('linda-nkomo')" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">📞 Contact</button>
                                <button onclick="trackWorker('linda-nkomo')" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">📍 Track</button>
                                <button onclick="assignTask('linda-nkomo')" class="bg-purple-600 text-white px-3 py-1 rounded text-sm hover:bg-purple-700">📋 Assign</button>
                            </div>
                        </div>

                        <!-- Worker Bee 3 -->
                        <div class="bee-card border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold">TM</div>
                                    <div class="ml-3">
                                        <h3 class="font-semibold text-gray-900">Thabo Mthembu</h3>
                                        <p class="text-sm text-gray-600">📚 Trainee Bee | Learning</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <span class="worker-status w-2 h-2 bg-blue-400 rounded-full mr-1"></span>
                                        TRAINING
                                    </span>
                                    <div class="text-xs text-gray-500 mt-1">⭐ 4.2 rating</div>
                                </div>
                            </div>
                            <div class="grid grid-cols-3 gap-4 text-sm">
                                <div>
                                    <span class="font-medium">Training:</span>
                                    <div class="text-indigo-600">Safety Protocols</div>
                                </div>
                                <div>
                                    <span class="font-medium">Progress:</span>
                                    <div class="text-orange-600">45%</div>
                                </div>
                                <div>
                                    <span class="font-medium">Mentor:</span>
                                    <div class="text-purple-600">Linda N.</div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="task-progress bg-orange-600 h-2 rounded-full" style="width: 45%"></div>
                                </div>
                            </div>
                            <div class="mt-3 flex space-x-2">
                                <button onclick="contactWorker('thabo-mthembu')" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">📞 Contact</button>
                                <button onclick="viewTraining('thabo-mthembu')" class="bg-indigo-600 text-white px-3 py-1 rounded text-sm hover:bg-indigo-700">📚 Training</button>
                                <button onclick="linkToSkillSync()" class="bg-orange-600 text-white px-3 py-1 rounded text-sm hover:bg-orange-700">🎓 SkillSync</button>
                            </div>
                        </div>
                    </div>
                    <div class="mt-6">
                        <button onclick="viewAllWorkers()" class="w-full bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700">
                            View All 24 Worker Bees
                        </button>
                    </div>
                </div>
            </div>

            <!-- Active Tasks Management -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900">📋 Active Tasks (47 Total)</h2>
                    <p class="text-sm text-gray-600">Current assignments and progress tracking</p>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <!-- Task 1 -->
                        <div class="bee-card border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <h3 class="font-semibold text-gray-900">🏗️ Municipal Building Inspection</h3>
                                    <p class="text-sm text-gray-600">Quality control and compliance verification</p>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        IN PROGRESS
                                    </span>
                                    <div class="text-xs text-gray-500 mt-1">Priority: HIGH</div>
                                </div>
                            </div>
                            <div class="grid grid-cols-3 gap-4 text-sm mb-3">
                                <div>
                                    <span class="font-medium">Assigned to:</span>
                                    <div class="text-blue-600">John Molefe</div>
                                </div>
                                <div>
                                    <span class="font-medium">Progress:</span>
                                    <div class="text-green-600">78%</div>
                                </div>
                                <div>
                                    <span class="font-medium">Due:</span>
                                    <div class="text-orange-600">Today 5:00 PM</div>
                                </div>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
                                <div class="task-progress bg-blue-600 h-2 rounded-full" style="width: 78%"></div>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="viewTaskDetails('task-001')" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">👁️ View</button>
                                <button onclick="reassignTask('task-001')" class="bg-orange-600 text-white px-3 py-1 rounded text-sm hover:bg-orange-700">🔄 Reassign</button>
                                <button onclick="urgentTask('task-001')" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700">⚡ Urgent</button>
                            </div>
                        </div>

                        <!-- Task 2 -->
                        <div class="bee-card border border-yellow-200 rounded-lg p-4 bg-yellow-50">
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <h3 class="font-semibold text-gray-900">📄 BEE Compliance Documentation</h3>
                                    <p class="text-sm text-gray-600">Certificate verification and scorecard update</p>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        QUALITY CHECK
                                    </span>
                                    <div class="text-xs text-gray-500 mt-1">Priority: MEDIUM</div>
                                </div>
                            </div>
                            <div class="grid grid-cols-3 gap-4 text-sm mb-3">
                                <div>
                                    <span class="font-medium">Completed by:</span>
                                    <div class="text-green-600">Linda Nkomo</div>
                                </div>
                                <div>
                                    <span class="font-medium">Quality Score:</span>
                                    <div class="text-yellow-600">Pending</div>
                                </div>
                                <div>
                                    <span class="font-medium">Submitted:</span>
                                    <div class="text-blue-600">11:30 AM</div>
                                </div>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
                                <div class="bg-yellow-600 h-2 rounded-full" style="width: 100%"></div>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="qualityCheck('task-002')" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">✅ Approve</button>
                                <button onclick="requestRevision('task-002')" class="bg-orange-600 text-white px-3 py-1 rounded text-sm hover:bg-orange-700">🔄 Revise</button>
                                <button onclick="linkToBEE()" class="bg-purple-600 text-white px-3 py-1 rounded text-sm hover:bg-purple-700">🏛️ BEE</button>
                            </div>
                        </div>

                        <!-- Task 3 -->
                        <div class="bee-card border border-green-200 rounded-lg p-4 bg-green-50">
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <h3 class="font-semibold text-gray-900">🔧 Equipment Maintenance Check</h3>
                                    <p class="text-sm text-gray-600">Tool inventory and maintenance scheduling</p>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        COMPLETED
                                    </span>
                                    <div class="text-xs text-gray-500 mt-1">Priority: LOW</div>
                                </div>
                            </div>
                            <div class="grid grid-cols-3 gap-4 text-sm mb-3">
                                <div>
                                    <span class="font-medium">Completed by:</span>
                                    <div class="text-green-600">Mike Sithole</div>
                                </div>
                                <div>
                                    <span class="font-medium">Quality Score:</span>
                                    <div class="text-green-600">⭐ 4.8/5</div>
                                </div>
                                <div>
                                    <span class="font-medium">Finished:</span>
                                    <div class="text-green-600">10:15 AM</div>
                                </div>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
                                <div class="bg-green-600 h-2 rounded-full" style="width: 100%"></div>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="viewTaskReport('task-003')" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">📊 Report</button>
                                <button onclick="linkToToolSync()" class="bg-orange-600 text-white px-3 py-1 rounded text-sm hover:bg-orange-700">🔧 ToolSync</button>
                                <button onclick="archiveTask('task-003')" class="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700">📁 Archive</button>
                            </div>
                        </div>
                    </div>
                    <div class="mt-6">
                        <button onclick="viewAllTasks()" class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
                            View All 47 Active Tasks
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Territory Map & Analytics -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Territory Coverage Map -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900">🗺️ Territory Coverage</h2>
                    <p class="text-sm text-gray-600">Real-time worker bee locations and coverage areas</p>
                </div>
                <div class="p-6">
                    <div class="territory-map rounded-lg h-64 flex items-center justify-center text-white relative overflow-hidden">
                        <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                        <div class="relative z-10 text-center">
                            <div class="text-4xl mb-2">🗺️</div>
                            <div class="text-xl font-bold mb-2">Johannesburg Central</div>
                            <div class="text-sm">15km radius coverage</div>
                            <div class="text-sm">18 active worker bees</div>
                        </div>
                        <!-- Worker Bee Indicators -->
                        <div class="absolute top-4 left-4 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                        <div class="absolute top-8 right-6 w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
                        <div class="absolute bottom-6 left-8 w-3 h-3 bg-orange-400 rounded-full animate-pulse"></div>
                        <div class="absolute bottom-4 right-4 w-3 h-3 bg-purple-400 rounded-full animate-pulse"></div>
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-yellow-400 rounded-full animate-pulse"></div>
                    </div>
                    <div class="mt-4 grid grid-cols-2 gap-4 text-sm">
                        <div class="text-center">
                            <div class="text-lg font-bold text-green-600">94%</div>
                            <div class="text-gray-600">Territory Coverage</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-blue-600">2.3km</div>
                            <div class="text-gray-600">Avg Distance</div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button onclick="openFullMap()" class="w-full bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700">
                            🗺️ Open Full Map View
                        </button>
                    </div>
                </div>
            </div>

            <!-- Performance Analytics -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900">📊 Performance Analytics</h2>
                    <p class="text-sm text-gray-600">Team productivity and quality metrics</p>
                </div>
                <div class="p-6">
                    <div class="space-y-6">
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">Team Productivity</span>
                                <span class="text-sm text-purple-600">89%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div class="bg-purple-600 h-3 rounded-full" style="width: 89%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">Quality Score</span>
                                <span class="text-sm text-green-600">4.7/5</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div class="bg-green-600 h-3 rounded-full" style="width: 94%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">Task Completion Rate</span>
                                <span class="text-sm text-blue-600">96%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div class="bg-blue-600 h-3 rounded-full" style="width: 96%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">Client Satisfaction</span>
                                <span class="text-sm text-orange-600">4.9/5</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div class="bg-orange-600 h-3 rounded-full" style="width: 98%"></div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 grid grid-cols-2 gap-4">
                        <div class="text-center bg-purple-50 rounded-lg p-3">
                            <div class="text-2xl font-bold text-purple-600">R847K</div>
                            <div class="text-xs text-gray-600">Value Delivered</div>
                        </div>
                        <div class="text-center bg-green-50 rounded-lg p-3">
                            <div class="text-2xl font-bold text-green-600">156</div>
                            <div class="text-xs text-gray-600">Tasks This Month</div>
                        </div>
                    </div>

                    <div class="mt-6">
                        <button onclick="viewDetailedAnalytics()" class="w-full bg-indigo-600 text-white py-2 px-4 rounded hover:bg-indigo-700">
                            📈 Detailed Analytics
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // QueenBee Dashboard JavaScript Functions with Backend Integration
        let beeProfiles = [];
        let beeTasks = [];
        let realTimeSubscriptions = [];

        // Initialize dashboard with real data
        async function initializeDashboard() {
            try {
                // Load real bee profiles and tasks
                await loadBeeProfiles();
                await loadBeeTasks();
                await loadDashboardStats();

                // Setup real-time subscriptions
                setupRealTimeUpdates();

                console.log('QueenBee Dashboard initialized with backend data');
            } catch (error) {
                console.error('Error initializing dashboard:', error);
                // Fallback to mock data if backend unavailable
                loadMockData();
            }
        }

        async function loadBeeProfiles() {
            try {
                beeProfiles = await window.BidBeezAPI.getBeeProfiles({ is_active: true });
                updateBeeProfilesDisplay();
            } catch (error) {
                console.error('Error loading bee profiles:', error);
            }
        }

        async function loadBeeTasks() {
            try {
                beeTasks = await window.BidBeezAPI.getBeeTasks({
                    status: ['assigned', 'in_progress', 'completed']
                });
                updateTasksDisplay();
            } catch (error) {
                console.error('Error loading bee tasks:', error);
            }
        }

        async function loadDashboardStats() {
            try {
                const stats = await window.BidBeezAPI.getDashboardStats();
                updateStatsDisplay(stats);
            } catch (error) {
                console.error('Error loading dashboard stats:', error);
            }
        }

        function updateBeeProfilesDisplay() {
            // Update the worker bee cards with real data
            const activeBees = beeProfiles.filter(bee => bee.availability_status === 'available');
            const busyBees = beeProfiles.filter(bee => bee.availability_status === 'busy');

            // Update header stats
            document.querySelector('.text-2xl.font-bold.text-purple-600').textContent = beeProfiles.length;
            document.querySelector('.text-2xl.font-bold.text-green-600').textContent = activeBees.length;

            console.log(`Loaded ${beeProfiles.length} bee profiles, ${activeBees.length} available`);
        }

        function updateTasksDisplay() {
            // Update task cards with real data
            const activeTasks = beeTasks.filter(task => task.status === 'assigned' || task.status === 'in_progress');
            const completedToday = beeTasks.filter(task =>
                task.status === 'completed' &&
                new Date(task.updated_at).toDateString() === new Date().toDateString()
            );

            // Update header stats
            const activeTasksElement = document.querySelector('.text-2xl.font-bold.text-blue-600');
            if (activeTasksElement) activeTasksElement.textContent = activeTasks.length;

            const completedTodayElement = document.querySelector('.text-2xl.font-bold.text-orange-600');
            if (completedTodayElement) completedTodayElement.textContent = completedToday.length;

            console.log(`Loaded ${beeTasks.length} tasks, ${activeTasks.length} active`);
        }

        function updateStatsDisplay(stats) {
            // Update various dashboard statistics
            console.log('Dashboard stats:', stats);
        }

        function setupRealTimeUpdates() {
            // Subscribe to real-time updates for bee locations
            const locationSubscription = window.BidBeezAPI.subscribeToBeeLocations((payload) => {
                console.log('Bee location update:', payload);
                handleLocationUpdate(payload);
            });

            // Subscribe to real-time updates for bee tasks
            const taskSubscription = window.BidBeezAPI.subscribeToBeeTasks((payload) => {
                console.log('Bee task update:', payload);
                handleTaskUpdate(payload);
            });

            realTimeSubscriptions.push(locationSubscription, taskSubscription);
        }

        function handleLocationUpdate(payload) {
            // Update bee location on the map
            const { new: newLocation, old: oldLocation, eventType } = payload;

            if (eventType === 'UPDATE' || eventType === 'INSERT') {
                // Update the territory map with new bee position
                updateBeeLocationOnMap(newLocation);
            }
        }

        function handleTaskUpdate(payload) {
            // Update task status in real-time
            const { new: newTask, old: oldTask, eventType } = payload;

            if (eventType === 'UPDATE') {
                // Update task progress or status
                updateTaskProgress(newTask);
            } else if (eventType === 'INSERT') {
                // Add new task to the list
                addNewTaskToDisplay(newTask);
            }
        }

        function updateBeeLocationOnMap(location) {
            // Update the visual indicators on the territory map
            console.log('Updating bee location on map:', location);
        }

        function updateTaskProgress(task) {
            // Update task progress bars and status
            console.log('Updating task progress:', task);
        }

        function addNewTaskToDisplay(task) {
            // Add new task to the active tasks list
            console.log('Adding new task to display:', task);
        }

        function loadMockData() {
            // Fallback mock data if backend is unavailable
            console.log('Loading mock data as fallback');
        }

        async function assignNewTask() {
            try {
                // Get available bees
                const availableBees = beeProfiles.filter(bee => bee.availability_status === 'available');

                if (availableBees.length === 0) {
                    alert('⚠️ No available worker bees at the moment. Please try again later.');
                    return;
                }

                // Show task creation interface
                const taskData = await showTaskCreationDialog(availableBees);

                if (taskData) {
                    // Create task in backend
                    const newTask = await window.BidBeezAPI.supabaseQuery('bee_tasks', {
                        method: 'INSERT',
                        data: taskData
                    });

                    alert(`✅ Task "${taskData.title}" assigned successfully to ${taskData.assigned_bee_name}!`);

                    // Refresh tasks display
                    await loadBeeTasks();
                }
            } catch (error) {
                console.error('Error assigning new task:', error);
                alert('❌ Error creating task. Please try again.');
            }
        }

        async function showTaskCreationDialog(availableBees) {
            // Simplified task creation - in production this would be a proper modal
            const taskTitle = prompt('Enter task title:');
            if (!taskTitle) return null;

            const taskDescription = prompt('Enter task description:');
            if (!taskDescription) return null;

            const beeNames = availableBees.map(bee => `${bee.full_name} (${bee.professional_title})`).join('\n');
            const selectedBeeIndex = prompt(`Select worker bee (0-${availableBees.length-1}):\n${beeNames}`);

            if (selectedBeeIndex === null || selectedBeeIndex < 0 || selectedBeeIndex >= availableBees.length) {
                return null;
            }

            const selectedBee = availableBees[selectedBeeIndex];

            return {
                title: taskTitle,
                description: taskDescription,
                assigned_bee_id: selectedBee.id,
                assigned_bee_name: selectedBee.full_name,
                status: 'assigned',
                priority: 'medium',
                category: 'general',
                created_at: new Date().toISOString(),
                assigned_at: new Date().toISOString()
            };
        }

        function contactWorker(workerId) {
            const workers = {
                'john-molefe': 'John Molefe (Specialist Bee)',
                'linda-nkomo': 'Linda Nkomo (Senior Bee)',
                'thabo-mthembu': 'Thabo Mthembu (Trainee Bee)'
            };

            alert(`📞 Contacting ${workers[workerId]}...\n\n✅ WhatsApp message sent\n✅ Location shared\n✅ Task update requested\n✅ ETA confirmation\n\nWorker bee contacted successfully!`);
        }

        function trackWorker(workerId) {
            alert(`📍 Tracking Worker Bee: ${workerId}\n\nReal-time location:\n• GPS coordinates: -26.2041, 28.0473\n• Current location: Sandton City\n• Last update: 2 minutes ago\n• Movement status: On route\n• Battery level: 78%\n\nLive tracking active!`);
        }

        function assignTask(workerId) {
            alert(`📋 Assigning Task to ${workerId}\n\nAvailable tasks:\n• Municipal building inspection\n• BEE compliance check\n• Equipment maintenance\n• Quality audit\n• Training session\n\nSelect task and priority level.`);
        }

        function viewTraining(workerId) {
            alert(`📚 Training Progress: ${workerId}\n\nCurrent training:\n• Safety Protocols (45% complete)\n• Quality Standards (78% complete)\n• BEE Compliance (23% complete)\n• Equipment Operation (89% complete)\n\nMentor: Linda Nkomo\nNext session: Tomorrow 9:00 AM`);
        }

        function linkToSkillSync() {
            alert('🎓 Connecting to SkillSync...\n\nIntegrating worker bee training with SkillSync:\n• Track certification progress\n• Schedule training sessions\n• Monitor skill development\n• Assign learning paths\n\nRedirecting to SkillSync...');
            setTimeout(() => {
                window.open('/skillsync-dashboard.html', '_blank');
            }, 1500);
        }

        function viewAllWorkers() {
            alert('🐝 All Worker Bees Overview\n\nShowing complete workforce:\n• 24 total worker bees\n• 18 currently active\n• 4 on break\n• 2 in training\n• Performance ratings\n• Current assignments\n• Location tracking\n\nManage your entire bee workforce!');
        }

        function viewTaskDetails(taskId) {
            alert(`📋 Task Details: ${taskId}\n\nComplete task information:\n• Task specifications\n• Progress timeline\n• Quality requirements\n• Resource allocation\n• Client requirements\n• Completion criteria\n\nDetailed task management interface.`);
        }

        function reassignTask(taskId) {
            alert(`🔄 Reassigning Task: ${taskId}\n\nReassignment options:\n• Select new worker bee\n• Adjust priority level\n• Update deadline\n• Modify requirements\n• Add additional resources\n\nOptimize task allocation for better results.`);
        }

        function urgentTask(taskId) {
            alert(`⚡ Marking Task as URGENT: ${taskId}\n\n✅ Priority elevated to URGENT\n✅ Worker bee notified immediately\n✅ Additional resources allocated\n✅ Queen Bee monitoring activated\n✅ Client notification sent\n\nUrgent task protocol activated!`);
        }

        function qualityCheck(taskId) {
            alert(`✅ Quality Check Approval: ${taskId}\n\n✅ Task quality approved\n✅ Worker bee performance rated\n✅ Client notification sent\n✅ Payment processing initiated\n✅ Task archived successfully\n\nTask completed with excellent quality!`);
        }

        function requestRevision(taskId) {
            alert(`🔄 Requesting Task Revision: ${taskId}\n\n✅ Revision request sent to worker bee\n✅ Specific feedback provided\n✅ Quality standards clarified\n✅ New deadline set\n✅ Additional support offered\n\nWorker bee will address the issues and resubmit.`);
        }

        function linkToBEE() {
            window.open('/bee-compliance-dashboard.html', '_blank');
        }

        function linkToToolSync() {
            window.open('/toolsync-dashboard.html', '_blank');
        }

        function viewTaskReport(taskId) {
            alert(`📊 Task Report: ${taskId}\n\nDetailed completion report:\n• Quality score: 4.8/5\n• Time efficiency: 95%\n• Resource utilization: 87%\n• Client satisfaction: 5/5\n• Cost effectiveness: 92%\n\nExcellent task execution!`);
        }

        function archiveTask(taskId) {
            alert(`📁 Archiving Task: ${taskId}\n\n✅ Task moved to archive\n✅ Performance data saved\n✅ Lessons learned documented\n✅ Worker bee feedback recorded\n✅ Client satisfaction logged\n\nTask successfully archived for future reference.`);
        }

        function viewAllTasks() {
            alert('📋 All Active Tasks Overview\n\nShowing complete task portfolio:\n• 47 active tasks\n• 12 in progress\n• 3 pending quality check\n• 8 scheduled\n• 24 completed today\n• Priority distribution\n• Resource allocation\n\nComprehensive task management interface!');
        }

        function openFullMap() {
            alert('🗺️ Opening Full Territory Map\n\nInteractive map features:\n• Real-time worker bee locations\n• Task assignment zones\n• Coverage area analysis\n• Route optimization\n• Traffic conditions\n• Client locations\n\nFull geographic management interface!');
        }

        function viewDetailedAnalytics() {
            alert('📈 Detailed Performance Analytics\n\nComprehensive analytics dashboard:\n• Team productivity trends\n• Individual performance metrics\n• Quality score analysis\n• Client satisfaction tracking\n• Revenue generation\n• Efficiency optimization\n\nAdvanced analytics and insights!');
        }

        // Real-time updates simulation
        function updateDashboard() {
            // Simulate real-time worker bee status updates
            const statusElements = document.querySelectorAll('.worker-status');
            statusElements.forEach(element => {
                element.style.opacity = element.style.opacity === '0.5' ? '1' : '0.5';
            });
        }

        // Initialize dashboard with backend integration
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize with real backend data
            initializeDashboard();

            // Start real-time updates
            setInterval(updateDashboard, 3000);

            // Show initialization notification
            setTimeout(() => {
                const notification = document.createElement('div');
                notification.className = 'fixed top-4 right-4 bg-purple-500 text-white p-4 rounded-lg shadow-lg z-50';
                notification.innerHTML = '👑 QueenBee Command Center connected to backend database';
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 5000);
            }, 2000);
        });

        // Simulate real-time monitoring
        setInterval(() => {
            console.log(`[${new Date().toLocaleTimeString()}] QueenBee Command Center: Monitoring 24 worker bees and 47 active tasks`);
        }, 30000);
    </script>
</body>
</html>