<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - Bid Templates</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .template-card { transition: all 0.3s ease; }
        .template-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="/bids-dashboard.html" class="text-orange-600 hover:text-orange-800 mr-4">← Back to Bids</a>
                    <h1 class="text-3xl font-bold text-orange-600">📋 Bid Templates</h1>
                    <span class="ml-3 text-sm text-gray-500">Reusable Bid Templates</span>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="createTemplate()" class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700">+ New Template</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <div class="text-2xl font-bold text-orange-600">15</div>
                <div class="text-sm text-gray-600">Total Templates</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <div class="text-2xl font-bold text-green-600">8</div>
                <div class="text-sm text-gray-600">Active Templates</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <div class="text-2xl font-bold text-blue-600">42</div>
                <div class="text-sm text-gray-600">Times Used</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <div class="text-2xl font-bold text-purple-600">73%</div>
                <div class="text-sm text-gray-600">Success Rate</div>
            </div>
        </div>

        <!-- Template Categories -->
        <div class="mb-8">
            <div class="flex space-x-4 mb-6">
                <button onclick="filterTemplates('all')" class="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700" id="filter-all">All Templates</button>
                <button onclick="filterTemplates('construction')" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300" id="filter-construction">Construction</button>
                <button onclick="filterTemplates('it')" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300" id="filter-it">IT Services</button>
                <button onclick="filterTemplates('consulting')" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300" id="filter-consulting">Consulting</button>
                <button onclick="filterTemplates('maintenance')" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300" id="filter-maintenance">Maintenance</button>
            </div>
        </div>

        <!-- Templates Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="templates-grid">
            <!-- Construction Template -->
            <div class="template-card bg-white rounded-lg shadow p-6" data-category="construction">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white text-xl">🏗️</div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Construction Services</h3>
                        <p class="text-sm text-gray-600">General construction template</p>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                        <span>Success Rate: 85%</span>
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded">Active</span>
                    </div>
                    <div class="text-sm text-gray-600">Used 12 times • Last used: 2 days ago</div>
                </div>
                <div class="flex space-x-2">
                    <button onclick="useTemplate('construction-services')" class="flex-1 bg-orange-600 text-white py-2 px-4 rounded hover:bg-orange-700">Use Template</button>
                    <button onclick="editTemplate('construction-services')" class="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50">Edit</button>
                </div>
            </div>

            <!-- IT Services Template -->
            <div class="template-card bg-white rounded-lg shadow p-6" data-category="it">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white text-xl">💻</div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">IT Infrastructure</h3>
                        <p class="text-sm text-gray-600">IT services and infrastructure</p>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                        <span>Success Rate: 72%</span>
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded">Active</span>
                    </div>
                    <div class="text-sm text-gray-600">Used 8 times • Last used: 1 week ago</div>
                </div>
                <div class="flex space-x-2">
                    <button onclick="useTemplate('it-infrastructure')" class="flex-1 bg-orange-600 text-white py-2 px-4 rounded hover:bg-orange-700">Use Template</button>
                    <button onclick="editTemplate('it-infrastructure')" class="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50">Edit</button>
                </div>
            </div>

            <!-- Consulting Template -->
            <div class="template-card bg-white rounded-lg shadow p-6" data-category="consulting">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white text-xl">🎯</div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Management Consulting</h3>
                        <p class="text-sm text-gray-600">Professional consulting services</p>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                        <span>Success Rate: 90%</span>
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded">Active</span>
                    </div>
                    <div class="text-sm text-gray-600">Used 15 times • Last used: 3 days ago</div>
                </div>
                <div class="flex space-x-2">
                    <button onclick="useTemplate('management-consulting')" class="flex-1 bg-orange-600 text-white py-2 px-4 rounded hover:bg-orange-700">Use Template</button>
                    <button onclick="editTemplate('management-consulting')" class="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50">Edit</button>
                </div>
            </div>

            <!-- Maintenance Template -->
            <div class="template-card bg-white rounded-lg shadow p-6" data-category="maintenance">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center text-white text-xl">🔧</div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Maintenance Services</h3>
                        <p class="text-sm text-gray-600">General maintenance template</p>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                        <span>Success Rate: 68%</span>
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded">Active</span>
                    </div>
                    <div class="text-sm text-gray-600">Used 7 times • Last used: 5 days ago</div>
                </div>
                <div class="flex space-x-2">
                    <button onclick="useTemplate('maintenance-services')" class="flex-1 bg-orange-600 text-white py-2 px-4 rounded hover:bg-orange-700">Use Template</button>
                    <button onclick="editTemplate('maintenance-services')" class="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50">Edit</button>
                </div>
            </div>

            <!-- Security Template -->
            <div class="template-card bg-white rounded-lg shadow p-6" data-category="security">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center text-white text-xl">🛡️</div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Security Services</h3>
                        <p class="text-sm text-gray-600">Security and protection services</p>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                        <span>Success Rate: 76%</span>
                        <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded">Draft</span>
                    </div>
                    <div class="text-sm text-gray-600">Used 3 times • Last used: 2 weeks ago</div>
                </div>
                <div class="flex space-x-2">
                    <button onclick="useTemplate('security-services')" class="flex-1 bg-orange-600 text-white py-2 px-4 rounded hover:bg-orange-700">Use Template</button>
                    <button onclick="editTemplate('security-services')" class="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50">Edit</button>
                </div>
            </div>

            <!-- Custom Template -->
            <div class="template-card bg-white rounded-lg shadow p-6" data-category="custom">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center text-white text-xl">⭐</div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Custom Template</h3>
                        <p class="text-sm text-gray-600">Your personalized template</p>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                        <span>Success Rate: 82%</span>
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded">Active</span>
                    </div>
                    <div class="text-sm text-gray-600">Used 5 times • Last used: 1 week ago</div>
                </div>
                <div class="flex space-x-2">
                    <button onclick="useTemplate('custom-template')" class="flex-1 bg-orange-600 text-white py-2 px-4 rounded hover:bg-orange-700">Use Template</button>
                    <button onclick="editTemplate('custom-template')" class="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50">Edit</button>
                </div>
            </div>
        </div>

        <!-- Template Analytics -->
        <div class="mt-12 bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">📊 Template Performance</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600 mb-2">85%</div>
                        <div class="text-sm text-gray-600">Average Success Rate</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600 mb-2">42</div>
                        <div class="text-sm text-gray-600">Total Uses This Month</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-purple-600 mb-2">2.3x</div>
                        <div class="text-sm text-gray-600">Faster Bid Creation</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function filterTemplates(category) {
            const templates = document.querySelectorAll('.template-card');
            const buttons = document.querySelectorAll('[id^="filter-"]');
            
            // Reset button styles
            buttons.forEach(btn => {
                btn.className = 'px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300';
            });
            
            // Highlight active button
            document.getElementById(`filter-${category}`).className = 'px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700';
            
            // Filter templates
            templates.forEach(template => {
                if (category === 'all' || template.dataset.category === category) {
                    template.style.display = 'block';
                } else {
                    template.style.display = 'none';
                }
            });
        }

        function useTemplate(templateId) {
            alert(`📋 Loading template: ${templateId}\nRedirecting to Create Bid with pre-filled data...`);
            setTimeout(() => {
                window.location.href = '/create-bid.html?template=' + templateId;
            }, 1500);
        }

        function editTemplate(templateId) {
            alert(`✏️ Opening template editor for: ${templateId}`);
        }

        function createTemplate() {
            alert('➕ Opening new template creator...');
        }

        // Initialize with all templates
        filterTemplates('all');
    </script>
</body>
</html>
