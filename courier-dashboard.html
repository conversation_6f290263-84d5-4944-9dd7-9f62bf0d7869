<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BidBeez - Courier Management Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .courier-card { transition: all 0.3s ease; }
        .courier-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .delivery-status { animation: pulse 2s infinite; }
        .route-indicator { animation: flow 2s infinite; }
        @keyframes flow { 0%, 100% { transform: translateX(0); } 50% { transform: translateX(10px); } }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="/dashboard.html" class="text-blue-600 hover:text-blue-800 mr-4">← Back to Dashboard</a>
                    <h1 class="text-3xl font-bold text-blue-600">🚚 Courier Management</h1>
                    <span class="ml-3 text-sm text-gray-500">Delivery & Logistics Control Center</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-green-600 delivery-status">🚛 8 Active Deliveries</span>
                    <button onclick="scheduleDelivery()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">📦 Schedule Delivery</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Quick Stats -->
    <div class="bg-blue-50 border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="grid grid-cols-2 md:grid-cols-6 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">8</div>
                    <div class="text-xs text-gray-600">Active Deliveries</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">23</div>
                    <div class="text-xs text-gray-600">Completed Today</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-600">5</div>
                    <div class="text-xs text-gray-600">Scheduled</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">12</div>
                    <div class="text-xs text-gray-600">Available Couriers</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-orange-600">97%</div>
                    <div class="text-xs text-gray-600">On-Time Rate</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-red-600">1</div>
                    <div class="text-xs text-gray-600">Delayed</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Active Deliveries -->
        <div class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-900 mb-6">🚛 Active Deliveries</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Delivery 1: Urgent Document -->
                <div class="courier-card bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">📄 Urgent Bid Documents</h3>
                                <p class="text-sm text-gray-600">Tender submission to Municipal Offices</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <span class="delivery-status w-2 h-2 bg-red-400 rounded-full mr-1"></span>
                                    URGENT
                                </span>
                                <button onclick="trackDelivery('DEL-001')" class="text-blue-600 hover:text-blue-800">📍</button>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Courier:</span>
                                <span class="text-sm font-medium">John Mthembu</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">From:</span>
                                <span class="text-sm font-medium">Office - Sandton</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">To:</span>
                                <span class="text-sm font-medium">City Hall - Johannesburg</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Deadline:</span>
                                <span class="text-sm font-medium text-red-600">Today 2:00 PM</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">ETA:</span>
                                <span class="text-sm font-medium text-green-600">1:45 PM</span>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">Progress</span>
                                <span class="text-sm text-gray-500">85%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="route-indicator bg-red-600 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                        <div class="mt-4 flex space-x-2">
                            <button onclick="callCourier('john-mthembu')" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">📞 Call</button>
                            <button onclick="trackDelivery('DEL-001')" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">📍 Track</button>
                        </div>
                    </div>
                </div>

                <!-- Delivery 2: Equipment Transport -->
                <div class="courier-card bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">🔧 Equipment Delivery</h3>
                                <p class="text-sm text-gray-600">Testing equipment to construction site</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <span class="delivery-status w-2 h-2 bg-blue-400 rounded-full mr-1"></span>
                                    IN TRANSIT
                                </span>
                                <button onclick="trackDelivery('DEL-002')" class="text-blue-600 hover:text-blue-800">📍</button>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Driver:</span>
                                <span class="text-sm font-medium">Sarah Ndlovu</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Vehicle:</span>
                                <span class="text-sm font-medium">Truck - ABC 123 GP</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">From:</span>
                                <span class="text-sm font-medium">Warehouse - Midrand</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">To:</span>
                                <span class="text-sm font-medium">Site - Pretoria North</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">ETA:</span>
                                <span class="text-sm font-medium text-blue-600">3:30 PM</span>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">Progress</span>
                                <span class="text-sm text-gray-500">62%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="route-indicator bg-blue-600 h-2 rounded-full" style="width: 62%"></div>
                            </div>
                        </div>
                        <div class="mt-4 flex space-x-2">
                            <button onclick="callCourier('sarah-ndlovu')" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">📞 Call</button>
                            <button onclick="trackDelivery('DEL-002')" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">📍 Track</button>
                        </div>
                    </div>
                </div>

                <!-- Delivery 3: Document Collection -->
                <div class="courier-card bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">📋 Document Collection</h3>
                                <p class="text-sm text-gray-600">BEE certificates from verification agency</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <span class="delivery-status w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                                    COLLECTING
                                </span>
                                <button onclick="trackDelivery('DEL-003')" class="text-blue-600 hover:text-blue-800">📍</button>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Courier:</span>
                                <span class="text-sm font-medium">Mike Sithole</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Collection Point:</span>
                                <span class="text-sm font-medium">BEE Verification Agency</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Delivery To:</span>
                                <span class="text-sm font-medium">Office - Sandton</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Priority:</span>
                                <span class="text-sm font-medium text-green-600">Standard</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">ETA:</span>
                                <span class="text-sm font-medium text-green-600">4:15 PM</span>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">Progress</span>
                                <span class="text-sm text-gray-500">25%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="route-indicator bg-green-600 h-2 rounded-full" style="width: 25%"></div>
                            </div>
                        </div>
                        <div class="mt-4 flex space-x-2">
                            <button onclick="callCourier('mike-sithole')" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">📞 Call</button>
                            <button onclick="linkToBEE()" class="bg-orange-600 text-white px-3 py-1 rounded text-sm hover:bg-orange-700">🏛️ BEE</button>
                        </div>
                    </div>
                </div>

                <!-- Delivery 4: Delayed Delivery -->
                <div class="courier-card bg-white rounded-lg shadow border-l-4 border-red-500">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">⚠️ Contract Documents</h3>
                                <p class="text-sm text-gray-600">Signed contracts to client office</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <span class="delivery-status w-2 h-2 bg-red-400 rounded-full mr-1"></span>
                                    DELAYED
                                </span>
                                <button onclick="trackDelivery('DEL-004')" class="text-blue-600 hover:text-blue-800">📍</button>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Courier:</span>
                                <span class="text-sm font-medium">Peter Molefe</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Issue:</span>
                                <span class="text-sm font-medium text-red-600">Traffic Delay</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Original ETA:</span>
                                <span class="text-sm font-medium text-gray-500">2:30 PM</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">New ETA:</span>
                                <span class="text-sm font-medium text-red-600">3:45 PM</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Client Notified:</span>
                                <span class="text-sm font-medium text-green-600">Yes</span>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">Progress</span>
                                <span class="text-sm text-gray-500">78%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-red-600 h-2 rounded-full" style="width: 78%"></div>
                            </div>
                        </div>
                        <div class="mt-4 flex space-x-2">
                            <button onclick="callCourier('peter-molefe')" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700">📞 Urgent Call</button>
                            <button onclick="notifyClient('DEL-004')" class="bg-orange-600 text-white px-3 py-1 rounded text-sm hover:bg-orange-700">📧 Notify Client</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Courier Fleet -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Available Couriers -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900">👥 Available Couriers</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold">JM</div>
                                <div class="ml-3">
                                    <div class="font-medium text-gray-900">James Mabena</div>
                                    <div class="text-sm text-gray-600">Motorcycle • Sandton Area</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-green-600">Available</div>
                                <div class="text-xs text-gray-500">⭐ 4.9 rating</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold">LN</div>
                                <div class="ml-3">
                                    <div class="font-medium text-gray-900">Linda Nkomo</div>
                                    <div class="text-sm text-gray-600">Van • Johannesburg</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-green-600">Available</div>
                                <div class="text-xs text-gray-500">⭐ 4.8 rating</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold">TM</div>
                                <div class="ml-3">
                                    <div class="font-medium text-gray-900">Thabo Mokoena</div>
                                    <div class="text-sm text-gray-600">Truck • Pretoria</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-green-600">Available</div>
                                <div class="text-xs text-gray-500">⭐ 4.7 rating</div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-6">
                        <button onclick="viewAllCouriers()" class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
                            View All Couriers
                        </button>
                    </div>
                </div>
            </div>

            <!-- Delivery Analytics -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900">📊 Delivery Analytics</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-6">
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">On-Time Delivery Rate</span>
                                <span class="text-sm text-green-600">97%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-600 h-2 rounded-full" style="width: 97%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">Customer Satisfaction</span>
                                <span class="text-sm text-blue-600">4.8/5</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 96%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">Fleet Utilization</span>
                                <span class="text-sm text-purple-600">78%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-purple-600 h-2 rounded-full" style="width: 78%"></div>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4 mt-6">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600">156</div>
                                <div class="text-xs text-gray-600">This Month</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600">R12.4K</div>
                                <div class="text-xs text-gray-600">Revenue</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function scheduleDelivery() {
            alert('📦 Schedule New Delivery\n\nDelivery options:\n• Document delivery\n• Equipment transport\n• Urgent courier service\n• Bulk deliveries\n• Scheduled pickups\n\nSelect delivery type and schedule.');
        }

        function trackDelivery(deliveryId) {
            alert(`📍 Tracking Delivery: ${deliveryId}\n\nReal-time tracking:\n• Current location\n• Route progress\n• ETA updates\n• Traffic conditions\n• Delivery status\n\nLive tracking active!`);
        }

        function callCourier(courierId) {
            const couriers = {
                'john-mthembu': 'John Mthembu',
                'sarah-ndlovu': 'Sarah Ndlovu',
                'mike-sithole': 'Mike Sithole',
                'peter-molefe': 'Peter Molefe'
            };
            
            alert(`📞 Calling ${couriers[courierId]}...\n\n✅ Connection established\n✅ Location confirmed\n✅ Status updated\n✅ Instructions given\n\nCourier contacted successfully!`);
        }

        function notifyClient(deliveryId) {
            alert(`📧 Notifying Client about ${deliveryId}\n\n✅ SMS sent\n✅ Email notification\n✅ WhatsApp update\n✅ Delivery tracking link\n\nClient has been informed of the delay.`);
        }

        function linkToBEE() {
            window.open('/bee-compliance-dashboard.html', '_blank');
        }

        function viewAllCouriers() {
            alert('👥 Courier Fleet Management\n\nFleet overview:\n• 12 available couriers\n• 8 currently on delivery\n• Performance ratings\n• Vehicle assignments\n• Availability schedules\n\nManage your courier network!');
        }

        // Simulate real-time delivery updates
        setInterval(() => {
            console.log(`[${new Date().toLocaleTimeString()}] Courier Management: Monitoring 8 active deliveries and 12 couriers`);
        }, 30000);
    </script>
</body>
</html>
