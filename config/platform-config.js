// BidBeez Platform Configuration
// Complete backend integration configuration

const PLATFORM_CONFIG = {
    // Supabase Configuration
    supabase: {
        url: 'https://uvksgkpxeyyssvdsxbts.supabase.co',
        anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV2a3Nna3B4ZXl5c3N2ZHN4YnRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTQ0OTc0NDIsImV4cCI6MjAzMDA3MzQ0Mn0.Qs8ZQgOJlrKJZqJQJQJQJQJQJQJQJQJQJQJQJQJQJQJQ',
        serviceRoleKey: 'service_role_key_here', // Replace with actual service role key
        region: 'eu-central-1',
        projectId: 'uvksgkpxeyyssvdsxbts'
    },

    // API Endpoints
    api: {
        baseUrl: 'http://localhost:8000',
        endpoints: {
            // Authentication
            auth: '/auth',
            login: '/auth/login',
            register: '/auth/register',

            // Core Business
            tenders: '/api/tenders',
            bids: '/api/bids',
            users: '/api/users',

            // QueenBee System
            queenbee: '/api/queenbee',
            beeProfiles: '/api/bee-profiles',
            beeTasks: '/api/bee-tasks',
            beeLocations: '/api/bee-locations',
            beeAssignments: '/api/bee-assignments',

            // Skills & Tools
            skills: '/api/skills',
            skillProviders: '/api/skill-providers',
            tools: '/api/tools',
            toolInventory: '/api/tool-inventory',

            // Contractors
            contractors: '/api/contractors',
            contractorProfiles: '/api/contractor-profiles',

            // Compliance
            compliance: '/api/compliance',
            beeCompliance: '/api/bee-compliance',

            // Analytics
            analytics: '/api/analytics',
            reports: '/api/reports'
        }
    },

    // Database Tables Structure
    database: {
        tables: {
            // Core Business Tables
            tenders: 'tenders',
            bids: 'bids',
            users: 'users',

            // QueenBee System Tables
            beeProfiles: 'bee_profiles',
            beeTasks: 'bee_tasks',
            beeLocations: 'bee_locations',
            beeHeartbeats: 'bee_heartbeats',
            beeRatings: 'bee_ratings',
            beeWallets: 'bee_wallets',
            beeAvailability: 'bee_availability',
            beeRoutes: 'bee_routes',
            beeVerifications: 'bee_verifications',
            beeRiskProfiles: 'bee_risk_profiles',
            beeCourierAssignments: 'bee_courier_assignments',
            beeRecruitmentRequests: 'bee_recruitment_requests',
            queenBeeLogs: 'queen_bee_logs',
            queenbeeAnchorBatches: 'queenbee_anchor_batches',
            queenbeeAnchorProofs: 'queenbee_anchor_proofs',
            queenbeeBeeProfilesMock: 'queenbee_bee_profiles_mock',
            queenbeeDistancesMock: 'queenbee_distances_mock',
            queenbeeLocationsMock: 'queenbee_locations_mock',

            // Skills & Training Tables
            skills: 'skills',
            skillExample: 'skill_example',
            skillDemandAnalysis: 'skill_demand_analysis',
            skillProviderProfiles: 'skill_provider_profiles',
            skillProviderQualifications: 'skill_provider_qualifications',
            skillProviderRatings: 'skill_provider_ratings',
            skillProviderSkills: 'skill_provider_skills',
            skillProviderVerifications: 'skill_provider_verifications',

            // Contractor System Tables
            contractorProfiles: 'contractor_profiles',
            contractorQuoteRequests: 'contractor_quote_requests',
            contractorSupplierAccess: 'contractor_supplier_access',

            // DroneContractor System (Extended Contractor Features)
            dronecontractorAssignment: 'dronecontractor_assignment',
            dronecontractorBidderprofile: 'dronecontractor_bidderprofile',
            dronecontractorCertification: 'dronecontractor_certification',
            dronecontractorTask: 'dronecontractor_task',
            dronecontractorGigworker: 'dronecontractor_gigworker',
            dronecontractorSkill: 'dronecontractor_skill',
            dronecontractorFeedback: 'dronecontractor_feedback',
            dronecontractorNotification: 'dronecontractor_notification',

            // Compliance Tables
            complianceValidations: 'compliance_validations',
            supplierComplianceRecord: 'supplier_compliance_record'
        }
    },

    // Platform Features Configuration
    features: {
        // QueenBee Management
        queenbee: {
            enabled: true,
            maxWorkerBees: 50,
            territoryRadius: 15, // km
            realTimeTracking: true,
            taskAutoAssignment: true,
            performanceMonitoring: true
        },

        // Skills Management
        skillsync: {
            enabled: true,
            certificationTracking: true,
            trainingMarketplace: true,
            skillGapAnalysis: true,
            providerVerification: true
        },

        // Tool Management
        toolsync: {
            enabled: true,
            inventoryTracking: true,
            maintenanceScheduling: true,
            toolSharing: true,
            marketplace: true
        },

        // Contractor Management
        contractorsync: {
            enabled: true,
            profileManagement: true,
            quoteRequests: true,
            supplierAccess: true,
            performanceTracking: true
        },

        // BEE Compliance
        beeCompliance: {
            enabled: true,
            scorecardTracking: true,
            complianceCalendar: true,
            reportGeneration: true,
            deadlineAlerts: true
        },

        // Automation
        taskRunner: {
            enabled: true,
            bidMonitoring: true,
            complianceChecking: true,
            maintenanceScheduling: true,
            notificationAutomation: true
        },

        // Courier Management
        courierManagement: {
            enabled: true,
            realTimeTracking: true,
            routeOptimization: true,
            deliveryAnalytics: true,
            fleetManagement: true
        }
    },

    // UI Configuration
    ui: {
        theme: {
            primary: '#3B82F6', // Blue
            secondary: '#8B5CF6', // Purple
            success: '#10B981', // Green
            warning: '#F59E0B', // Orange
            error: '#EF4444', // Red
            info: '#06B6D4' // Cyan
        },

        dashboards: {
            main: '/dashboard.html',
            queenbee: '/queenbee-dashboard.html',
            skillsync: '/skillsync-dashboard.html',
            toolsync: '/toolsync-dashboard.html',
            contractorsync: '/contractorsync-hub.html',
            beeCompliance: '/bee-compliance-dashboard.html',
            taskRunner: '/task-runner-dashboard.html',
            courier: '/courier-dashboard.html',
            bids: '/bids-dashboard.html',
            whatsapp: '/whatsapp-dashboard.html',
            analytics: '/analytics-dashboard.html'
        },

        pages: {
            masterIndex: '/master-index.html',
            pageGenerator: '/page-generator.html',
            userProfile: '/user-profile.html',
            notifications: '/notifications.html',
            platformOverview: '/complete-platform-overview.html'
        }
    },

    // Real-time Configuration
    realtime: {
        enabled: true,
        channels: {
            beeLocations: 'bee_locations',
            beeTasks: 'bee_tasks',
            beeHeartbeats: 'bee_heartbeats',
            tenders: 'tenders',
            bids: 'bids',
            notifications: 'notifications'
        },
        updateInterval: 5000 // 5 seconds
    },

    // Performance Configuration
    performance: {
        caching: {
            enabled: true,
            duration: 300000 // 5 minutes
        },
        pagination: {
            defaultPageSize: 20,
            maxPageSize: 100
        },
        optimization: {
            lazyLoading: true,
            imageCompression: true,
            bundleMinification: true
        }
    }
};

// Export configuration
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PLATFORM_CONFIG;
} else if (typeof window !== 'undefined') {
    window.PLATFORM_CONFIG = PLATFORM_CONFIG;
}