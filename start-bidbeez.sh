#!/bin/bash

# BidBeez Complete Application Startup Script
# This script starts all components of the BidBeez platform

set -e

echo "🚀 Starting BidBeez - Psychological Engagement Platform"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "start-bidbeez.sh" ]; then
    print_error "Please run this script from the bidbeez-new-frontend-main directory"
    exit 1
fi

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        return 0
    else
        return 1
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    print_info "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            print_status "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within expected time"
    return 1
}

# Kill any existing processes on our ports
print_info "Cleaning up any existing processes..."
if check_port 8000; then
    print_warning "Port 8000 is in use, attempting to free it..."
    lsof -ti:8000 | xargs kill -9 2>/dev/null || true
fi

if check_port 3000; then
    print_warning "Port 3000 is in use, attempting to free it..."
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Start API Server
print_info "Starting BidBeez API Server (Backend)..."
cd api

# Check if Python dependencies are installed
if ! python3 -c "import fastapi" 2>/dev/null; then
    print_info "Installing Python dependencies..."
    pip3 install fastapi uvicorn pydantic supabase python-multipart python-dotenv gunicorn
fi

# Start the API server in background
SUPABASE_URL=https://uvksgkpxeyyssvdsxbts.supabase.co \
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_key \
python3 -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload > ../logs/api.log 2>&1 &

API_PID=$!
echo $API_PID > ../logs/api.pid

cd ..

# Wait for API to be ready
wait_for_service "http://localhost:8000/health" "API Server"

# Start Frontend Development Server
print_info "Starting BidBeez Frontend..."
cd frontend

# Load NVM and install Node.js if needed
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    print_info "Installing Node.js..."
    nvm install node
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    print_info "Installing frontend dependencies..."
    npm install
fi

# Start the frontend server in background
npm run dev > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
echo $FRONTEND_PID > ../logs/frontend.pid

cd ..

# Wait a moment for frontend to start
sleep 5

# Display status
echo ""
echo "🎯 BidBeez Platform Status"
echo "========================="
print_status "API Server: Running on http://localhost:8000"
print_status "Frontend: Running on http://localhost:3000"
print_status "Demo Dashboard: file://$(pwd)/frontend/dashboard-demo.html"

echo ""
print_info "All 92+ pages are accessible through the navigation system:"
echo "  📊 Analytics Dashboard (10 pages)"
echo "  📝 Bid Management (10 pages)" 
echo "  🛡️  Compliance Dashboard (10 pages)"
echo "  👥 Supplier Management (10 pages)"
echo "  💬 WhatsApp Integration (10 pages)"
echo "  🏠 Main Dashboard & 42+ additional pages"

echo ""
print_info "Psychological Systems Status:"
curl -s http://localhost:8000/health | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    services = data.get('services', {})
    for service, status in services.items():
        print(f'  ✅ {service.replace(\"_\", \" \").title()}: {status}')
except:
    print('  ⚠️  Unable to fetch system status')
"

echo ""
print_info "Quick Access URLs:"
echo "  🌐 Main Application: http://localhost:3000"
echo "  📡 API Documentation: http://localhost:8000/docs"
echo "  💊 Health Check: http://localhost:8000/health"
echo "  📋 Demo Dashboard: file://$(pwd)/frontend/dashboard-demo.html"

echo ""
print_info "Process IDs saved to logs/ directory for easy management"
print_info "Logs are being written to logs/api.log and logs/frontend.log"

echo ""
print_warning "To stop all services, run: ./stop-bidbeez.sh"

# Open the demo dashboard in browser
if command -v open &> /dev/null; then
    print_info "Opening demo dashboard in browser..."
    open "file://$(pwd)/frontend/dashboard-demo.html"
elif command -v xdg-open &> /dev/null; then
    print_info "Opening demo dashboard in browser..."
    xdg-open "file://$(pwd)/frontend/dashboard-demo.html"
fi

echo ""
print_status "🎉 BidBeez Platform is now running with all 92+ pages accessible!"
print_info "Press Ctrl+C to stop all services, or run ./stop-bidbeez.sh"

# Wait for user interrupt
trap 'echo ""; print_info "Shutting down BidBeez Platform..."; kill $API_PID $FRONTEND_PID 2>/dev/null; exit 0' INT

# Keep script running
while true; do
    sleep 10
    # Check if processes are still running
    if ! kill -0 $API_PID 2>/dev/null; then
        print_error "API Server stopped unexpectedly"
        break
    fi
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        print_error "Frontend Server stopped unexpectedly"
        break
    fi
done
