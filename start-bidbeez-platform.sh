#!/bin/bash

# BidBeez Platform Startup Script
# Comprehensive script to start all platform components

echo "🚀 Starting BidBeez Platform..."
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}$1${NC}"
}

# Check if required tools are installed
check_dependencies() {
    print_header "🔍 Checking Dependencies..."
    
    # Check for Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_status "Node.js found: $NODE_VERSION"
    else
        print_error "Node.js not found. Please install Node.js first."
        exit 1
    fi
    
    # Check for Python
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version)
        print_status "Python found: $PYTHON_VERSION"
    else
        print_error "Python3 not found. Please install Python3 first."
        exit 1
    fi
    
    # Check for required ports
    check_port() {
        if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
            print_warning "Port $1 is already in use"
            return 1
        else
            print_status "Port $1 is available"
            return 0
        fi
    }
    
    print_status "Checking required ports..."
    check_port 3000  # Frontend
    check_port 8000  # Backend API
}

# Start backend services
start_backend() {
    print_header "🔧 Starting Backend Services..."
    
    # Check if backend directory exists
    if [ -d "../backend" ]; then
        cd ../backend
        
        # Install dependencies if needed
        if [ ! -d "venv" ]; then
            print_status "Creating Python virtual environment..."
            python3 -m venv venv
        fi
        
        # Activate virtual environment
        source venv/bin/activate
        
        # Install requirements
        if [ -f "requirements.txt" ]; then
            print_status "Installing Python dependencies..."
            pip install -r requirements.txt
        fi
        
        # Start FastAPI backend
        print_status "Starting FastAPI backend on port 8000..."
        uvicorn main:app --host 0.0.0.0 --port 8000 --reload &
        BACKEND_PID=$!
        
        # Wait for backend to start
        sleep 5
        
        # Check if backend is running
        if curl -s http://localhost:8000/health > /dev/null; then
            print_status "✅ Backend API is running on http://localhost:8000"
            print_status "📚 API Documentation: http://localhost:8000/docs"
        else
            print_error "❌ Backend failed to start"
        fi
        
        cd ../frontend
    else
        print_warning "Backend directory not found. Starting frontend only."
    fi
}

# Start frontend services
start_frontend() {
    print_header "🌐 Starting Frontend Services..."
    
    # Check if we're in the frontend directory
    if [ ! -f "dashboard.html" ]; then
        print_error "Frontend files not found. Please run this script from the frontend directory."
        exit 1
    fi
    
    # Start HTTP server for frontend
    print_status "Starting frontend HTTP server on port 3000..."
    
    # Try different HTTP server options
    if command -v python3 &> /dev/null; then
        python3 -m http.server 3000 &
        FRONTEND_PID=$!
        print_status "✅ Frontend server started using Python HTTP server"
    elif command -v node &> /dev/null && npm list -g live-server &> /dev/null; then
        live-server --port=3000 &
        FRONTEND_PID=$!
        print_status "✅ Frontend server started using live-server"
    elif command -v php &> /dev/null; then
        php -S localhost:3000 &
        FRONTEND_PID=$!
        print_status "✅ Frontend server started using PHP built-in server"
    else
        print_error "No suitable HTTP server found. Please install Python3, Node.js with live-server, or PHP."
        exit 1
    fi
    
    # Wait for frontend to start
    sleep 3
    
    # Check if frontend is running
    if curl -s http://localhost:3000 > /dev/null; then
        print_status "✅ Frontend is running on http://localhost:3000"
    else
        print_error "❌ Frontend failed to start"
    fi
}

# Display access information
show_access_info() {
    print_header "🎯 BidBeez Platform Access Information"
    echo ""
    echo -e "${CYAN}🏠 Main Dashboard:${NC}           http://localhost:3000/dashboard.html"
    echo -e "${CYAN}🗺️  Master Index:${NC}            http://localhost:3000/master-index.html"
    echo -e "${CYAN}🚀 Master Launcher:${NC}          http://localhost:3000/launch-all-dashboards.html"
    echo -e "${CYAN}👑 QueenBee Command:${NC}         http://localhost:3000/queenbee-dashboard.html"
    echo -e "${CYAN}🎓 SkillSync:${NC}                http://localhost:3000/skillsync-dashboard.html"
    echo -e "${CYAN}🔧 ToolSync:${NC}                 http://localhost:3000/toolsync-dashboard.html"
    echo -e "${CYAN}🤝 ContractorSync:${NC}           http://localhost:3000/contractorsync-hub.html"
    echo -e "${CYAN}🏛️  BEE Compliance:${NC}          http://localhost:3000/bee-compliance-dashboard.html"
    echo -e "${CYAN}⚡ Task Runner:${NC}              http://localhost:3000/task-runner-dashboard.html"
    echo -e "${CYAN}🚚 Courier Management:${NC}       http://localhost:3000/courier-dashboard.html"
    echo -e "${CYAN}📝 Bids Management:${NC}          http://localhost:3000/bids-dashboard.html"
    echo -e "${CYAN}💬 WhatsApp Integration:${NC}     http://localhost:3000/whatsapp-dashboard.html"
    echo -e "${CYAN}📊 Analytics:${NC}                http://localhost:3000/analytics-dashboard.html"
    echo -e "${CYAN}🎯 Platform Overview:${NC}        http://localhost:3000/complete-platform-overview.html"
    echo ""
    echo -e "${BLUE}🔧 Backend API:${NC}              http://localhost:8000"
    echo -e "${BLUE}📚 API Documentation:${NC}        http://localhost:8000/docs"
    echo -e "${BLUE}❤️  Health Check:${NC}            http://localhost:8000/health"
    echo ""
    echo -e "${GREEN}🎉 Total Pages Available: 99+${NC}"
    echo -e "${GREEN}🔄 Real-time Backend Integration: Active${NC}"
    echo -e "${GREEN}🧠 AI-Powered Optimization: Enabled${NC}"
    echo ""
}

# Create desktop shortcuts (optional)
create_shortcuts() {
    print_header "🔗 Creating Desktop Shortcuts..."
    
    # Create shortcuts directory
    mkdir -p ~/Desktop/BidBeez-Shortcuts
    
    # Main dashboard shortcut
    cat > ~/Desktop/BidBeez-Shortcuts/BidBeez-Dashboard.url << EOF
[InternetShortcut]
URL=http://localhost:3000/dashboard.html
IconFile=
IconIndex=0
EOF
    
    # Master launcher shortcut
    cat > ~/Desktop/BidBeez-Shortcuts/BidBeez-Launcher.url << EOF
[InternetShortcut]
URL=http://localhost:3000/launch-all-dashboards.html
IconFile=
IconIndex=0
EOF
    
    print_status "✅ Desktop shortcuts created in ~/Desktop/BidBeez-Shortcuts/"
}

# Cleanup function
cleanup() {
    print_header "🧹 Cleaning up..."
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
        print_status "Frontend server stopped"
    fi
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
        print_status "Backend server stopped"
    fi
    
    print_status "BidBeez Platform stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Main execution
main() {
    print_header "🧠 BidBeez Platform Startup"
    print_header "Complete Business Management Ecosystem"
    echo ""
    
    # Check dependencies
    check_dependencies
    echo ""
    
    # Start services
    start_backend
    echo ""
    start_frontend
    echo ""
    
    # Show access information
    show_access_info
    
    # Optional: Create shortcuts
    read -p "Create desktop shortcuts? (y/n): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        create_shortcuts
        echo ""
    fi
    
    print_header "🎯 Platform Status: FULLY OPERATIONAL"
    print_status "Press Ctrl+C to stop all services"
    
    # Keep script running
    while true; do
        sleep 10
        
        # Check if services are still running
        if ! curl -s http://localhost:3000 > /dev/null; then
            print_error "Frontend service stopped unexpectedly"
            break
        fi
        
        if ! curl -s http://localhost:8000/health > /dev/null; then
            print_warning "Backend service may have stopped"
        fi
    done
}

# Run main function
main "$@"
