import React, { Suspense } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Toaster } from '@/components/ui/toaster'
import { AuthProvider } from '@/contexts/AuthContext'
import { LoadingSpinner } from '@/components/common/LoadingSpinner'
import { ErrorBoundary } from '@/components/common/ErrorBoundary'

// Lazy load page components for better performance
const Dashboard = React.lazy(() => import('@/pages/dashboard/Dashboard'))
const Login = React.lazy(() => import('@/pages/auth/Login'))
const Register = React.lazy(() => import('@/pages/auth/Register'))

// Analytics Pages
const AnalyticsDashboard = React.lazy(() => import('@/pages/analytics/AnalyticsDashboard'))
const BidAnalytics = React.lazy(() => import('@/pages/analytics/BidAnalytics'))
const TenderAnalytics = React.lazy(() => import('@/pages/analytics/TenderAnalytics'))
const SupplierAnalytics = React.lazy(() => import('@/pages/analytics/SupplierAnalytics'))
const PerformanceMetrics = React.lazy(() => import('@/pages/analytics/PerformanceMetrics'))
const PsychologicalInsights = React.lazy(() => import('@/pages/analytics/PsychologicalInsights'))
const BehavioralReports = React.lazy(() => import('@/pages/analytics/BehavioralReports'))
const MarketIntelligence = React.lazy(() => import('@/pages/analytics/MarketIntelligence'))
const CompetitorAnalysis = React.lazy(() => import('@/pages/analytics/CompetitorAnalysis'))
const ROIAnalysis = React.lazy(() => import('@/pages/analytics/ROIAnalysis'))

// Bid Management Pages
const BidDashboard = React.lazy(() => import('@/pages/bids/BidDashboard'))
const CreateBid = React.lazy(() => import('@/pages/bids/CreateBid'))
const BidHistory = React.lazy(() => import('@/pages/bids/BidHistory'))
const BidTracking = React.lazy(() => import('@/pages/bids/BidTracking'))
const BidTemplates = React.lazy(() => import('@/pages/bids/BidTemplates'))
const BidCollaboration = React.lazy(() => import('@/pages/bids/BidCollaboration'))
const BidOptimization = React.lazy(() => import('@/pages/bids/BidOptimization'))
const BidSubmission = React.lazy(() => import('@/pages/bids/BidSubmission'))
const BidEvaluation = React.lazy(() => import('@/pages/bids/BidEvaluation'))
const BidSuccess = React.lazy(() => import('@/pages/bids/BidSuccess'))

// Compliance Pages
const ComplianceDashboard = React.lazy(() => import('@/pages/compliance/ComplianceDashboard'))
const BidProtests = React.lazy(() => import('@/pages/compliance/BidProtests'))
const RegulatoryCompliance = React.lazy(() => import('@/pages/compliance/RegulatoryCompliance'))
const DocumentCompliance = React.lazy(() => import('@/pages/compliance/DocumentCompliance'))
const BBBEECompliance = React.lazy(() => import('@/pages/compliance/BBBEECompliance'))
const LegalRequirements = React.lazy(() => import('@/pages/compliance/LegalRequirements'))
const ComplianceReports = React.lazy(() => import('@/pages/compliance/ComplianceReports'))
const AuditTrail = React.lazy(() => import('@/pages/compliance/AuditTrail'))
const ComplianceAlerts = React.lazy(() => import('@/pages/compliance/ComplianceAlerts'))
const ComplianceTraining = React.lazy(() => import('@/pages/compliance/ComplianceTraining'))

// Supplier Pages
const SupplierDashboard = React.lazy(() => import('@/pages/supplier/SupplierDashboard'))
const SupplierDirectory = React.lazy(() => import('@/pages/supplier/SupplierDirectory'))
const SupplierProfiles = React.lazy(() => import('@/pages/supplier/SupplierProfiles'))
const SupplierMatching = React.lazy(() => import('@/pages/supplier/SupplierMatching'))
const SupplierEvaluation = React.lazy(() => import('@/pages/supplier/SupplierEvaluation'))
const SupplierContracts = React.lazy(() => import('@/pages/supplier/SupplierContracts'))
const SupplierPerformance = React.lazy(() => import('@/pages/supplier/SupplierPerformance'))
const SupplierPayments = React.lazy(() => import('@/pages/supplier/SupplierPayments'))
const SupplierCommunication = React.lazy(() => import('@/pages/supplier/SupplierCommunication'))
const SupplierOnboarding = React.lazy(() => import('@/pages/supplier/SupplierOnboarding'))

// WhatsApp Integration Pages
const WhatsAppDashboard = React.lazy(() => import('@/pages/whatsapp/WhatsAppDashboard'))
const AutoBidding = React.lazy(() => import('@/pages/whatsapp/AutoBidding'))
const WhatsAppNotifications = React.lazy(() => import('@/pages/whatsapp/WhatsAppNotifications'))
const WhatsAppSettings = React.lazy(() => import('@/pages/whatsapp/WhatsAppSettings'))
const WhatsAppAnalytics = React.lazy(() => import('@/pages/whatsapp/WhatsAppAnalytics'))
const WhatsAppTemplates = React.lazy(() => import('@/pages/whatsapp/WhatsAppTemplates'))
const WhatsAppIntegration = React.lazy(() => import('@/pages/whatsapp/WhatsAppIntegration'))
const WhatsAppBot = React.lazy(() => import('@/pages/whatsapp/WhatsAppBot'))
const WhatsAppReports = React.lazy(() => import('@/pages/whatsapp/WhatsAppReports'))
const WhatsAppSupport = React.lazy(() => import('@/pages/whatsapp/WhatsAppSupport'))

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <div className="min-h-screen bg-background">
          <Suspense fallback={<LoadingSpinner />}>
            <Routes>
              {/* Auth Routes */}
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              
              {/* Dashboard */}
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              
              {/* Analytics Routes */}
              <Route path="/analytics" element={<AnalyticsDashboard />} />
              <Route path="/analytics/bids" element={<BidAnalytics />} />
              <Route path="/analytics/tenders" element={<TenderAnalytics />} />
              <Route path="/analytics/suppliers" element={<SupplierAnalytics />} />
              <Route path="/analytics/performance" element={<PerformanceMetrics />} />
              <Route path="/analytics/psychological" element={<PsychologicalInsights />} />
              <Route path="/analytics/behavioral" element={<BehavioralReports />} />
              <Route path="/analytics/market" element={<MarketIntelligence />} />
              <Route path="/analytics/competitors" element={<CompetitorAnalysis />} />
              <Route path="/analytics/roi" element={<ROIAnalysis />} />
              
              {/* Bid Management Routes */}
              <Route path="/bids" element={<BidDashboard />} />
              <Route path="/bids/create" element={<CreateBid />} />
              <Route path="/bids/history" element={<BidHistory />} />
              <Route path="/bids/tracking" element={<BidTracking />} />
              <Route path="/bids/templates" element={<BidTemplates />} />
              <Route path="/bids/collaboration" element={<BidCollaboration />} />
              <Route path="/bids/optimization" element={<BidOptimization />} />
              <Route path="/bids/submission" element={<BidSubmission />} />
              <Route path="/bids/evaluation" element={<BidEvaluation />} />
              <Route path="/bids/success" element={<BidSuccess />} />
              
              {/* Compliance Routes */}
              <Route path="/compliance" element={<ComplianceDashboard />} />
              <Route path="/compliance/protests" element={<BidProtests />} />
              <Route path="/compliance/regulatory" element={<RegulatoryCompliance />} />
              <Route path="/compliance/documents" element={<DocumentCompliance />} />
              <Route path="/compliance/bbbee" element={<BBBEECompliance />} />
              <Route path="/compliance/legal" element={<LegalRequirements />} />
              <Route path="/compliance/reports" element={<ComplianceReports />} />
              <Route path="/compliance/audit" element={<AuditTrail />} />
              <Route path="/compliance/alerts" element={<ComplianceAlerts />} />
              <Route path="/compliance/training" element={<ComplianceTraining />} />
              
              {/* Supplier Routes */}
              <Route path="/suppliers" element={<SupplierDashboard />} />
              <Route path="/suppliers/directory" element={<SupplierDirectory />} />
              <Route path="/suppliers/profiles" element={<SupplierProfiles />} />
              <Route path="/suppliers/matching" element={<SupplierMatching />} />
              <Route path="/suppliers/evaluation" element={<SupplierEvaluation />} />
              <Route path="/suppliers/contracts" element={<SupplierContracts />} />
              <Route path="/suppliers/performance" element={<SupplierPerformance />} />
              <Route path="/suppliers/payments" element={<SupplierPayments />} />
              <Route path="/suppliers/communication" element={<SupplierCommunication />} />
              <Route path="/suppliers/onboarding" element={<SupplierOnboarding />} />
              
              {/* WhatsApp Routes */}
              <Route path="/whatsapp" element={<WhatsAppDashboard />} />
              <Route path="/whatsapp/autobid" element={<AutoBidding />} />
              <Route path="/whatsapp/notifications" element={<WhatsAppNotifications />} />
              <Route path="/whatsapp/settings" element={<WhatsAppSettings />} />
              <Route path="/whatsapp/analytics" element={<WhatsAppAnalytics />} />
              <Route path="/whatsapp/templates" element={<WhatsAppTemplates />} />
              <Route path="/whatsapp/integration" element={<WhatsAppIntegration />} />
              <Route path="/whatsapp/bot" element={<WhatsAppBot />} />
              <Route path="/whatsapp/reports" element={<WhatsAppReports />} />
              <Route path="/whatsapp/support" element={<WhatsAppSupport />} />
              
              {/* Catch all route */}
              <Route path="*" element={<Navigate to="/dashboard" replace />} />
            </Routes>
          </Suspense>
          <Toaster />
        </div>
      </AuthProvider>
    </ErrorBoundary>
  )
}

export default App
