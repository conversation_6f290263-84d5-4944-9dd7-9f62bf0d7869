import { useState, useEffect, useCallback } from 'react';
import ArchetypeDetectionService, {
  BidderArchetype,
  ArchetypeProfile,
  UserBehaviorData
} from '../services/ArchetypeDetectionService';

// Types for psychological systems
interface PsychologicalProfile {
  archetype: 'achiever' | 'hunter' | 'relationship_builder' | 'analyst';
  motivation_factors: string[];
  confidence_scores: Record<string, number>;
  psychological_state: {
    stress_level: number;
    motivation_level: number;
    confidence_level: number;
    urgency_response: number;
    competitive_spirit: number;
    financial_motivation: number;
  };
}

interface SalesTarget {
  target_id: string;
  target_type: string;
  period: string;
  target_value: number;
  current_value: number;
  completion_percentage: number;
  days_remaining: number;
}

interface Achievement {
  achievement_id: string;
  name: string;
  description: string;
  icon: string;
  tier: string;
  category: string;
  xp_reward: number;
  unlocked_at: string;
}

interface BehavioralNudge {
  nudge_id: string;
  trigger_type: string;
  message: string;
  intensity: string;
  psychological_principle: string;
}

interface OnboardingState {
  rep_id?: string;
  onboarding_state: string;
  feature_tier: string;
  progress_percentage: number;
  company_verification_status: string;
}

// API base URL - would be configured via environment
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export const usePsychologicalSystems = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for different psychological systems
  const [profile, setProfile] = useState<PsychologicalProfile | null>(null);
  const [targets, setTargets] = useState<SalesTarget[]>([]);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [nudges, setNudges] = useState<BehavioralNudge[]>([]);
  const [onboardingState, setOnboardingState] = useState<OnboardingState | null>(null);

  // Archetype detection state
  const [archetypeProfile, setArchetypeProfile] = useState<ArchetypeProfile | null>(null);
  const [behaviorData, setBehaviorData] = useState<UserBehaviorData | null>(null);

  const archetypeService = ArchetypeDetectionService.getInstance();

  // Generic API call function
  const apiCall = useCallback(async (endpoint: string, options: RequestInit = {}) => {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          // Add auth headers when available
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`API call failed: ${response.statusText}`);
      }

      return await response.json();
    } catch (err) {
      console.error(`API call to ${endpoint} failed:`, err);
      throw err;
    }
  }, []);

  // Sales Rep Profile Management
  const loadSalesRepProfile = useCallback(async (repId: string) => {
    try {
      setLoading(true);
      const data = await apiCall(`/sales-rep/${repId}/profile`);
      setProfile(data);
    } catch (err) {
      setError('Failed to load sales rep profile');
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  // Target Management
  const loadTargets = useCallback(async (repId: string, period: string = 'monthly') => {
    try {
      setLoading(true);
      const data = await apiCall(`/sales-rep/${repId}/targets?period=${period}`);
      setTargets(data.targets || []);
    } catch (err) {
      setError('Failed to load targets');
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  const createTarget = useCallback(async (repId: string, targetData: Partial<SalesTarget>) => {
    try {
      setLoading(true);
      const data = await apiCall(`/sales-rep/${repId}/targets`, {
        method: 'POST',
        body: JSON.stringify(targetData),
      });
      
      // Reload targets after creation
      await loadTargets(repId);
      return data;
    } catch (err) {
      setError('Failed to create target');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiCall, loadTargets]);

  // Achievement Management
  const loadAchievements = useCallback(async (repId: string) => {
    try {
      const data = await apiCall(`/sales-rep/${repId}/achievements`);
      setAchievements(data.achievements || []);
    } catch (err) {
      setError('Failed to load achievements');
    }
  }, [apiCall]);

  const checkAchievements = useCallback(async (repId: string, activityData: Record<string, any>) => {
    try {
      const data = await apiCall(`/sales-rep/${repId}/achievements/check`, {
        method: 'POST',
        body: JSON.stringify(activityData),
      });
      
      if (data.achievements && data.achievements.length > 0) {
        // Reload achievements to get updated list
        await loadAchievements(repId);
        return data.achievements;
      }
      
      return [];
    } catch (err) {
      console.error('Failed to check achievements:', err);
      return [];
    }
  }, [apiCall, loadAchievements]);

  // Behavioral Nudges
  const generateNudges = useCallback(async (repId: string, context: Record<string, any>) => {
    try {
      const data = await apiCall(`/sales-rep/${repId}/nudges`, {
        method: 'POST',
        body: JSON.stringify(context),
      });
      setNudges(data.nudges || []);
      return data.nudges;
    } catch (err) {
      setError('Failed to generate nudges');
      return [];
    }
  }, [apiCall]);

  // Onboarding Management
  const instantOnboard = useCallback(async (onboardingData: any) => {
    try {
      setLoading(true);
      const data = await apiCall('/sales-rep/instant-onboard', {
        method: 'POST',
        body: JSON.stringify(onboardingData),
      });
      
      setOnboardingState({
        rep_id: data.rep_id,
        onboarding_state: data.onboarding_state,
        feature_tier: 'solo_rep',
        progress_percentage: 15,
        company_verification_status: 'not_started'
      });
      
      return data;
    } catch (err) {
      setError('Failed to complete onboarding');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  const getOnboardingStatus = useCallback(async (repId: string) => {
    try {
      const data = await apiCall(`/sales-rep/${repId}/onboarding-status`);
      setOnboardingState(data);
      return data;
    } catch (err) {
      setError('Failed to get onboarding status');
      return null;
    }
  }, [apiCall]);

  const initiateCompanyOnboarding = useCallback(async (repId: string, companyData: any) => {
    try {
      setLoading(true);
      const data = await apiCall(`/sales-rep/${repId}/company-onboard`, {
        method: 'POST',
        body: JSON.stringify(companyData),
      });
      
      // Update onboarding state
      if (onboardingState) {
        setOnboardingState({
          ...onboardingState,
          onboarding_state: 'company_onboarding',
          feature_tier: 'team_rep',
          company_verification_status: 'in_progress'
        });
      }
      
      return data;
    } catch (err) {
      setError('Failed to initiate company onboarding');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiCall, onboardingState]);

  // Contractor-Supplier Access
  const findSuppliers = useCallback(async (contractorId: string, searchCriteria: any) => {
    try {
      setLoading(true);
      const data = await apiCall(`/contractor/${contractorId}/find-suppliers`, {
        method: 'POST',
        body: JSON.stringify(searchCriteria),
      });
      return data.suppliers || [];
    } catch (err) {
      setError('Failed to find suppliers');
      return [];
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  const requestQuote = useCallback(async (contractorId: string, quoteRequest: any) => {
    try {
      setLoading(true);
      const data = await apiCall(`/contractor/${contractorId}/request-quote`, {
        method: 'POST',
        body: JSON.stringify(quoteRequest),
      });
      return data;
    } catch (err) {
      setError('Failed to request quote');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  // Archetype Detection Methods
  const detectArchetype = useCallback(async (userId: string) => {
    try {
      setLoading(true);

      // In a real app, this would fetch actual user behavior data
      const mockBehaviorData = archetypeService.generateMockBehaviorData();
      setBehaviorData(mockBehaviorData);

      const detectedProfile = archetypeService.analyzeArchetype(mockBehaviorData);
      setArchetypeProfile(detectedProfile);

      // Update the main profile with archetype info
      setProfile(prev => prev ? {
        ...prev,
        archetype: detectedProfile.archetype
      } : {
        archetype: detectedProfile.archetype,
        motivation_factors: detectedProfile.motivationTriggers,
        confidence_scores: {
          overall: detectedProfile.confidence,
          competitiveness: detectedProfile.traits.competitiveness,
          analytical: detectedProfile.traits.analyticalThinking,
          social: detectedProfile.traits.socialOrientation
        },
        psychological_state: {
          stress_level: 0.3,
          motivation_level: detectedProfile.confidence,
          confidence_level: detectedProfile.confidence,
          urgency_response: detectedProfile.traits.competitiveness,
          competitive_spirit: detectedProfile.traits.competitiveness,
          financial_motivation: 0.7
        }
      });

      return detectedProfile;
    } catch (err) {
      setError('Failed to detect archetype');
      return null;
    } finally {
      setLoading(false);
    }
  }, [archetypeService]);

  const updateBehaviorData = useCallback((newData: Partial<UserBehaviorData>) => {
    setBehaviorData(prev => prev ? { ...prev, ...newData } : null);

    // Re-analyze archetype if we have behavior data
    if (behaviorData) {
      const updatedData = { ...behaviorData, ...newData };
      const updatedProfile = archetypeService.analyzeArchetype(updatedData);
      setArchetypeProfile(updatedProfile);
    }
  }, [behaviorData, archetypeService]);

  const getPersonalizedRecommendations = useCallback((): string[] => {
    if (!archetypeProfile) return [];

    const recommendations: string[] = [];

    switch (archetypeProfile.archetype) {
      case 'achiever':
        recommendations.push(
          'Focus on high-prestige projects to boost your ranking',
          'Check competitor activity to maintain your edge',
          'Aim for projects that unlock achievement badges'
        );
        break;
      case 'hunter':
        recommendations.push(
          'Strike fast on urgent opportunities',
          'Look for tenders with weak competition',
          'Trust your instincts on quick decisions'
        );
        break;
      case 'analyst':
        recommendations.push(
          'Download all available documentation',
          'Analyze market trends before bidding',
          'Take time to optimize your bid strategy'
        );
        break;
      case 'relationship_builder':
        recommendations.push(
          'Explore partnership opportunities',
          'Build relationships with potential collaborators',
          'Focus on long-term client relationships'
        );
        break;
    }

    return recommendations;
  }, [archetypeProfile]);

  // Initialize archetype detection on mount
  useEffect(() => {
    // Auto-detect archetype for current user
    detectArchetype('current-user');
  }, [detectArchetype]);

  return {
    // State
    loading,
    error,
    profile,
    targets,
    achievements,
    nudges,
    onboardingState,

    // Archetype Detection State
    archetypeProfile,
    behaviorData,

    // Actions
    loadSalesRepProfile,
    loadTargets,
    createTarget,
    loadAchievements,
    checkAchievements,
    generateNudges,
    instantOnboard,
    getOnboardingStatus,
    initiateCompanyOnboarding,
    findSuppliers,
    requestQuote,

    // Archetype Detection Actions
    detectArchetype,
    updateBehaviorData,
    getPersonalizedRecommendations,

    // Utilities
    clearError: () => setError(null),
    refreshData: () => {
      // Auto-refresh logic would go here
      console.log('Refreshing psychological systems data...');
    }
  };
};

export default usePsychologicalSystems;
