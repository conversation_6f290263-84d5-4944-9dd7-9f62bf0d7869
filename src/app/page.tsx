import React from 'react';
import { <PERSON>, Typography, But<PERSON>, Con<PERSON>er, <PERSON><PERSON>, Card, CardContent } from '@mui/material';
import { Dashboard, Business, WhatsApp, Analytics } from '@mui/icons-material';

export default function HomePage() {
  return (
    <Container maxWidth="lg" sx={{ py: 8 }}>
      <Box sx={{ textAlign: 'center', mb: 6 }}>
        <Typography variant="h2" component="h1" gutterBottom fontWeight="bold">
          Welcome to BidBeez
        </Typography>
        <Typography variant="h5" color="text.secondary" sx={{ mb: 4 }}>
          South Africa's Premier Tendering Intelligence Platform
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4, maxWidth: 600, mx: 'auto' }}>
          Revolutionizing how businesses discover, analyze, and win government tenders with AI-powered insights, 
          WhatsApp automation, and psychological bidding intelligence.
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
          <Button 
            variant="contained" 
            size="large" 
            href="/dashboard"
            startIcon={<Dashboard />}
            sx={{ px: 4, py: 1.5 }}
          >
            Go to Dashboard
          </Button>
          <Button 
            variant="outlined" 
            size="large" 
            href="/auth/login"
            sx={{ px: 4, py: 1.5 }}
          >
            Login
          </Button>
        </Box>
      </Box>

      <Grid container spacing={4} sx={{ mt: 4 }}>
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%', textAlign: 'center', p: 2 }}>
            <CardContent>
              <WhatsApp sx={{ fontSize: 48, color: 'success.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                WhatsApp Auto-Bidding
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Receive tender notifications and submit bids directly through WhatsApp with our intelligent automation system.
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%', textAlign: 'center', p: 2 }}>
            <CardContent>
              <Analytics sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                AI-Powered Analytics
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Advanced analytics and psychological insights to optimize your bidding strategy and increase win rates.
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%', textAlign: 'center', p: 2 }}>
            <CardContent>
              <Business sx={{ fontSize: 48, color: 'warning.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Compliance Tools
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Built-in South African compliance tools including B-BBEE verification and tender protest management.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Box sx={{ mt: 8, textAlign: 'center', p: 4, bgcolor: 'grey.50', borderRadius: 2 }}>
        <Typography variant="h4" gutterBottom>
          Ready to Transform Your Tendering?
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          Join thousands of South African businesses already winning more tenders with BidBeez.
        </Typography>
        <Button 
          variant="contained" 
          size="large" 
          href="/auth/register"
          sx={{ px: 6, py: 2 }}
        >
          Get Started Free
        </Button>
      </Box>
    </Container>
  );
}
