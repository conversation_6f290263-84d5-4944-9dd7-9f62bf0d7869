'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Chip,
  Alert,
  IconButton,
  Tooltip,
  Badge,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Avatar,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  FormControlLabel,
  Switch
} from '@mui/material';
import { LinearProgress } from '@mui/material';
import {
  Search as SearchIcon,
  Assignment as AssignmentIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  Business as BusinessIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  AttachMoney as AttachMoneyIcon,
  Description as DescriptionIcon,
  CalendarToday as CalendarIcon,
  Bookmark as BookmarkIcon,
  Upload as UploadIcon,
  Send as SendIcon,
  Visibility as VisibilityIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  EmojiEvents as EmojiEventsIcon,
  Psychology as PsychologyIcon
} from '@mui/icons-material';
import {
  getDashboardSummary,
  getTenders,
  getBids,
  getCalendarEvents,
  markNotificationAsRead
} from '../../services/mockData';
// Types imported as needed

// Mock dashboard data
const mockDashboardData = {
  activeBids: 3,
  pendingTenders: 12,
  successRate: 68,
  totalEarnings: 245000,
  recentActivity: [
    { id: 1, type: 'bid_submitted', description: 'Bid submitted for Road Maintenance Project', time: '2 hours ago' },
    { id: 2, type: 'tender_found', description: 'New tender matches your criteria', time: '4 hours ago' },
    { id: 3, type: 'compliance_check', description: 'Compliance documents verified', time: '1 day ago' }
  ]
};

// Mock user for demo
const mockUser = {
  id: 'demo-user-123',
  username: 'Demo User',
  subscriptionTier: 'free',
  role: 'user',
  isBetaTester: true
};

export default function Dashboard() {
  // Feature toggles for demo
  const [complianceEnabled, setComplianceEnabled] = useState(true);
  const [gamificationEnabled, setGamificationEnabled] = useState(false);
  const [analyticsEnabled, setAnalyticsEnabled] = useState(false);

  // Core MVP Features - Always Available
  const renderCoreMetrics = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} sm={6} md={3}>
        <Card elevation={2}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <AssignmentIcon color="primary" sx={{ mr: 1 }} />
              <Typography variant="h6">Active Bids</Typography>
            </Box>
            <Typography variant="h3" color="primary">
              {mockDashboardData.activeBids}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Currently tracking
            </Typography>
            <Button
              size="small"
              variant="outlined"
              sx={{ mt: 1 }}
              onClick={() => window.location.href = '/bids'}
            >
              Manage Bids
            </Button>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card elevation={2}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <SearchIcon color="info" sx={{ mr: 1 }} />
              <Typography variant="h6">New Tenders</Typography>
            </Box>
            <Typography variant="h3" color="info.main">
              {mockDashboardData.pendingTenders}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Matching your criteria
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card elevation={2}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <TrendingUpIcon color="success" sx={{ mr: 1 }} />
              <Typography variant="h6">Success Rate</Typography>
            </Box>
            <Typography variant="h3" color="success.main">
              {mockDashboardData.successRate}%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Last 12 months
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card elevation={2}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <BusinessIcon color="warning" sx={{ mr: 1 }} />
              <Typography variant="h6">Total Earnings</Typography>
            </Box>
            <Typography variant="h3" color="warning.main">
              R{mockDashboardData.totalEarnings.toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              This year
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  // Quick Actions - Core + Feature-gated
  const renderQuickActions = () => (
    <Card elevation={2}>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Quick Actions
        </Typography>
        <Grid container spacing={2}>
          {/* Core Actions - Always Available */}
          <Grid item xs={12} sm={6} md={4}>
            <Button
              fullWidth
              variant="contained"
              startIcon={<SearchIcon />}
              onClick={() => alert('Navigate to Tender Search')}
            >
              Find Tenders
            </Button>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<AssignmentIcon />}
              onClick={() => alert('Navigate to Bid Creation')}
            >
              Create Bid
            </Button>
          </Grid>

          {/* Feature-gated Actions */}
          {complianceEnabled && (
            <Grid item xs={12} sm={6} md={4}>
              <Button
                fullWidth
                variant="outlined"
                color="success"
                startIcon={<SecurityIcon />}
                onClick={() => alert('Navigate to Compliance Tools')}
              >
                Compliance Tools
              </Button>
            </Grid>
          )}

          {analyticsEnabled && (
            <Grid item xs={12} sm={6} md={4}>
              <Button
                fullWidth
                variant="outlined"
                color="info"
                startIcon={<TrendingUpIcon />}
                onClick={() => alert('Navigate to Analytics')}
              >
                Analytics
              </Button>
            </Grid>
          )}
        </Grid>
      </CardContent>
    </Card>
  );

  // Feature Status Indicators (for demo purposes)
  const renderFeatureStatus = () => (
    <Card elevation={2} sx={{ mb: 3 }}>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2 }}>
          🚀 BidBeez MVP Dashboard - Feature Toggle Demo
        </Typography>
        
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            MVP Core Features: Ready for Launch
          </Typography>
          <LinearProgress 
            variant="determinate" 
            value={100} 
            sx={{ mt: 1 }}
            color="success"
          />
        </Box>

        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item>
            <FormControlLabel
              control={
                <Switch
                  checked={complianceEnabled}
                  onChange={(e) => setComplianceEnabled(e.target.checked)}
                  color="success"
                />
              }
              label="SA Compliance Tools"
            />
          </Grid>
          <Grid item>
            <FormControlLabel
              control={
                <Switch
                  checked={gamificationEnabled}
                  onChange={(e) => setGamificationEnabled(e.target.checked)}
                  color="warning"
                />
              }
              label="Gamification"
            />
          </Grid>
          <Grid item>
            <FormControlLabel
              control={
                <Switch
                  checked={analyticsEnabled}
                  onChange={(e) => setAnalyticsEnabled(e.target.checked)}
                  color="info"
                />
              }
              label="Advanced Analytics"
            />
          </Grid>
        </Grid>

        <Grid container spacing={1}>
          <Grid item>
            <Chip 
              label="Core Features" 
              color="success" 
              variant="filled" 
              size="small" 
            />
          </Grid>
          <Grid item>
            <Chip 
              label="Compliance" 
              color={complianceEnabled ? 'success' : 'default'} 
              variant={complianceEnabled ? 'filled' : 'outlined'}
              size="small" 
            />
          </Grid>
          <Grid item>
            <Chip 
              label="Gamification" 
              color={gamificationEnabled ? 'success' : 'default'} 
              variant={gamificationEnabled ? 'filled' : 'outlined'}
              size="small" 
            />
          </Grid>
          <Grid item>
            <Chip 
              label="Analytics" 
              color={analyticsEnabled ? 'success' : 'default'} 
              variant={analyticsEnabled ? 'filled' : 'outlined'}
              size="small" 
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Current User: {mockUser.username} ({mockUser.subscriptionTier} tier, Beta Tester: {mockUser.isBetaTester ? 'Yes' : 'No'})
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );

  // Compliance Section (feature-gated)
  const renderComplianceSection = () => (
    <Card elevation={2}>
      <CardContent>
        {complianceEnabled ? (
          <>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <SecurityIcon color="success" sx={{ mr: 1 }} />
              <Typography variant="h6">SA Compliance</Typography>
              <Chip label="ENABLED" color="success" size="small" sx={{ ml: 1 }} />
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Professional legal compliance tools available
            </Typography>
            <Button 
              variant="contained" 
              color="success"
              onClick={() => alert('Navigate to Compliance Tools')}
            >
              Access Tools
            </Button>
          </>
        ) : (
          <>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <SecurityIcon color="disabled" sx={{ mr: 1 }} />
              <Typography variant="h6" color="text.secondary">SA Compliance</Typography>
              <Chip label="DISABLED" color="default" size="small" sx={{ ml: 1 }} />
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Toggle the switch above to enable compliance tools
            </Typography>
            <Button 
              variant="outlined" 
              disabled
              size="small"
            >
              Feature Disabled
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  );

  // Gamification Section (feature-gated)
  const renderGamificationSection = () => (
    <Card elevation={2}>
      <CardContent>
        {gamificationEnabled ? (
          <>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <EmojiEventsIcon color="warning" sx={{ mr: 1 }} />
              <Typography variant="h6">Achievements</Typography>
              <Chip label="ENABLED" color="success" size="small" sx={{ ml: 1 }} />
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Track your progress and unlock rewards
            </Typography>
            <Button 
              variant="outlined" 
              onClick={() => alert('Navigate to Achievements')}
            >
              View Achievements
            </Button>
          </>
        ) : (
          <>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <EmojiEventsIcon color="disabled" sx={{ mr: 1 }} />
              <Typography variant="h6" color="text.secondary">Achievements</Typography>
              <Chip label="DISABLED" color="default" size="small" sx={{ ml: 1 }} />
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Toggle the switch above to enable gamification
            </Typography>
            <Button 
              variant="outlined" 
              disabled
              size="small"
            >
              Feature Disabled
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ p: 3, minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          <Typography variant="h4" component="h1" sx={{ fontWeight: 500 }}>
            Welcome back, {mockUser.username}! 👋
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Your BidBeez MVP Dashboard - Progressive Feature Demo
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Notifications">
            <IconButton>
              <Badge badgeContent={3} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Tooltip>

          <Tooltip title="Settings">
            <IconButton>
              <SettingsIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Beta Tester Alert */}
      {mockUser.isBetaTester && (
        <Alert 
          severity="info" 
          sx={{ mb: 3 }}
          icon={<PsychologyIcon />}
        >
          🧪 You're a beta tester! Use the toggles below to enable/disable features and see how the platform adapts.
        </Alert>
      )}

      {/* Feature Status (Demo Mode) */}
      {renderFeatureStatus()}

      {/* Core Metrics */}
      <Box sx={{ mb: 3 }}>
        {renderCoreMetrics()}
      </Box>

      {/* Main Content Grid */}
      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12} md={8}>
          {renderQuickActions()}
        </Grid>

        {/* Side Panel */}
        <Grid item xs={12} md={4}>
          <Grid container spacing={3}>
            {/* Compliance Section */}
            <Grid item xs={12}>
              {renderComplianceSection()}
            </Grid>

            {/* Gamification Section */}
            <Grid item xs={12}>
              {renderGamificationSection()}
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
}
