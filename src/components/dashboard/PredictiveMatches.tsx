import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Button,
  Grid,
  LinearProgress,
  Chip,
  Stack,
  IconButton,
  Tooltip,
  Avatar,
  Divider,
  Alert
} from '@mui/material';
import {
  TrendingUp,
  FlashOn,
  Assessment,
  Timer,
  MonetizationOn,
  Speed,
  Star,
  Refresh,
  Visibility,
  Add,
  Psychology
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

interface PredictiveMatch {
  id: string;
  type: 'tender' | 'rfq_opportunity';
  title: string;
  description: string;
  estimatedValue: number;
  winProbability: number;
  deadline: string;
  category: string;
  recommendation: 'bid' | 'create_rfq' | 'skip' | 'watch';
  reasoningFactors: string[];
  competitorCount?: number;
  supplierCount?: number;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  aiConfidence: number;
  potentialEarnings: number;
  timeToComplete: string;
}

const PredictiveMatches: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [matches, setMatches] = useState<PredictiveMatch[]>([
    {
      id: 'tender-001',
      type: 'tender',
      title: 'Upcoming Tender X',
      description: 'Municipal Infrastructure Development Project',
      estimatedValue: 15000000,
      winProbability: 75,
      deadline: '2024-02-15',
      category: 'Construction',
      recommendation: 'bid',
      reasoningFactors: [
        'Matches your specialization',
        'Low competitor density',
        'Previous success in similar projects',
        'Optimal bid timing window'
      ],
      competitorCount: 8,
      urgency: 'medium',
      aiConfidence: 89,
      potentialEarnings: 2250000,
      timeToComplete: '3-4 weeks'
    },
    {
      id: 'rfq-001',
      type: 'rfq_opportunity',
      title: 'RFQ with High Potential',
      description: 'Office Equipment Supply - Bulk Order',
      estimatedValue: 450000,
      winProbability: 92,
      deadline: '2024-01-25',
      category: 'Office Supplies',
      recommendation: 'create_rfq',
      reasoningFactors: [
        '92% success rate in this category',
        'High supplier interest detected',
        'Optimal market conditions',
        'Quick turnaround opportunity'
      ],
      supplierCount: 15,
      urgency: 'high',
      aiConfidence: 94,
      potentialEarnings: 67500,
      timeToComplete: '2-3 days'
    },
    {
      id: 'tender-002',
      type: 'tender',
      title: 'Strategic Opportunity',
      description: 'IT Infrastructure Upgrade Project',
      estimatedValue: 8500000,
      winProbability: 68,
      deadline: '2024-03-01',
      category: 'Technology',
      recommendation: 'watch',
      reasoningFactors: [
        'Moderate competition expected',
        'Requires partnership strategy',
        'High technical requirements',
        'Good long-term client potential'
      ],
      competitorCount: 12,
      urgency: 'low',
      aiConfidence: 76,
      potentialEarnings: 1275000,
      timeToComplete: '4-6 weeks'
    }
  ]);

  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate AI analysis refresh
    setTimeout(() => {
      setMatches(prev => prev.map(match => ({
        ...match,
        winProbability: Math.min(match.winProbability + Math.random() * 5 - 2.5, 100),
        aiConfidence: Math.min(match.aiConfidence + Math.random() * 3 - 1.5, 100)
      })));
      setRefreshing(false);
    }, 2000);
  };

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'bid': return '#4caf50';
      case 'create_rfq': return '#2196f3';
      case 'watch': return '#ff9800';
      case 'skip': return '#f44336';
      default: return '#757575';
    }
  };

  const getRecommendationIcon = (recommendation: string) => {
    switch (recommendation) {
      case 'bid': return <FlashOn />;
      case 'create_rfq': return <Add />;
      case 'watch': return <Visibility />;
      case 'skip': return <Timer />;
      default: return <Assessment />;
    }
  };

  const getRecommendationText = (recommendation: string) => {
    switch (recommendation) {
      case 'bid': return 'BID NOW';
      case 'create_rfq': return 'CREATE RFQ';
      case 'watch': return 'WATCH & WAIT';
      case 'skip': return 'SKIP THIS ONE';
      default: return 'ANALYZE';
    }
  };

  const handleAction = (match: PredictiveMatch) => {
    switch (match.recommendation) {
      case 'bid':
        navigate(`/bids/create?tender=${match.id}`);
        break;
      case 'create_rfq':
        navigate('/rfq/create', { state: { suggestedCategory: match.category } });
        break;
      case 'watch':
        navigate(`/tenders/${match.id}`);
        break;
      default:
        console.log('Action for:', match.recommendation);
    }
  };

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `R${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `R${(value / 1000).toFixed(0)}k`;
    return `R${value}`;
  };

  return (
    <Card>
      <CardHeader 
        title="Predictive Matches"
        action={
          <Tooltip title="Refresh AI Analysis">
            <IconButton onClick={handleRefresh} disabled={refreshing}>
              <Refresh sx={{ animation: refreshing ? 'spin 1s linear infinite' : 'none' }} />
            </IconButton>
          </Tooltip>
        }
        sx={{
          '& .MuiCardHeader-title': {
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }
        }}
      />
      
      <CardContent>
        <Grid container spacing={3}>
          {matches.map((match) => (
            <Grid item xs={12} md={6} key={match.id}>
              <Card 
                variant="outlined"
                sx={{ 
                  height: '100%',
                  border: `2px solid ${getRecommendationColor(match.recommendation)}40`,
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: 4
                  },
                  transition: 'all 0.3s ease'
                }}
              >
                <CardContent sx={{ p: 3 }}>
                  {/* Header */}
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 1 }}>
                        {match.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {match.description}
                      </Typography>
                    </Box>
                    <Avatar 
                      sx={{ 
                        backgroundColor: getRecommendationColor(match.recommendation),
                        width: 32,
                        height: 32
                      }}
                    >
                      {match.type === 'tender' ? '📋' : '🚀'}
                    </Avatar>
                  </Box>

                  {/* Win Probability */}
                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" fontWeight="bold">
                        Win Probability
                      </Typography>
                      <Typography variant="body2" fontWeight="bold" color={getRecommendationColor(match.recommendation)}>
                        {match.winProbability}%
                      </Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={match.winProbability}
                      sx={{ 
                        height: 8, 
                        borderRadius: 4,
                        backgroundColor: '#e0e0e0',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: getRecommendationColor(match.recommendation),
                          borderRadius: 4
                        }
                      }} 
                    />
                  </Box>

                  {/* Key Metrics */}
                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={6}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="caption" color="text.secondary">
                          Value
                        </Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {formatCurrency(match.estimatedValue)}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="caption" color="text.secondary">
                          Potential Earnings
                        </Typography>
                        <Typography variant="body2" fontWeight="bold" color="success.main">
                          {formatCurrency(match.potentialEarnings)}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>

                  {/* AI Reasoning */}
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" fontWeight="bold" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Psychology sx={{ fontSize: 16 }} />
                      AI Analysis ({match.aiConfidence}% confidence)
                    </Typography>
                    <Stack spacing={0.5}>
                      {match.reasoningFactors.slice(0, 2).map((factor, index) => (
                        <Typography key={index} variant="caption" color="text.secondary">
                          • {factor}
                        </Typography>
                      ))}
                    </Stack>
                  </Box>

                  {/* Competition/Supplier Info */}
                  <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                    {match.competitorCount && (
                      <Chip 
                        label={`${match.competitorCount} competitors`}
                        size="small"
                        color="warning"
                      />
                    )}
                    {match.supplierCount && (
                      <Chip 
                        label={`${match.supplierCount} suppliers`}
                        size="small"
                        color="info"
                      />
                    )}
                    <Chip 
                      label={match.timeToComplete}
                      size="small"
                      color="default"
                    />
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  {/* Recommendation */}
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Recommended Action:</strong>
                    </Typography>
                    <Button
                      variant="contained"
                      fullWidth
                      startIcon={getRecommendationIcon(match.recommendation)}
                      onClick={() => handleAction(match)}
                      sx={{
                        backgroundColor: getRecommendationColor(match.recommendation),
                        fontWeight: 'bold',
                        '&:hover': {
                          backgroundColor: getRecommendationColor(match.recommendation),
                          opacity: 0.8
                        }
                      }}
                    >
                      {getRecommendationText(match.recommendation)}
                    </Button>
                    
                    {match.recommendation === 'create_rfq' && (
                      <Typography variant="caption" color="success.main" sx={{ mt: 1, display: 'block' }}>
                        🚀 92% success rate - CREATE NOW!
                      </Typography>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* AI Insights Summary */}
        <Alert severity="info" sx={{ mt: 3 }}>
          <Typography variant="body2" fontWeight="bold" sx={{ mb: 1 }}>
            🧠 AI Insights Summary
          </Typography>
          <Typography variant="body2">
            Based on your profile and market analysis, you have a <strong>83% average win probability</strong> across these opportunities. 
            The RFQ opportunity shows exceptional potential with minimal competition.
          </Typography>
        </Alert>
      </CardContent>
    </Card>
  );
};

export default PredictiveMatches;
