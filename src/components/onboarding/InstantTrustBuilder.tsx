import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  <PERSON>,
  <PERSON><PERSON>,
  Alert,
  LinearProgress,
  Stack,
  Avatar,
  Divider,
  IconButton,
  Tooltip,
  Fade,
  Slide,
  Zoom
} from '@mui/material';
import {
  Psychology,
  LocationOn,
  TrendingUp,
  FlashOn,
  Warning,
  CheckCircle,
  Refresh,
  GpsFixed as Target,
  Description,
  Timer,
  MonetizationOn,
  EmojiEvents,
  Visibility,
  AutoAwesome,
  Bolt,
  Star
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

interface CompanyProfile {
  name: string;
  registrationNumber: string;
  bbbeeLevel: number;
  turnover: number;
  province: string;
  city: string;
  categories: string[];
  skills: string[];
  certifications: string[];
  yearsInBusiness: number;
}

interface PastBid {
  id: string;
  tenderNumber: string;
  title: string;
  entity: string;
  bidAmount: number;
  submissionDate: string;
  status: 'awarded' | 'not_awarded' | 'pending' | 'disqualified';
  awardAmount?: number;
  awardDate?: string;
  competitors: number;
  ranking?: number;
  source: 'etenders' | 'municipal' | 'provincial' | 'national';
}

interface BiddingStats {
  totalBids: number;
  totalAwarded: number;
  totalValue: number;
  awardedValue: number;
  successRate: number;
  averageBidValue: number;
  largestAward: number;
  recentActivity: string;
  competitiveRanking: string;
}

interface PastBid {
  id: string;
  tenderNumber: string;
  title: string;
  entity: string;
  bidAmount: number;
  submissionDate: string;
  status: 'awarded' | 'not_awarded' | 'pending' | 'disqualified';
  awardAmount?: number;
  awardDate?: string;
  competitors: number;
  ranking?: number;
  source: 'etenders' | 'municipal' | 'provincial' | 'national';
}

interface BiddingStats {
  totalBids: number;
  totalAwarded: number;
  totalValue: number;
  awardedValue: number;
  successRate: number;
  averageBidValue: number;
  largestAward: number;
  recentActivity: string;
  competitiveRanking: string;
}

interface InstantMatch {
  id: string;
  title: string;
  value: number;
  matchScore: number;
  location: string;
  deadline: string;
  requirements: string[];
  whyPerfect: string;
  psychTrigger: string;
  urgency: 'critical' | 'high' | 'medium';
  estimatedWinChance: number;
}

interface TrustSignal {
  icon: React.ReactNode;
  title: string;
  description: string;
  value: string;
  color: string;
}

const InstantTrustBuilder: React.FC = () => {
  const { user } = useAuth();
  
  // Simulated company profile from CSD/onboarding
  const [companyProfile] = useState<CompanyProfile>({
    name: 'Acme Construction (Pty) Ltd',
    registrationNumber: '2019/123456/07',
    bbbeeLevel: 4,
    turnover: 25000000,
    province: 'Gauteng',
    city: 'Johannesburg',
    categories: ['Construction', 'Infrastructure', 'Civil Engineering'],
    skills: ['Project Management', 'Construction Management', 'Civil Engineering'],
    certifications: ['ISO 9001:2015', 'OHSAS 18001'],
    yearsInBusiness: 8
  });

  const [instantMatches, setInstantMatches] = useState<InstantMatch[]>([]);
  const [trustSignals, setTrustSignals] = useState<TrustSignal[]>([]);
  const [pastBids, setPastBids] = useState<PastBid[]>([]);
  const [biddingStats, setBiddingStats] = useState<BiddingStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [showingResults, setShowingResults] = useState(false);

  useEffect(() => {
    // Simulate AI analysis of company profile + government database lookup
    setTimeout(() => {
      generatePastBidHistory();
      generateBiddingStats();
      generateInstantMatches();
      generateTrustSignals();
      setLoading(false);
      setTimeout(() => setShowingResults(true), 500);
    }, 2000);
  }, []);

  const generatePastBidHistory = () => {
    const bids: PastBid[] = [
      {
        id: 'bid-001',
        tenderNumber: 'COJ/2023/INFRA/001',
        title: 'Road Maintenance and Repair - Johannesburg CBD',
        entity: 'City of Johannesburg',
        bidAmount: 8500000,
        submissionDate: '2023-11-15T14:30:00',
        status: 'awarded',
        awardAmount: 8500000,
        awardDate: '2023-12-20T10:00:00',
        competitors: 12,
        ranking: 1,
        source: 'municipal'
      },
      {
        id: 'bid-002',
        tenderNumber: 'GP/2023/CONST/045',
        title: 'School Building Construction - Soweto',
        entity: 'Gauteng Department of Education',
        bidAmount: 15200000,
        submissionDate: '2023-09-22T16:45:00',
        status: 'awarded',
        awardAmount: 15200000,
        awardDate: '2023-10-30T11:30:00',
        competitors: 18,
        ranking: 1,
        source: 'provincial'
      },
      {
        id: 'bid-003',
        tenderNumber: 'SANRAL/2023/HWY/012',
        title: 'Highway Bridge Maintenance - N1 Corridor',
        entity: 'South African National Roads Agency',
        bidAmount: 22400000,
        submissionDate: '2023-08-10T12:00:00',
        status: 'not_awarded',
        competitors: 25,
        ranking: 3,
        source: 'national'
      },
      {
        id: 'bid-004',
        tenderNumber: 'COT/2023/UTIL/008',
        title: 'Water Infrastructure Upgrade - Tshwane',
        entity: 'City of Tshwane',
        bidAmount: 12800000,
        submissionDate: '2023-07-05T15:20:00',
        status: 'awarded',
        awardAmount: 12800000,
        awardDate: '2023-08-15T09:45:00',
        competitors: 14,
        ranking: 1,
        source: 'municipal'
      },
      {
        id: 'bid-005',
        tenderNumber: 'PRASA/2023/RAIL/003',
        title: 'Railway Station Renovation - Johannesburg',
        entity: 'Passenger Rail Agency of South Africa',
        bidAmount: 18600000,
        submissionDate: '2023-06-12T11:15:00',
        status: 'not_awarded',
        competitors: 22,
        ranking: 2,
        source: 'national'
      }
    ];
    setPastBids(bids);
  };

  const generateBiddingStats = () => {
    const stats: BiddingStats = {
      totalBids: 5,
      totalAwarded: 3,
      totalValue: 77500000,
      awardedValue: 36500000,
      successRate: 60,
      averageBidValue: 15500000,
      largestAward: 15200000,
      recentActivity: 'Last bid: 2 months ago',
      competitiveRanking: 'Top 15% in Gauteng Construction'
    };
    setBiddingStats(stats);
  };

  const generateInstantMatches = () => {
    const matches: InstantMatch[] = [
      {
        id: 'tender-001',
        title: 'Municipal Infrastructure Development - R15.6M',
        value: 15600000,
        matchScore: 94,
        location: 'Johannesburg, Gauteng (12km from you)',
        deadline: '2024-02-15T17:00:00',
        requirements: ['Construction Management', 'Civil Engineering', 'B-BBEE Level 4+'],
        whyPerfect: 'Perfect match: Your civil engineering expertise + B-BBEE Level 4 + local location',
        psychTrigger: '🎯 PERFECT MATCH: 94% compatibility - you tick ALL the boxes!',
        urgency: 'critical',
        estimatedWinChance: 87
      },
      {
        id: 'tender-002',
        title: 'Highway Construction Project - R22.4M',
        value: 22400000,
        matchScore: 91,
        location: 'Pretoria, Gauteng (45km from you)',
        deadline: '2024-02-20T12:00:00',
        requirements: ['Infrastructure Experience', 'R25M+ Turnover', 'ISO 9001'],
        whyPerfect: 'Your R25M turnover + ISO 9001 certification + 8 years experience = QUALIFIED',
        psychTrigger: '💰 BIG OPPORTUNITY: Your turnover qualifies you for this R22.4M project!',
        urgency: 'high',
        estimatedWinChance: 82
      },
      {
        id: 'tender-003',
        title: 'Office Building Construction - R8.5M',
        value: 8500000,
        matchScore: 89,
        location: 'Sandton, Gauteng (18km from you)',
        deadline: '2024-02-18T16:00:00',
        requirements: ['Construction Management', 'Project Management', 'Local Presence'],
        whyPerfect: 'Local advantage + your project management skills = COMPETITIVE EDGE',
        psychTrigger: '🏆 LOCAL ADVANTAGE: Johannesburg location gives you the edge!',
        urgency: 'medium',
        estimatedWinChance: 78
      }
    ];
    setInstantMatches(matches);
  };

  const generateTrustSignals = () => {
    const signals: TrustSignal[] = [
      {
        icon: <Target />,
        title: 'Instant Matches Found',
        description: 'AI analyzed your profile and found perfect tender matches',
        value: '3 High-Value Tenders',
        color: '#4caf50'
      },
      {
        icon: <MonetizationOn />,
        title: 'Total Opportunity Value',
        description: 'Combined value of tenders you qualify for',
        value: 'R46.5M Available',
        color: '#ff9800'
      },
      {
        icon: <LocationOn />,
        title: 'Geographic Advantage',
        description: 'Tenders within your service area',
        value: 'All Within 50km',
        color: '#2196f3'
      },
      {
        icon: <CheckCircle />,
        title: 'Qualification Rate',
        description: 'Percentage of requirements you already meet',
        value: '91% Qualified',
        color: '#4caf50'
      },
      {
        icon: <EmojiEvents />,
        title: 'Competitive Advantage',
        description: 'Your unique strengths vs competitors',
        value: 'B-BBEE + Local + Experience',
        color: '#9c27b0'
      },
      {
        icon: <Timer />,
        title: 'Time to First Bid',
        description: 'How quickly you can submit your first bid',
        value: 'Ready in 24 Hours',
        color: '#f44336'
      }
    ];
    setTrustSignals(signals);
  };

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `R${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `R${(value / 1000).toFixed(0)}k`;
    return `R${value}`;
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return '#f44336';
      case 'high': return '#ff9800';
      case 'medium': return '#2196f3';
      default: return '#757575';
    }
  };

  const getDaysUntilDeadline = (deadline: string) => {
    const days = Math.ceil((new Date(deadline).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
    return days;
  };

  if (loading) {
    return (
      <Box sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h4" fontWeight="bold" sx={{ mb: 2 }}>
          🧠 Analyzing Your Company Profile...
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          Our AI is matching your capabilities with active tenders AND retrieving your bidding history from government databases
        </Typography>
        <LinearProgress sx={{ mb: 2 }} />
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          ✅ Processing: {companyProfile.name} • {companyProfile.categories.join(', ')} • {companyProfile.province}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          🔍 Scanning: eTenders, Municipal, Provincial & National databases
        </Typography>
        <Typography variant="body2" color="text.secondary">
          📊 Analyzing: Past bids, awards, success rates & competitive ranking
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 4 }}>
      {/* AHHHHAAAA Header */}
      <Fade in={showingResults} timeout={1000}>
        <Card sx={{ mb: 4, background: 'linear-gradient(135deg, #4caf50, #8bc34a)', color: 'white' }}>
          <CardContent sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="h3" fontWeight="bold" sx={{ mb: 2 }}>
              🎉 AHHHHAAAA!!!! MOMENT
            </Typography>
            <Typography variant="h5" sx={{ mb: 2 }}>
              Welcome {companyProfile.name}!
            </Typography>
            <Typography variant="h6" sx={{ mb: 2 }}>
              🧠 Our AI found <strong>3 PERFECT TENDER MATCHES</strong> worth <strong>R46.5M</strong> that you qualify for RIGHT NOW!
            </Typography>
            <Typography variant="h6" sx={{ mb: 3 }}>
              📊 PLUS: We found your <strong>{biddingStats?.totalBids} PAST BIDS</strong> with <strong>{biddingStats?.totalAwarded} AWARDS</strong> worth <strong>R{biddingStats ? (biddingStats.awardedValue / 1000000).toFixed(1) : '0'}M</strong>!
            </Typography>
            <Chip 
              label="🚀 INSTANT INTELLIGENCE ACTIVATED" 
              sx={{ 
                bgcolor: 'rgba(255,255,255,0.2)', 
                color: 'white', 
                fontWeight: 'bold',
                fontSize: '1.1rem',
                px: 2,
                py: 1,
                height: 40
              }} 
            />
          </CardContent>
        </Card>
      </Fade>

      {/* Trust Signals Grid */}
      <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
        🎯 Why BidBeez is PERFECT for You:
      </Typography>
      
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {trustSignals.map((signal, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Zoom in={showingResults} timeout={1000 + (index * 200)}>
              <Card sx={{ height: '100%', border: `2px solid ${signal.color}20`, bgcolor: `${signal.color}05` }}>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Avatar sx={{ bgcolor: signal.color, mx: 'auto', mb: 2, width: 56, height: 56 }}>
                    {signal.icon}
                  </Avatar>
                  <Typography variant="h6" fontWeight="bold" sx={{ mb: 1 }}>
                    {signal.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {signal.description}
                  </Typography>
                  <Typography variant="h6" fontWeight="bold" sx={{ color: signal.color }}>
                    {signal.value}
                  </Typography>
                </CardContent>
              </Card>
            </Zoom>
          </Grid>
        ))}
      </Grid>

      {/* Bidding History & Track Record */}
      <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
        📊 Your Government Bidding Track Record:
      </Typography>

      {biddingStats && (
        <Card sx={{ mb: 4, border: '2px solid', borderColor: 'success.light', bgcolor: 'success.lighter' }}>
          <CardContent>
            <Grid container spacing={3} sx={{ mb: 3 }}>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" fontWeight="bold" color="primary.main">
                    {biddingStats.totalBids}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Bids Submitted
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    {biddingStats.totalAwarded}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Tenders Awarded
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" fontWeight="bold" color="warning.main">
                    {biddingStats.successRate}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Success Rate
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    R{(biddingStats.awardedValue / 1000000).toFixed(1)}M
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Awards Value
                  </Typography>
                </Box>
              </Grid>
            </Grid>

            <Alert severity="success" sx={{ mb: 3 }}>
              <Typography variant="body1" fontWeight="bold">
                🏆 IMPRESSIVE TRACK RECORD: {biddingStats.successRate}% success rate puts you in the {biddingStats.competitiveRanking}!
              </Typography>
              <Typography variant="body2">
                Your largest award: R{(biddingStats.largestAward / 1000000).toFixed(1)}M • {biddingStats.recentActivity}
              </Typography>
            </Alert>

            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
              Recent Bidding Activity:
            </Typography>

            <Grid container spacing={2}>
              {pastBids.slice(0, 3).map((bid, index) => (
                <Grid item xs={12} md={4} key={bid.id}>
                  <Slide in={showingResults} direction="up" timeout={2000 + (index * 200)}>
                    <Card
                      variant="outlined"
                      sx={{
                        border: bid.status === 'awarded' ? '2px solid #4caf50' : '2px solid #ff9800',
                        bgcolor: bid.status === 'awarded' ? '#4caf5010' : '#ff980010'
                      }}
                    >
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                          <Typography variant="body1" fontWeight="bold" sx={{ flex: 1 }}>
                            {bid.title}
                          </Typography>
                          <Chip
                            label={bid.status === 'awarded' ? '🏆 WON' : bid.status === 'not_awarded' ? '❌ LOST' : '⏳ PENDING'}
                            color={bid.status === 'awarded' ? 'success' : bid.status === 'not_awarded' ? 'error' : 'warning'}
                            size="small"
                            sx={{ fontWeight: 'bold' }}
                          />
                        </Box>

                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          {bid.entity}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          Tender: {bid.tenderNumber}
                        </Typography>

                        <Grid container spacing={1} sx={{ mb: 2 }}>
                          <Grid item xs={6}>
                            <Typography variant="caption" color="text.secondary">
                              Bid Amount
                            </Typography>
                            <Typography variant="body2" fontWeight="bold">
                              {formatCurrency(bid.bidAmount)}
                            </Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="caption" color="text.secondary">
                              Competitors
                            </Typography>
                            <Typography variant="body2" fontWeight="bold">
                              {bid.competitors} bidders
                            </Typography>
                          </Grid>
                        </Grid>

                        {bid.status === 'awarded' && (
                          <Alert severity="success">
                            <Typography variant="body2" fontWeight="bold">
                              🎉 AWARDED: {formatCurrency(bid.awardAmount!)} on {new Date(bid.awardDate!).toLocaleDateString()}
                            </Typography>
                          </Alert>
                        )}

                        {bid.status === 'not_awarded' && bid.ranking && (
                          <Alert severity="warning">
                            <Typography variant="body2">
                              Ranked #{bid.ranking} out of {bid.competitors} bidders
                            </Typography>
                          </Alert>
                        )}
                      </CardContent>
                    </Card>
                  </Slide>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Instant Tender Matches */}
      <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
        🎯 Your INSTANT Tender Matches:
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        {instantMatches.map((match, index) => (
          <Grid item xs={12} md={6} lg={4} key={match.id}>
            <Slide in={showingResults} direction="up" timeout={1500 + (index * 300)}>
              <Card 
                sx={{ 
                  height: '100%',
                  border: `3px solid ${getUrgencyColor(match.urgency)}`,
                  bgcolor: `${getUrgencyColor(match.urgency)}10`,
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4
                  },
                  transition: 'all 0.3s ease'
                }}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 1 }}>
                        {match.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        📍 {match.location}
                      </Typography>
                    </Box>
                    <Chip 
                      label={`${match.matchScore}% MATCH`}
                      color="success"
                      size="small"
                      sx={{ fontWeight: 'bold' }}
                    />
                  </Box>

                  <Typography variant="body2" color="primary.main" fontWeight="bold" sx={{ mb: 2 }}>
                    {match.psychTrigger}
                  </Typography>

                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={6}>
                      <Typography variant="caption" color="text.secondary">
                        Tender Value
                      </Typography>
                      <Typography variant="h6" fontWeight="bold" color="success.main">
                        {formatCurrency(match.value)}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="caption" color="text.secondary">
                        Win Chance
                      </Typography>
                      <Typography variant="h6" fontWeight="bold" color="warning.main">
                        {match.estimatedWinChance}%
                      </Typography>
                    </Grid>
                  </Grid>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    <strong>Why it's perfect:</strong>
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    {match.whyPerfect}
                  </Typography>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    <strong>Requirements:</strong>
                  </Typography>
                  <Stack direction="row" spacing={1} flexWrap="wrap" gap={1} sx={{ mb: 3 }}>
                    {match.requirements.map((req, reqIndex) => (
                      <Chip 
                        key={reqIndex} 
                        label={req} 
                        size="small" 
                        color="primary" 
                        variant="outlined"
                      />
                    ))}
                  </Stack>

                  <Alert severity="info" sx={{ mb: 2 }}>
                    <Typography variant="body2" fontWeight="bold">
                      ⏰ Deadline: {getDaysUntilDeadline(match.deadline)} days left
                    </Typography>
                  </Alert>

                  <Button
                    variant="contained"
                    fullWidth
                    startIcon={<Bolt />}
                    sx={{
                      background: `linear-gradient(45deg, ${getUrgencyColor(match.urgency)}, ${getUrgencyColor(match.urgency)}80)`,
                      fontWeight: 'bold',
                      fontSize: '1.1rem'
                    }}
                  >
                    🚀 START BIDDING NOW!
                  </Button>
                </CardContent>
              </Card>
            </Slide>
          </Grid>
        ))}
      </Grid>

      {/* Final Trust Builder */}
      <Fade in={showingResults} timeout={3000}>
        <Alert 
          severity="success" 
          sx={{ 
            fontSize: '1.1rem',
            fontWeight: 'bold',
            border: '2px solid',
            borderColor: 'success.main'
          }}
        >
          <Typography variant="h6" fontWeight="bold" sx={{ mb: 1 }}>
            🎯 INSTANT TRUST ACHIEVED: BidBeez ALREADY KNOWS Your Complete Business History!
          </Typography>
          <Typography variant="body1">
            In just 30 seconds, our AI analyzed your company profile, retrieved your complete government bidding history
            ({biddingStats?.totalBids} bids, {biddingStats?.successRate}% success rate, R{biddingStats ? (biddingStats.awardedValue / 1000000).toFixed(1) : '0'}M in awards),
            and found R46.5M in NEW tender opportunities that perfectly match your proven capabilities.
            <strong> We know your track record - now let's build on it!</strong>
          </Typography>
        </Alert>
      </Fade>
    </Box>
  );
};

export default InstantTrustBuilder;
