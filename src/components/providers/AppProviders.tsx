'use client';

import React from 'react';
import { Provider } from 'react-redux';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { store } from '../../store/store';
import { AuthProvider } from '../../contexts/AuthContext';
import AdaptiveInterface from '../adaptive/AdaptiveInterface';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';

// Import ProductionInitializer
import ProductionInitializer from '../production/ProductionInitializer';

// Create base theme
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#034B56',
    },
    secondary: {
      main: '#dc004e',
    },
    background: {
      default: '#f8fafc',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        },
      },
    },
  },
});

// Main App Layout Component (now part of providers)
const AppLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { startTracking } = useNeuroMarketing();

  React.useEffect(() => {
    // Start NeuroMarketing tracking when app loads
    startTracking();
  }, [startTracking]);

  return (
    <AdaptiveInterface
      enablePsychologicalOptimization={true}
      enableAdaptiveAnimations={true}
      enableStressReduction={true}
      enableCognitiveLoadManagement={true}
    >
      {children}
    </AdaptiveInterface>
  );
};

interface AppProvidersProps {
  children: React.ReactNode;
}

const AppProviders: React.FC<AppProvidersProps> = ({ children }) => {
  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <AuthProvider>
          <ProductionInitializer>
            <AppLayout>
              {children}
            </AppLayout>
          </ProductionInitializer>
        </AuthProvider>
      </ThemeProvider>
    </Provider>
  );
};

export default AppProviders;
