import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  <PERSON><PERSON>graphy,
  Button,
  TextField,
  Grid,
  <PERSON><PERSON>,
  Step,
  StepLabel,
  StepContent,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  LinearProgress,
  Alert,
  IconButton,
  Tooltip,
  Divider
} from '@mui/material';
import {
  FlashOn,
  Speed,
  Timer,
  Category,
  Description,
  People,
  Send,
  ArrowBack,
  ArrowForward,
  CheckCircle
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

interface RFQData {
  category: string;
  title: string;
  description: string;
  estimatedValue: number;
  deadline: string;
  requirements: string[];
  targetSuppliers: number;
  urgency: 'low' | 'medium' | 'high';
}

const categories = [
  'Construction Materials',
  'IT Equipment',
  'Professional Services',
  'Security Services',
  'Cleaning Supplies',
  'Catering Services',
  'Transport Services',
  'Medical Supplies',
  'Office Supplies',
  'Engineering Services'
];

const quickRequirements = [
  'B-BBEE Level 4 or better',
  'Valid Tax Clearance',
  'Professional Indemnity Insurance',
  'Previous Experience Required',
  'Local Supplier Preferred',
  'ISO Certification',
  'SABS Approved Products',
  'Same Day Delivery',
  'Bulk Discount Available',
  'Extended Warranty'
];

const RFQCreationWizard: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [activeStep, setActiveStep] = useState(0);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [loading, setLoading] = useState(false);
  const [rfqData, setRFQData] = useState<RFQData>({
    category: '',
    title: '',
    description: '',
    estimatedValue: 0,
    deadline: '',
    requirements: [],
    targetSuppliers: 5,
    urgency: 'medium'
  });

  // Timer for 90-second challenge
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeElapsed(prev => prev + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getTimeColor = () => {
    if (timeElapsed < 60) return '#4caf50'; // Green
    if (timeElapsed < 90) return '#ff9800'; // Orange
    return '#f44336'; // Red
  };

  const handleNext = () => {
    setActiveStep(prev => prev + 1);
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/rfq/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...rfqData,
          bidderId: user?.id,
          createdAt: new Date().toISOString(),
          timeToCreate: timeElapsed
        })
      });

      if (response.ok) {
        const result = await response.json();
        navigate(`/rfq/${result.id}`, { 
          state: { 
            success: true, 
            timeElapsed,
            message: `RFQ created in ${formatTime(timeElapsed)}! ${timeElapsed <= 90 ? '🏆 Speed Demon Achievement Unlocked!' : ''}` 
          }
        });
      }
    } catch (error) {
      console.error('Failed to create RFQ:', error);
    } finally {
      setLoading(false);
    }
  };

  const canProceed = () => {
    switch (activeStep) {
      case 0: return rfqData.category !== '';
      case 1: return rfqData.title !== '' && rfqData.description !== '';
      case 2: return rfqData.estimatedValue > 0 && rfqData.deadline !== '';
      default: return true;
    }
  };

  const steps = [
    'Category (30s)',
    'Details (45s)', 
    'Finalize (15s)'
  ];

  return (
    <Box sx={{ 
      minHeight: '100vh', 
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      py: 4,
      px: 2
    }}>
      <Box sx={{ maxWidth: 800, mx: 'auto' }}>
        {/* Speed Challenge Header */}
        <Card sx={{ mb: 3, background: 'rgba(255,255,255,0.95)' }}>
          <CardContent sx={{ textAlign: 'center', py: 3 }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 2, color: '#1976d2' }}>
              ⚡ 90-Second RFQ Challenge!
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
              Create your RFQ in under 90 seconds for instant supplier responses
            </Typography>
            
            {/* Timer */}
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 2, mb: 2 }}>
              <Timer sx={{ color: getTimeColor() }} />
              <Typography 
                variant="h5" 
                fontWeight="bold" 
                sx={{ color: getTimeColor() }}
              >
                {formatTime(timeElapsed)}
              </Typography>
              <Chip 
                label={timeElapsed <= 90 ? 'ON TRACK' : 'OVERTIME'}
                color={timeElapsed <= 90 ? 'success' : 'error'}
                size="small"
              />
            </Box>
            
            <LinearProgress 
              variant="determinate" 
              value={(activeStep / (steps.length - 1)) * 100}
              sx={{ 
                height: 8, 
                borderRadius: 4,
                backgroundColor: '#e0e0e0',
                '& .MuiLinearProgress-bar': {
                  background: 'linear-gradient(90deg, #4CAF50, #2196F3)'
                }
              }}
            />
          </CardContent>
        </Card>

        {/* Wizard Content */}
        <Card sx={{ background: 'rgba(255,255,255,0.95)' }}>
          <CardContent sx={{ p: 4 }}>
            <Stepper activeStep={activeStep} orientation="vertical">
              
              {/* Step 1: Category Selection (30 seconds) */}
              <Step>
                <StepLabel>
                  <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Category color="primary" />
                    Quick Category Selection
                  </Typography>
                </StepLabel>
                <StepContent>
                  <Typography variant="body1" sx={{ mb: 3 }}>
                    What do you need? Pick the closest category:
                  </Typography>
                  
                  <Grid container spacing={1}>
                    {categories.map((category) => (
                      <Grid item xs={6} sm={4} key={category}>
                        <Button
                          fullWidth
                          variant={rfqData.category === category ? 'contained' : 'outlined'}
                          onClick={() => setRFQData(prev => ({ ...prev, category }))}
                          sx={{ 
                            py: 1.5,
                            fontSize: '0.9rem',
                            textTransform: 'none'
                          }}
                        >
                          {category}
                        </Button>
                      </Grid>
                    ))}
                  </Grid>
                  
                  <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                    <Button 
                      variant="contained" 
                      onClick={handleNext}
                      disabled={!canProceed()}
                      startIcon={<ArrowForward />}
                    >
                      Continue (30s target)
                    </Button>
                  </Box>
                </StepContent>
              </Step>

              {/* Step 2: Basic Details (45 seconds) */}
              <Step>
                <StepLabel>
                  <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Description color="primary" />
                    Quick Details
                  </Typography>
                </StepLabel>
                <StepContent>
                  <Typography variant="body1" sx={{ mb: 3 }}>
                    Fill in the essentials - keep it simple:
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="RFQ Title"
                        placeholder="e.g., Office Cleaning Services - Monthly Contract"
                        value={rfqData.title}
                        onChange={(e) => setRFQData(prev => ({ ...prev, title: e.target.value }))}
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        multiline
                        rows={3}
                        label="Brief Description"
                        placeholder="Quick description of what you need..."
                        value={rfqData.description}
                        onChange={(e) => setRFQData(prev => ({ ...prev, description: e.target.value }))}
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        type="number"
                        label="Estimated Value (ZAR)"
                        value={rfqData.estimatedValue || ''}
                        onChange={(e) => setRFQData(prev => ({ ...prev, estimatedValue: Number(e.target.value) }))}
                        InputProps={{ startAdornment: 'R' }}
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        type="datetime-local"
                        label="Response Deadline"
                        value={rfqData.deadline}
                        onChange={(e) => setRFQData(prev => ({ ...prev, deadline: e.target.value }))}
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>
                  </Grid>
                  
                  <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                    <Button onClick={handleBack} startIcon={<ArrowBack />}>
                      Back
                    </Button>
                    <Button 
                      variant="contained" 
                      onClick={handleNext}
                      disabled={!canProceed()}
                      startIcon={<ArrowForward />}
                    >
                      Almost Done! (45s target)
                    </Button>
                  </Box>
                </StepContent>
              </Step>

              {/* Step 3: Finalize (15 seconds) */}
              <Step>
                <StepLabel>
                  <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <People color="primary" />
                    Quick Finalize
                  </Typography>
                </StepLabel>
                <StepContent>
                  <Typography variant="body1" sx={{ mb: 3 }}>
                    Add any quick requirements and send:
                  </Typography>
                  
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    Quick Requirements (optional):
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 3 }}>
                    {quickRequirements.slice(0, 6).map((req) => (
                      <Chip
                        key={req}
                        label={req}
                        clickable
                        size="small"
                        color={rfqData.requirements.includes(req) ? 'primary' : 'default'}
                        onClick={() => {
                          setRFQData(prev => ({
                            ...prev,
                            requirements: prev.requirements.includes(req)
                              ? prev.requirements.filter(r => r !== req)
                              : [...prev.requirements, req]
                          }));
                        }}
                      />
                    ))}
                  </Box>
                  
                  <Grid container spacing={2} sx={{ mb: 3 }}>
                    <Grid item xs={6}>
                      <FormControl fullWidth size="small">
                        <InputLabel>Target Suppliers</InputLabel>
                        <Select
                          value={rfqData.targetSuppliers}
                          onChange={(e) => setRFQData(prev => ({ ...prev, targetSuppliers: Number(e.target.value) }))}
                        >
                          <MenuItem value={3}>3 suppliers</MenuItem>
                          <MenuItem value={5}>5 suppliers</MenuItem>
                          <MenuItem value={10}>10 suppliers</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    
                    <Grid item xs={6}>
                      <FormControl fullWidth size="small">
                        <InputLabel>Urgency</InputLabel>
                        <Select
                          value={rfqData.urgency}
                          onChange={(e) => setRFQData(prev => ({ ...prev, urgency: e.target.value as any }))}
                        >
                          <MenuItem value="low">Standard</MenuItem>
                          <MenuItem value="medium">Urgent</MenuItem>
                          <MenuItem value="high">Critical</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>
                  
                  {timeElapsed <= 90 && (
                    <Alert severity="success" sx={{ mb: 2 }}>
                      🏆 Amazing! You're on track for the Speed Demon achievement!
                    </Alert>
                  )}
                  
                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Button onClick={handleBack} startIcon={<ArrowBack />}>
                      Back
                    </Button>
                    <Button 
                      variant="contained" 
                      size="large"
                      onClick={handleSubmit}
                      disabled={loading}
                      startIcon={loading ? undefined : <Send />}
                      sx={{
                        background: 'linear-gradient(45deg, #4CAF50, #8BC34A)',
                        fontSize: '1.1rem',
                        fontWeight: 'bold',
                        flex: 1
                      }}
                    >
                      {loading ? 'Creating...' : '🚀 Send RFQ to Suppliers!'}
                    </Button>
                  </Box>
                </StepContent>
              </Step>
              
            </Stepper>
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
};

export default RFQCreationWizard;
