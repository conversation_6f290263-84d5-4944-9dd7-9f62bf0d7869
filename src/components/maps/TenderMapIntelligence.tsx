import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Grid,
  Chip,
  Stack,
  Button,
  IconButton,
  Tooltip,
  Avatar,
  Alert,
  Divider,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  LocationOn,
  MyLocation,
  Layers,
  FilterList,
  TrendingUp,
  Psychology,
  Build,
  School,
  People,
  Refresh,
  Visibility,
  FlashOn
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

interface MapTender {
  id: string;
  title: string;
  value: number;
  location: {
    province: string;
    city: string;
    coordinates: { lat: number; lng: number };
    address: string;
  };
  category: string;
  deadline: string;
  requirements: {
    skills: string[];
    tools: string[];
    bbbeeLevel: number;
  };
  matchScore: number;
  distance: number;
  urgency: 'critical' | 'high' | 'medium' | 'low';
  psychTrigger: string;
}

interface MapLayer {
  id: string;
  name: string;
  type: 'tenders' | 'skills' | 'tools' | 'contractors' | 'suppliers';
  visible: boolean;
  count: number;
  color: string;
}

interface UserLocation {
  province: string;
  city: string;
  coordinates: { lat: number; lng: number };
  serviceRadius: number;
}

const TenderMapIntelligence: React.FC = () => {
  const { user } = useAuth();
  
  const [userLocation, setUserLocation] = useState<UserLocation>({
    province: 'Gauteng',
    city: 'Johannesburg',
    coordinates: { lat: -26.2041, lng: 28.0473 },
    serviceRadius: 100
  });

  const [mapTenders, setMapTenders] = useState<MapTender[]>([
    {
      id: 'tender-001',
      title: 'Municipal Infrastructure - R15.6M',
      value: 15600000,
      location: {
        province: 'Gauteng',
        city: 'Johannesburg',
        coordinates: { lat: -26.2041, lng: 28.0473 },
        address: 'City Deep, Johannesburg'
      },
      category: 'Infrastructure',
      deadline: '2024-02-15T17:00:00',
      requirements: {
        skills: ['PMP', 'Water Treatment Engineering'],
        tools: ['AutoCAD', 'Microsoft Project'],
        bbbeeLevel: 4
      },
      matchScore: 85,
      distance: 15,
      urgency: 'critical',
      psychTrigger: 'PERFECT MATCH: 85% compatibility in your area!'
    },
    {
      id: 'tender-002',
      title: 'Highway Construction - R22.4M',
      value: 22400000,
      location: {
        province: 'Gauteng',
        city: 'Pretoria',
        coordinates: { lat: -25.7479, lng: 28.2293 },
        address: 'N1 Highway, Pretoria'
      },
      category: 'Construction',
      deadline: '2024-02-20T12:00:00',
      requirements: {
        skills: ['Civil Engineering', 'Construction Management'],
        tools: ['AutoCAD', 'Primavera P6'],
        bbbeeLevel: 7
      },
      matchScore: 92,
      distance: 45,
      urgency: 'high',
      psychTrigger: 'EXCLUSIVE: Grade 7+ ONLY - You qualify!'
    },
    {
      id: 'tender-003',
      title: 'IT Infrastructure - R8.5M',
      value: 8500000,
      location: {
        province: 'Western Cape',
        city: 'Cape Town',
        coordinates: { lat: -33.9249, lng: 18.4241 },
        address: 'CBD, Cape Town'
      },
      category: 'Technology',
      deadline: '2024-02-18T16:00:00',
      requirements: {
        skills: ['Network Engineering', 'Project Management'],
        tools: ['Microsoft Project', 'Visio'],
        bbbeeLevel: 4
      },
      matchScore: 67,
      distance: 1400,
      urgency: 'medium',
      psychTrigger: 'REMOTE OPPORTUNITY: High-value IT project'
    }
  ]);

  const [mapLayers, setMapLayers] = useState<MapLayer[]>([
    {
      id: 'tenders',
      name: 'Active Tenders',
      type: 'tenders',
      visible: true,
      count: 156,
      color: '#f44336'
    },
    {
      id: 'skills',
      name: 'Skill Providers',
      type: 'skills',
      visible: false,
      count: 89,
      color: '#2196f3'
    },
    {
      id: 'tools',
      name: 'Tool Providers',
      type: 'tools',
      visible: false,
      count: 45,
      color: '#ff9800'
    },
    {
      id: 'contractors',
      name: 'Available Contractors',
      type: 'contractors',
      visible: false,
      count: 234,
      color: '#4caf50'
    },
    {
      id: 'suppliers',
      name: 'Suppliers',
      type: 'suppliers',
      visible: false,
      count: 567,
      color: '#9c27b0'
    }
  ]);

  const [selectedRadius, setSelectedRadius] = useState(100);
  const [showOnlyMatches, setShowOnlyMatches] = useState(true);

  useEffect(() => {
    filterTendersByLocation();
  }, [selectedRadius, userLocation]);

  const filterTendersByLocation = () => {
    // Filter tenders based on user's service radius and preferences
    const filteredTenders = mapTenders.filter(tender => {
      if (showOnlyMatches && tender.matchScore < 70) return false;
      return tender.distance <= selectedRadius;
    });
    
    // Update psychological triggers based on proximity and scarcity
    filteredTenders.forEach(tender => {
      if (tender.distance <= 25) {
        tender.psychTrigger = `🎯 LOCAL OPPORTUNITY: ${tender.distance}km from you!`;
      } else if (tender.matchScore > 90) {
        tender.psychTrigger = `⭐ PERFECT MATCH: ${tender.matchScore}% compatibility!`;
      } else if (tender.urgency === 'critical') {
        tender.psychTrigger = `🚨 URGENT: Deadline approaching fast!`;
      }
    });
  };

  const handleLayerToggle = (layerId: string) => {
    setMapLayers(prev => prev.map(layer => 
      layer.id === layerId 
        ? { ...layer, visible: !layer.visible }
        : layer
    ));
  };

  const handleRadiusChange = (radius: number) => {
    setSelectedRadius(radius);
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return '#f44336';
      case 'high': return '#ff9800';
      case 'medium': return '#2196f3';
      case 'low': return '#4caf50';
      default: return '#757575';
    }
  };

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `R${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `R${(value / 1000).toFixed(0)}k`;
    return `R${value}`;
  };

  const getDistanceText = (distance: number) => {
    if (distance <= 25) return `${distance}km - LOCAL`;
    if (distance <= 100) return `${distance}km - REGIONAL`;
    if (distance <= 500) return `${distance}km - PROVINCIAL`;
    return `${distance}km - NATIONAL`;
  };

  const visibleTenders = mapTenders.filter(tender => 
    tender.distance <= selectedRadius && 
    (!showOnlyMatches || tender.matchScore >= 70)
  );

  return (
    <Card>
      <CardContent>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <LocationOn color="primary" />
            Map Intelligence Dashboard
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton>
              <MyLocation />
            </IconButton>
            <IconButton>
              <FilterList />
            </IconButton>
            <IconButton>
              <Refresh />
            </IconButton>
          </Box>
        </Box>

        {/* Location & Radius Controls */}
        <Card variant="outlined" sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="body1" fontWeight="bold" sx={{ mb: 2 }}>
              📍 Your Location: {userLocation.city}, {userLocation.province}
            </Typography>
            
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Service Radius:
            </Typography>
            <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
              {[25, 50, 100, 200, 500].map(radius => (
                <Button
                  key={radius}
                  variant={selectedRadius === radius ? 'contained' : 'outlined'}
                  size="small"
                  onClick={() => handleRadiusChange(radius)}
                >
                  {radius}km
                </Button>
              ))}
            </Stack>
            
            <FormControlLabel
              control={
                <Switch
                  checked={showOnlyMatches}
                  onChange={(e) => setShowOnlyMatches(e.target.checked)}
                />
              }
              label="Show only high-match opportunities (70%+)"
            />
          </CardContent>
        </Card>

        {/* Map Layers */}
        <Card variant="outlined" sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="body1" fontWeight="bold" sx={{ mb: 2 }}>
              🗺️ Map Layers
            </Typography>
            
            <Grid container spacing={2}>
              {mapLayers.map(layer => (
                <Grid item xs={6} sm={4} key={layer.id}>
                  <Box 
                    sx={{ 
                      p: 1, 
                      border: `2px solid ${layer.visible ? layer.color : '#e0e0e0'}`,
                      borderRadius: 2,
                      cursor: 'pointer',
                      '&:hover': { opacity: 0.8 }
                    }}
                    onClick={() => handleLayerToggle(layer.id)}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box 
                        sx={{ 
                          width: 12, 
                          height: 12, 
                          backgroundColor: layer.visible ? layer.color : '#e0e0e0',
                          borderRadius: '50%' 
                        }} 
                      />
                      <Typography variant="body2" fontWeight="bold">
                        {layer.name}
                      </Typography>
                    </Box>
                    <Typography variant="caption" color="text.secondary">
                      {layer.count} items
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>

        {/* Tender Intelligence Results */}
        <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
          🎯 Tender Intelligence Results ({visibleTenders.length} matches)
        </Typography>

        {visibleTenders.length === 0 && (
          <Alert severity="info">
            <Typography variant="body2">
              No tenders found in your {selectedRadius}km radius. Try expanding your search area.
            </Typography>
          </Alert>
        )}

        <Stack spacing={2}>
          {visibleTenders.map(tender => (
            <Card 
              key={tender.id}
              variant="outlined"
              sx={{ 
                border: `2px solid ${getUrgencyColor(tender.urgency)}40`,
                '&:hover': {
                  transform: 'translateY(-1px)',
                  boxShadow: 3
                }
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body1" fontWeight="bold" sx={{ mb: 0.5 }}>
                      {tender.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      📍 {tender.location.address} • {getDistanceText(tender.distance)}
                    </Typography>
                    <Typography variant="caption" color="primary.main" fontWeight="bold">
                      {tender.psychTrigger}
                    </Typography>
                  </Box>
                  <Stack spacing={1} alignItems="flex-end">
                    <Chip 
                      label={`${tender.matchScore}% MATCH`}
                      size="small"
                      color={tender.matchScore >= 90 ? 'success' : tender.matchScore >= 70 ? 'warning' : 'default'}
                      sx={{ fontWeight: 'bold' }}
                    />
                    <Chip 
                      label={tender.urgency.toUpperCase()}
                      size="small"
                      sx={{ 
                        backgroundColor: getUrgencyColor(tender.urgency),
                        color: 'white',
                        fontWeight: 'bold'
                      }}
                    />
                  </Stack>
                </Box>

                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Value</Typography>
                    <Typography variant="h6" fontWeight="bold" color="success.main">
                      {formatCurrency(tender.value)}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Category</Typography>
                    <Typography variant="body2" fontWeight="bold">
                      {tender.category}
                    </Typography>
                  </Grid>
                </Grid>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  Requirements:
                </Typography>
                <Stack direction="row" spacing={1} flexWrap="wrap" gap={1} sx={{ mb: 2 }}>
                  {tender.requirements.skills.map((skill, index) => (
                    <Chip key={index} label={skill} size="small" color="primary" />
                  ))}
                  {tender.requirements.tools.map((tool, index) => (
                    <Chip key={index} label={tool} size="small" color="secondary" />
                  ))}
                  <Chip 
                    label={`B-BBEE Level ${tender.requirements.bbbeeLevel}+`} 
                    size="small" 
                    color="warning" 
                  />
                </Stack>

                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<Visibility />}
                    sx={{ flex: 1 }}
                  >
                    View Details
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    startIcon={<FlashOn />}
                    sx={{
                      flex: 1,
                      backgroundColor: getUrgencyColor(tender.urgency),
                      '&:hover': {
                        backgroundColor: getUrgencyColor(tender.urgency),
                        opacity: 0.8
                      }
                    }}
                  >
                    {tender.matchScore >= 90 ? 'BID NOW!' : 'ANALYZE'}
                  </Button>
                </Box>
              </CardContent>
            </Card>
          ))}
        </Stack>

        {/* Map Intelligence Summary */}
        <Alert severity="success" sx={{ mt: 3 }}>
          <Typography variant="body2" fontWeight="bold">
            🧠 MAP INTELLIGENCE: {visibleTenders.length} opportunities within {selectedRadius}km
          </Typography>
          <Typography variant="caption">
            Average match score: {Math.round(visibleTenders.reduce((sum, t) => sum + t.matchScore, 0) / visibleTenders.length || 0)}% • 
            Total value: {formatCurrency(visibleTenders.reduce((sum, t) => sum + t.value, 0))}
          </Typography>
        </Alert>
      </CardContent>
    </Card>
  );
};

export default TenderMapIntelligence;
