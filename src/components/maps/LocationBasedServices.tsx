/**
 * Location-Based Services Integration
 * Maps ecosystem services (SkillSync, ToolSync, etc.) with geographic optimization
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Button,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Rating,
  Stack,
  Alert,
  Divider
} from '@mui/material';
import {
  LocationOn as LocationIcon,
  DirectionsCar as CarIcon,
  AccessTime as TimeIcon,
  AttachMoney as MoneyIcon,
  Psychology as PsychologyIcon,
  Star as StarIcon,
  Build as ToolIcon,
  People as SkillIcon,
  Engineering as ContractorIcon,
  Business as SupplierIcon
} from '@mui/icons-material';
import { GoogleMap, Marker, InfoWindow } from '@react-google-maps/api';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';

interface LocationService {
  id: string;
  name: string;
  type: 'skill' | 'tool' | 'contractor' | 'supplier';
  provider: string;
  position: { lat: number; lng: number };
  address: string;
  rating: number;
  distance: number; // km from user
  travelTime: number; // minutes
  price: number;
  currency: string;
  availability: 'available' | 'busy' | 'unavailable';
  psychologicalFit: number; // 0-100
  stressLevel: number; // 0-1 (how stressful to work with)
  responseTime: string;
  specialties: string[];
}

interface LocationBasedServicesProps {
  userLocation: { lat: number; lng: number } | null;
  selectedTenderId?: string;
  onServiceSelect?: (service: LocationService) => void;
}

const LocationBasedServices: React.FC<LocationBasedServicesProps> = ({
  userLocation,
  selectedTenderId,
  onServiceSelect
}) => {
  const {
    psychologicalState,
    isStressed,
    needsSimplification
  } = useNeuroMarketing();

  const [services, setServices] = useState<LocationService[]>([]);
  const [selectedService, setSelectedService] = useState<LocationService | null>(null);
  const [showInfoWindow, setShowInfoWindow] = useState(false);
  const [filterByStress, setFilterByStress] = useState(false);

  useEffect(() => {
    loadLocationServices();
  }, [userLocation]);

  useEffect(() => {
    if (isStressed) {
      setFilterByStress(true);
    }
  }, [isStressed]);

  const loadLocationServices = () => {
    if (!userLocation) return;

    // Mock location-based services around South Africa
    const mockServices: LocationService[] = [
      {
        id: 'service-001',
        name: 'Expert Project Manager - Sarah Johnson',
        type: 'skill',
        provider: 'SkillSync Professional',
        position: { lat: -26.1951, lng: 28.0568 }, // Sandton
        address: 'Sandton City, Johannesburg',
        rating: 4.9,
        distance: 12,
        travelTime: 20,
        price: 850,
        currency: 'ZAR',
        availability: 'available',
        psychologicalFit: 94,
        stressLevel: 0.2, // Low stress - very supportive
        responseTime: '< 2 hours',
        specialties: ['Government Tenders', 'Stress Management', 'Team Leadership']
      },
      {
        id: 'service-002',
        name: 'Professional Surveying Equipment',
        type: 'tool',
        provider: 'TechRent Solutions',
        position: { lat: -26.1849, lng: 28.0131 }, // Rosebank
        address: 'Rosebank, Johannesburg',
        rating: 4.7,
        distance: 8,
        travelTime: 15,
        price: 1200,
        currency: 'ZAR',
        availability: 'available',
        psychologicalFit: 87,
        stressLevel: 0.3, // Low-medium - includes training
        responseTime: '< 1 hour',
        specialties: ['GPS Equipment', 'Training Included', 'Technical Support']
      },
      {
        id: 'service-003',
        name: 'Elite Construction Team',
        type: 'contractor',
        provider: 'BuildMaster Contractors',
        position: { lat: -25.7461, lng: 28.1881 }, // Pretoria East
        address: 'Pretoria East, Gauteng',
        rating: 4.8,
        distance: 65,
        travelTime: 55,
        price: 25000,
        currency: 'ZAR',
        availability: 'busy',
        psychologicalFit: 91,
        stressLevel: 0.4, // Medium - handles all compliance
        responseTime: '< 30 minutes',
        specialties: ['CIDB Grade 9', 'Government Projects', 'Full Compliance']
      },
      {
        id: 'service-004',
        name: 'IT Hardware Supplier',
        type: 'supplier',
        provider: 'TechSupply Pro',
        position: { lat: -29.8579, lng: 31.0292 }, // Durban
        address: 'Durban Central, KwaZulu-Natal',
        rating: 4.6,
        distance: 560,
        travelTime: 480, // 8 hours drive
        price: 15000,
        currency: 'ZAR',
        availability: 'available',
        psychologicalFit: 76,
        stressLevel: 0.7, // High due to distance
        responseTime: '< 4 hours',
        specialties: ['Enterprise Hardware', 'Nationwide Delivery', 'Technical Support']
      },
      {
        id: 'service-005',
        name: 'Local Legal Compliance Expert',
        type: 'skill',
        provider: 'LegalEase Consultants',
        position: { lat: -26.2023, lng: 28.0436 }, // Johannesburg CBD
        address: 'Johannesburg CBD',
        rating: 4.9,
        distance: 5,
        travelTime: 12,
        price: 1200,
        currency: 'ZAR',
        availability: 'available',
        psychologicalFit: 96,
        stressLevel: 0.1, // Very low - specializes in reducing legal anxiety
        responseTime: '< 1 hour',
        specialties: ['Tender Compliance', 'Anxiety Reduction', 'Government Relations']
      }
    ];

    setServices(mockServices);
  };

  const getServiceIcon = (type: string) => {
    switch (type) {
      case 'skill': return <SkillIcon />;
      case 'tool': return <ToolIcon />;
      case 'contractor': return <ContractorIcon />;
      case 'supplier': return <SupplierIcon />;
      default: return <LocationIcon />;
    }
  };

  const getServiceColor = (type: string) => {
    switch (type) {
      case 'skill': return '#2196F3';
      case 'tool': return '#FF9800';
      case 'contractor': return '#4CAF50';
      case 'supplier': return '#9C27B0';
      default: return '#757575';
    }
  };

  const getStressLevelText = (stressLevel: number) => {
    if (stressLevel < 0.3) return 'Very Low Stress';
    if (stressLevel < 0.5) return 'Low Stress';
    if (stressLevel < 0.7) return 'Medium Stress';
    return 'High Stress';
  };

  const getStressLevelColor = (stressLevel: number) => {
    if (stressLevel < 0.3) return 'success';
    if (stressLevel < 0.5) return 'info';
    if (stressLevel < 0.7) return 'warning';
    return 'error';
  };

  const filteredServices = services.filter(service => {
    if (filterByStress && service.stressLevel > 0.5) return false;
    if (needsSimplification && service.distance > 50) return false;
    return true;
  });

  const handleServiceClick = (service: LocationService) => {
    setSelectedService(service);
    setShowInfoWindow(true);
    
    // Track engagement for analytics
    console.log('Service clicked:', {
      serviceId: service.id,
      serviceType: service.type,
      distance: service.distance,
      stressLevel: service.stressLevel,
      psychologicalState
    });
  };

  const renderServiceInfo = (service: LocationService) => (
    <Card sx={{ minWidth: 320, maxWidth: 400 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar sx={{ bgcolor: getServiceColor(service.type), mr: 2 }}>
            {getServiceIcon(service.type)}
          </Avatar>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
              {service.name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {service.provider}
            </Typography>
          </Box>
        </Box>

        <Stack spacing={1} sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Rating value={service.rating} precision={0.1} size="small" readOnly />
            <Typography variant="body2">({service.rating})</Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="body2">Distance:</Typography>
            <Typography variant="body2">{service.distance}km</Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="body2">Travel Time:</Typography>
            <Typography variant="body2">{service.travelTime} min</Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="body2">Price:</Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
              {service.currency} {service.price.toLocaleString()}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="body2">Response Time:</Typography>
            <Typography variant="body2">{service.responseTime}</Typography>
          </Box>
        </Stack>

        {/* Psychological Indicators */}
        <Alert 
          severity={getStressLevelColor(service.stressLevel)}
          sx={{ mb: 2 }}
        >
          <Typography variant="body2">
            🧠 <strong>{getStressLevelText(service.stressLevel)}</strong>
            <br />
            {service.psychologicalFit}% psychological compatibility
          </Typography>
        </Alert>

        {/* Specialties */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" sx={{ mb: 1, fontWeight: 'bold' }}>
            Specialties:
          </Typography>
          <Stack direction="row" spacing={0.5} flexWrap="wrap">
            {service.specialties.map((specialty, index) => (
              <Chip key={index} label={specialty} size="small" variant="outlined" />
            ))}
          </Stack>
        </Box>

        <Stack direction="row" spacing={1}>
          <Button
            variant="contained"
            size="small"
            onClick={() => onServiceSelect?.(service)}
            disabled={service.availability === 'unavailable'}
          >
            {service.availability === 'available' ? 'Select Service' : 
             service.availability === 'busy' ? 'Join Waitlist' : 'Unavailable'}
          </Button>
          
          <Button
            variant="outlined"
            size="small"
            onClick={() => window.open(`https://maps.google.com/dir/?api=1&destination=${service.position.lat},${service.position.lng}`)}
          >
            Directions
          </Button>
        </Stack>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      {/* Stress Filter Alert */}
      {isStressed && (
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            🧠 Showing only low-stress services to help manage your current stress level.
          </Typography>
        </Alert>
      )}

      {/* Services List */}
      <Typography variant="h6" sx={{ mb: 2 }}>
        📍 Nearby Services ({filteredServices.length})
      </Typography>
      
      <List>
        {filteredServices.map((service) => (
          <ListItem 
            key={service.id}
            onClick={() => handleServiceClick(service)}
            sx={{ 
              cursor: 'pointer',
              '&:hover': { backgroundColor: 'action.hover' },
              border: '1px solid',
              borderColor: 'divider',
              borderRadius: 1,
              mb: 1
            }}
          >
            <ListItemAvatar>
              <Avatar sx={{ bgcolor: getServiceColor(service.type) }}>
                {getServiceIcon(service.type)}
              </Avatar>
            </ListItemAvatar>
            
            <ListItemText
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                    {needsSimplification ? service.name.split(' ').slice(0, 3).join(' ') : service.name}
                  </Typography>
                  <Chip 
                    label={service.availability.toUpperCase()}
                    size="small"
                    color={service.availability === 'available' ? 'success' : 'warning'}
                  />
                </Box>
              }
              secondary={
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    {service.distance}km • {service.travelTime} min • {service.currency} {service.price.toLocaleString()}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                    <Rating value={service.rating} precision={0.1} size="small" readOnly />
                    <Chip 
                      label={`${service.psychologicalFit}% fit`}
                      size="small"
                      color="primary"
                    />
                    <Chip 
                      label={getStressLevelText(service.stressLevel)}
                      size="small"
                      color={getStressLevelColor(service.stressLevel)}
                    />
                  </Box>
                </Box>
              }
            />
          </ListItem>
        ))}
      </List>

      {filteredServices.length === 0 && (
        <Alert severity="info">
          <Typography variant="body2">
            No services found matching your current criteria. Try expanding your search radius or adjusting filters.
          </Typography>
        </Alert>
      )}
    </Box>
  );
};

export default LocationBasedServices;
