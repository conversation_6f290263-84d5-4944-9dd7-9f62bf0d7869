/**
 * TaskLocationMap - Interactive map for task and tender locations
 * Integrates with both Google Maps and Mapbox for comprehensive location services
 */

import React, { useEffect, useRef, useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Button,
  IconButton,
  Tooltip,
  Alert,
  Stack
} from '@mui/material';
import {
  MyLocation as LocationIcon,
  Directions as DirectionsIcon,
  Layers as LayersIcon,
  Fullscreen as FullscreenIcon
} from '@mui/icons-material';
import mapboxgl from 'mapbox-gl';
import { env } from '../../config/environment';
import GeomapService, { Coordinates, DistanceResult } from '../../services/geomap';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';

// Import Mapbox CSS
import 'mapbox-gl/dist/mapbox-gl.css';

interface TaskLocation {
  id: string;
  title: string;
  description?: string;
  coordinates: Coordinates;
  address: string;
  type: 'tender' | 'meeting' | 'site_visit' | 'delivery' | 'inspection';
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  deadline?: string;
  assignedTo?: string;
  estimatedDuration?: number; // minutes
  requirements?: string[];
}

interface TaskLocationMapProps {
  tasks: TaskLocation[];
  userLocation?: Coordinates;
  selectedTaskId?: string;
  onTaskSelect?: (task: TaskLocation) => void;
  onLocationUpdate?: (taskId: string, coordinates: Coordinates) => void;
  height?: string;
  showRouting?: boolean;
  psychologicalOptimization?: boolean;
}

const TaskLocationMap: React.FC<TaskLocationMapProps> = ({
  tasks = [],
  userLocation,
  selectedTaskId,
  onTaskSelect,
  onLocationUpdate,
  height = '600px',
  showRouting = true,
  psychologicalOptimization = true
}) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const markers = useRef<{ [key: string]: mapboxgl.Marker }>({});
  const [mapLoaded, setMapLoaded] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<Coordinates | null>(userLocation || null);
  const [routeInfo, setRouteInfo] = useState<DistanceResult | null>(null);
  const [mapStyle, setMapStyle] = useState('mapbox://styles/mapbox/streets-v12');
  
  const geoService = GeomapService.getInstance();
  const {
    psychologicalState,
    isStressed,
    needsSimplification
  } = useNeuroMarketing();

  // Initialize map
  useEffect(() => {
    if (!mapContainer.current || map.current) return;

    mapboxgl.accessToken = env.mapboxToken;

    if (!mapboxgl.accessToken) {
      console.error('Mapbox token not found');
      return;
    }

    // Adapt map style based on psychological state
    let initialStyle = mapStyle;
    if (psychologicalOptimization && isStressed) {
      initialStyle = 'mapbox://styles/mapbox/light-v11'; // Calmer, lighter style
    }

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: initialStyle,
      center: currentLocation ? [currentLocation.longitude, currentLocation.latitude] : [28.0473, -26.2041],
      zoom: currentLocation ? 12 : 10,
      attributionControl: false
    });

    // Add controls
    map.current.addControl(new mapboxgl.NavigationControl(), 'top-right');
    if (!needsSimplification) {
      map.current.addControl(new mapboxgl.FullscreenControl(), 'top-right');
    }

    // Map loaded event
    map.current.on('load', () => {
      setMapLoaded(true);
      
      // Add click handler for adding new locations
      map.current!.on('click', handleMapClick);
    });

    return () => {
      if (map.current) {
        map.current.remove();
        map.current = null;
      }
    };
  }, []);

  // Update markers when tasks change
  useEffect(() => {
    if (!map.current || !mapLoaded) return;

    // Clear existing markers
    Object.values(markers.current).forEach(marker => marker.remove());
    markers.current = {};

    // Add task markers
    tasks.forEach(task => {
      addTaskMarker(task);
    });

    // Add user location marker
    if (currentLocation) {
      addUserLocationMarker();
    }

    // Fit map to show all tasks
    if (tasks.length > 0) {
      fitMapToTasks();
    }
  }, [tasks, mapLoaded, currentLocation]);

  // Handle selected task
  useEffect(() => {
    if (!selectedTaskId || !map.current) return;

    const selectedTask = tasks.find(task => task.id === selectedTaskId);
    if (selectedTask) {
      // Center on selected task
      map.current.flyTo({
        center: [selectedTask.coordinates.longitude, selectedTask.coordinates.latitude],
        zoom: 15,
        duration: 1000
      });

      // Calculate route if user location is available
      if (currentLocation && showRouting) {
        calculateRoute(selectedTask);
      }

      // Track engagement for analytics
      console.log('Task location selected:', {
        taskId: selectedTaskId,
        taskType: selectedTask.type,
        psychologicalState
      });
    }
  }, [selectedTaskId, tasks, currentLocation, showRouting]);

  const addTaskMarker = (task: TaskLocation) => {
    if (!map.current) return;

    // Create custom marker element
    const markerElement = document.createElement('div');
    markerElement.className = 'task-marker';
    
    const color = getTaskColor(task);
    const icon = getTaskIcon(task.type);
    
    markerElement.innerHTML = `
      <div style="
        width: 36px;
        height: 36px;
        background-color: ${color};
        border: 2px solid white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        transition: transform 0.2s ease;
      ">
        ${icon}
      </div>
    `;

    // Add hover effects
    markerElement.addEventListener('mouseenter', () => {
      markerElement.style.transform = 'scale(1.1)';
    });

    markerElement.addEventListener('mouseleave', () => {
      if (task.id !== selectedTaskId) {
        markerElement.style.transform = 'scale(1)';
      }
    });

    // Create marker
    const marker = new mapboxgl.Marker(markerElement)
      .setLngLat([task.coordinates.longitude, task.coordinates.latitude])
      .addTo(map.current);

    // Create popup
    const popup = new mapboxgl.Popup({
      offset: 25,
      closeButton: true,
      closeOnClick: false
    }).setHTML(createTaskPopupContent(task));

    // Add click handler
    markerElement.addEventListener('click', () => {
      onTaskSelect?.(task);
      marker.setPopup(popup).togglePopup();
    });

    // Store marker reference
    markers.current[task.id] = marker;
  };

  const addUserLocationMarker = () => {
    if (!map.current || !currentLocation) return;

    const userMarkerElement = document.createElement('div');
    userMarkerElement.innerHTML = `
      <div style="
        width: 20px;
        height: 20px;
        background-color: #2196F3;
        border: 3px solid white;
        border-radius: 50%;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      "></div>
    `;

    new mapboxgl.Marker(userMarkerElement)
      .setLngLat([currentLocation.longitude, currentLocation.latitude])
      .addTo(map.current);
  };

  const createTaskPopupContent = (task: TaskLocation): string => {
    const deadlineText = task.deadline ? new Date(task.deadline).toLocaleDateString() : 'No deadline';
    const durationText = task.estimatedDuration ? `${task.estimatedDuration} minutes` : 'Unknown';
    
    return `
      <div style="padding: 12px; min-width: 250px;">
        <div style="margin-bottom: 8px;">
          <div style="font-weight: bold; font-size: 16px; margin-bottom: 4px;">${task.title}</div>
          <div style="font-size: 12px; color: #666;">${task.address}</div>
        </div>
        
        ${task.description ? `
          <div style="margin-bottom: 8px;">
            <div style="font-size: 14px;">${task.description}</div>
          </div>
        ` : ''}
        
        <div style="display: flex; gap: 4px; margin-bottom: 8px; flex-wrap: wrap;">
          <span style="background: ${getTaskColor(task)}; color: white; padding: 2px 6px; border-radius: 12px; font-size: 11px;">
            ${task.type.replace('_', ' ').toUpperCase()}
          </span>
          <span style="background: ${getPriorityColor(task.priority)}; color: white; padding: 2px 6px; border-radius: 12px; font-size: 11px;">
            ${task.priority.toUpperCase()}
          </span>
          <span style="background: ${getStatusColor(task.status)}; color: white; padding: 2px 6px; border-radius: 12px; font-size: 11px;">
            ${task.status.replace('_', ' ').toUpperCase()}
          </span>
        </div>
        
        <div style="font-size: 12px; color: #666; margin-bottom: 8px;">
          <div>Deadline: ${deadlineText}</div>
          <div>Duration: ${durationText}</div>
          ${task.assignedTo ? `<div>Assigned to: ${task.assignedTo}</div>` : ''}
        </div>
        
        ${task.requirements && task.requirements.length > 0 ? `
          <div style="margin-bottom: 8px;">
            <div style="font-size: 12px; font-weight: bold; margin-bottom: 4px;">Requirements:</div>
            <ul style="margin: 0; padding-left: 16px; font-size: 11px;">
              ${task.requirements.map(req => `<li>${req}</li>`).join('')}
            </ul>
          </div>
        ` : ''}
        
        <div style="display: flex; gap: 8px; margin-top: 8px;">
          <button onclick="window.open('https://maps.google.com/dir/?api=1&destination=${task.coordinates.latitude},${task.coordinates.longitude}')" 
                  style="background: #4CAF50; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer;">
            Directions
          </button>
          ${currentLocation ? `
            <button onclick="calculateTaskRoute('${task.id}')" 
                    style="background: #2196F3; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer;">
              Route Info
            </button>
          ` : ''}
        </div>
      </div>
    `;
  };

  const calculateRoute = async (task: TaskLocation) => {
    if (!currentLocation) return;

    try {
      const result = await geoService.calculateDistance(currentLocation, task.coordinates);
      setRouteInfo(result);
      
      // Track route calculation for analytics
      console.log('Route calculated:', {
        taskId: task.id,
        distance: result.distance,
        duration: result.duration,
        psychologicalState
      });
    } catch (error) {
      console.error('Route calculation failed:', error);
    }
  };

  const handleMapClick = (e: mapboxgl.MapMouseEvent) => {
    if (!onLocationUpdate) return;

    const coordinates: Coordinates = {
      latitude: e.lngLat.lat,
      longitude: e.lngLat.lng
    };

    // For now, we'll just log the coordinates
    // In a real implementation, you might show a dialog to create a new task
    console.log('Map clicked at:', coordinates);
  };

  const getCurrentLocation = async () => {
    try {
      const location = await geoService.getCurrentLocation();
      setCurrentLocation(location);
      
      if (map.current) {
        map.current.flyTo({
          center: [location.longitude, location.latitude],
          zoom: 14
        });
      }
      
      // Track user location for analytics
      console.log('User location obtained:', {
        accuracy: 'high',
        psychologicalState
      });
    } catch (error) {
      console.error('Failed to get current location:', error);
    }
  };

  const fitMapToTasks = () => {
    if (!map.current || tasks.length === 0) return;

    const coordinates: Coordinates[] = tasks.map(task => task.coordinates);
    if (currentLocation) {
      coordinates.push(currentLocation);
    }

    if (coordinates.length === 1) {
      map.current.flyTo({
        center: [coordinates[0].longitude, coordinates[0].latitude],
        zoom: 14
      });
    } else {
      const bounds = geoService.calculateBounds(coordinates);
      
      map.current.fitBounds([
        [bounds.southwest.longitude, bounds.southwest.latitude],
        [bounds.northeast.longitude, bounds.northeast.latitude]
      ], {
        padding: 50,
        maxZoom: 15
      });
    }
  };

  const getTaskColor = (task: TaskLocation): string => {
    switch (task.type) {
      case 'tender': return '#2196F3';
      case 'meeting': return '#FF9800';
      case 'site_visit': return '#4CAF50';
      case 'delivery': return '#9C27B0';
      case 'inspection': return '#F44336';
      default: return '#757575';
    }
  };

  const getTaskIcon = (type: string): string => {
    switch (type) {
      case 'tender': return '📋';
      case 'meeting': return '🤝';
      case 'site_visit': return '🏗️';
      case 'delivery': return '📦';
      case 'inspection': return '🔍';
      default: return '📍';
    }
  };

  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'critical': return '#F44336';
      case 'high': return '#FF9800';
      case 'medium': return '#2196F3';
      case 'low': return '#4CAF50';
      default: return '#757575';
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'completed': return '#4CAF50';
      case 'in_progress': return '#FF9800';
      case 'pending': return '#2196F3';
      case 'cancelled': return '#757575';
      default: return '#757575';
    }
  };

  if (!env.mapboxToken) {
    return (
      <Card>
        <CardContent>
          <Alert severity="error">
            Mapbox token not configured. Please set MAPBOX_TOKEN environment variable.
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Box sx={{ position: 'relative', height }}>
      {/* Map Container */}
      <div
        ref={mapContainer}
        style={{
          width: '100%',
          height: '100%',
          borderRadius: '8px',
          overflow: 'hidden'
        }}
      />

      {/* Controls Overlay */}
      <Box
        sx={{
          position: 'absolute',
          top: 16,
          left: 16,
          zIndex: 1000,
          display: 'flex',
          flexDirection: 'column',
          gap: 1
        }}
      >
        <Tooltip title="Get Current Location">
          <IconButton
            onClick={getCurrentLocation}
            sx={{
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              '&:hover': { backgroundColor: 'rgba(255, 255, 255, 1)' }
            }}
          >
            <LocationIcon />
          </IconButton>
        </Tooltip>

        {!needsSimplification && (
          <Tooltip title="Toggle Map Style">
            <IconButton
              onClick={() => {
                const newStyle = mapStyle === 'mapbox://styles/mapbox/streets-v12' 
                  ? 'mapbox://styles/mapbox/satellite-v9' 
                  : 'mapbox://styles/mapbox/streets-v12';
                setMapStyle(newStyle);
                if (map.current) {
                  map.current.setStyle(newStyle);
                }
              }}
              sx={{
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                '&:hover': { backgroundColor: 'rgba(255, 255, 255, 1)' }
              }}
            >
              <LayersIcon />
            </IconButton>
          </Tooltip>
        )}
      </Box>

      {/* Task Count and Route Info */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 16,
          left: 16,
          zIndex: 1000
        }}
      >
        <Stack spacing={1}>
          <Chip
            label={`${tasks.length} Tasks`}
            color="primary"
            size="small"
            sx={{
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              color: 'primary.main'
            }}
          />
          
          {routeInfo && (
            <Card sx={{ backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
              <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
                <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block' }}>
                  Route Info:
                </Typography>
                <Typography variant="caption">
                  {routeInfo.distance.toFixed(1)}km • {Math.round(routeInfo.duration)} min
                </Typography>
              </CardContent>
            </Card>
          )}
        </Stack>
      </Box>

      {/* Psychological Optimization Indicator */}
      {psychologicalOptimization && isStressed && (
        <Box
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            zIndex: 1000
          }}
        >
          <Chip
            label="🧠 Stress-Optimized View"
            size="small"
            color="info"
            sx={{
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              color: 'info.main'
            }}
          />
        </Box>
      )}
    </Box>
  );
};

export default TaskLocationMap;
