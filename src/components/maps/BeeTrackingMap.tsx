/**
 * BeeTrackingMap - Real-time tracking of Bee workers using Mapbox
 * Integrates with the existing BidBeez ecosystem for worker management
 */

import React, { useEffect, useRef, useState } from 'react';
import { Box, Chip, Typography, Card, CardContent, Avatar } from '@mui/material';
import mapboxgl from 'mapbox-gl';
import { env } from '../../config/environment';
import GeomapService, { Coordinates } from '../../services/geomap';

// Import Mapbox CSS
import 'mapbox-gl/dist/mapbox-gl.css';

interface BeeData {
  id: string;
  beeId: string;
  fullName: string;
  phoneNumber: string;
  email: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
    lastUpdated: Date;
  };
  currentTask?: {
    id: string;
    title: string;
    status: string;
    progress: number;
  };
  rating: number;
  isActive: boolean;
  avatar?: string;
}

interface BeeTrackingMapProps {
  bees: BeeData[] | undefined;
  selectedBeeId?: string;
  onBeeSelect?: (bee: BeeData) => void;
  height?: string;
  showControls?: boolean;
}

const BeeTrackingMap: React.FC<BeeTrackingMapProps> = ({
  bees = [],
  selectedBeeId,
  onBeeSelect,
  height = '500px',
  showControls = true
}) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const markers = useRef<{ [key: string]: mapboxgl.Marker }>({});
  const [mapLoaded, setMapLoaded] = useState(false);
  const geoService = GeomapService.getInstance();

  // Initialize map
  useEffect(() => {
    if (!mapContainer.current || map.current) return;

    // Set Mapbox access token
    mapboxgl.accessToken = env.mapboxToken;

    if (!mapboxgl.accessToken) {
      console.error('Mapbox token not found');
      return;
    }

    // Create map
    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/streets-v12',
      center: [28.0473, -26.2041], // Johannesburg
      zoom: 10,
      attributionControl: false
    });

    // Add controls if enabled
    if (showControls) {
      map.current.addControl(new mapboxgl.NavigationControl(), 'top-right');
      map.current.addControl(new mapboxgl.FullscreenControl(), 'top-right');
    }

    // Map loaded event
    map.current.on('load', () => {
      setMapLoaded(true);
    });

    return () => {
      if (map.current) {
        map.current.remove();
        map.current = null;
      }
    };
  }, [showControls]);

  // Update markers when bees data changes
  useEffect(() => {
    if (!map.current || !mapLoaded || !bees) return;

    // Clear existing markers
    Object.values(markers.current).forEach(marker => marker.remove());
    markers.current = {};

    // Add new markers
    bees.forEach(bee => {
      if (bee.location && bee.location.latitude && bee.location.longitude) {
        addBeeMarker(bee);
      }
    });

    // Fit map to show all bees
    if (bees.length > 0) {
      fitMapToBees();
    }
  }, [bees, mapLoaded]);

  // Highlight selected bee
  useEffect(() => {
    if (!selectedBeeId || !markers.current[selectedBeeId]) return;

    // Reset all markers
    Object.entries(markers.current).forEach(([id, marker]) => {
      const element = marker.getElement();
      element.style.transform = id === selectedBeeId ? 'scale(1.2)' : 'scale(1)';
      element.style.zIndex = id === selectedBeeId ? '1000' : '1';
    });

    // Center on selected bee
    const selectedBee = bees?.find(bee => bee.id === selectedBeeId);
    if (selectedBee && map.current) {
      map.current.flyTo({
        center: [selectedBee.location.longitude, selectedBee.location.latitude],
        zoom: 14,
        duration: 1000
      });
    }
  }, [selectedBeeId, bees]);

  const addBeeMarker = (bee: BeeData) => {
    if (!map.current) return;

    // Create custom marker element
    const markerElement = document.createElement('div');
    markerElement.className = 'bee-marker';
    markerElement.style.cssText = `
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 3px solid ${getBeeStatusColor(bee)};
      background-color: white;
      background-image: url(${bee.avatar || '/default-avatar.png'});
      background-size: cover;
      background-position: center;
      cursor: pointer;
      transition: transform 0.2s ease;
      box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    `;

    // Add hover effects
    markerElement.addEventListener('mouseenter', () => {
      markerElement.style.transform = 'scale(1.1)';
    });

    markerElement.addEventListener('mouseleave', () => {
      if (bee.id !== selectedBeeId) {
        markerElement.style.transform = 'scale(1)';
      }
    });

    // Create marker
    const marker = new mapboxgl.Marker(markerElement)
      .setLngLat([bee.location.longitude, bee.location.latitude])
      .addTo(map.current);

    // Create popup
    const popup = new mapboxgl.Popup({
      offset: 25,
      closeButton: false,
      closeOnClick: false
    }).setHTML(createBeePopupContent(bee));

    // Add click handler
    markerElement.addEventListener('click', () => {
      onBeeSelect?.(bee);
      marker.setPopup(popup).togglePopup();
    });

    // Store marker reference
    markers.current[bee.id] = marker;
  };

  const createBeePopupContent = (bee: BeeData): string => {
    const lastUpdated = new Date(bee.location.lastUpdated).toLocaleTimeString();
    
    return `
      <div style="padding: 8px; min-width: 200px;">
        <div style="display: flex; align-items: center; margin-bottom: 8px;">
          <img src="${bee.avatar || '/default-avatar.png'}" 
               style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;" />
          <div>
            <div style="font-weight: bold; font-size: 14px;">${bee.fullName}</div>
            <div style="font-size: 12px; color: #666;">ID: ${bee.beeId}</div>
          </div>
        </div>
        
        <div style="margin-bottom: 8px;">
          <div style="font-size: 12px; color: #666;">Status:</div>
          <span style="background: ${getBeeStatusColor(bee)}; color: white; padding: 2px 6px; border-radius: 12px; font-size: 11px;">
            ${bee.isActive ? 'ACTIVE' : 'OFFLINE'}
          </span>
        </div>
        
        ${bee.currentTask ? `
          <div style="margin-bottom: 8px;">
            <div style="font-size: 12px; color: #666;">Current Task:</div>
            <div style="font-size: 13px; font-weight: 500;">${bee.currentTask.title}</div>
            <div style="font-size: 11px; color: #666;">Progress: ${bee.currentTask.progress}%</div>
          </div>
        ` : ''}
        
        <div style="margin-bottom: 8px;">
          <div style="font-size: 12px; color: #666;">Rating:</div>
          <div style="font-size: 13px;">⭐ ${bee.rating.toFixed(1)}</div>
        </div>
        
        <div style="font-size: 11px; color: #999;">
          Last updated: ${lastUpdated}
        </div>
      </div>
    `;
  };

  const getBeeStatusColor = (bee: BeeData): string => {
    if (!bee.isActive) return '#757575'; // Gray for offline
    if (bee.currentTask) {
      switch (bee.currentTask.status) {
        case 'in_progress': return '#FF9800'; // Orange for busy
        case 'completed': return '#4CAF50'; // Green for completed
        default: return '#2196F3'; // Blue for available
      }
    }
    return '#4CAF50'; // Green for available
  };

  const fitMapToBees = () => {
    if (!map.current || !bees || bees.length === 0) return;

    const coordinates: Coordinates[] = bees
      .filter(bee => bee.location && bee.location.latitude && bee.location.longitude)
      .map(bee => ({
        latitude: bee.location.latitude,
        longitude: bee.location.longitude
      }));

    if (coordinates.length === 0) return;

    if (coordinates.length === 1) {
      // Single bee - center on it
      map.current.flyTo({
        center: [coordinates[0].longitude, coordinates[0].latitude],
        zoom: 14
      });
    } else {
      // Multiple bees - fit bounds
      const bounds = geoService.calculateBounds(coordinates);
      
      map.current.fitBounds([
        [bounds.southwest.longitude, bounds.southwest.latitude],
        [bounds.northeast.longitude, bounds.northeast.latitude]
      ], {
        padding: 50,
        maxZoom: 15
      });
    }
  };

  if (!env.mapboxToken) {
    return (
      <Card>
        <CardContent>
          <Typography color="error">
            Mapbox token not configured. Please set MAPBOX_TOKEN environment variable.
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Box sx={{ position: 'relative', height }}>
      {/* Map Container */}
      <div
        ref={mapContainer}
        style={{
          width: '100%',
          height: '100%',
          borderRadius: '8px',
          overflow: 'hidden'
        }}
      />

      {/* Loading Overlay */}
      {!mapLoaded && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            borderRadius: '8px'
          }}
        >
          <Typography>Loading map...</Typography>
        </Box>
      )}

      {/* Bee Count Overlay */}
      {mapLoaded && bees && (
        <Box
          sx={{
            position: 'absolute',
            top: 16,
            left: 16,
            zIndex: 1000
          }}
        >
          <Chip
            label={`${bees.length} Bees Tracked`}
            color="primary"
            size="small"
            sx={{
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              color: 'primary.main'
            }}
          />
        </Box>
      )}

      {/* Legend */}
      {mapLoaded && showControls && (
        <Box
          sx={{
            position: 'absolute',
            bottom: 16,
            left: 16,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderRadius: 1,
            p: 1,
            zIndex: 1000
          }}
        >
          <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block', mb: 0.5 }}>
            Status Legend:
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Box sx={{ width: 12, height: 12, borderRadius: '50%', backgroundColor: '#4CAF50' }} />
              <Typography variant="caption">Available</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Box sx={{ width: 12, height: 12, borderRadius: '50%', backgroundColor: '#FF9800' }} />
              <Typography variant="caption">Busy</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Box sx={{ width: 12, height: 12, borderRadius: '50%', backgroundColor: '#757575' }} />
              <Typography variant="caption">Offline</Typography>
            </Box>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default BeeTrackingMap;
