import React from 'react'

export function Toaster() {
  return <div id="toast-container" className="fixed top-4 right-4 z-50" />
}

export function toast(message: string, type: 'success' | 'error' | 'info' = 'info') {
  const container = document.getElementById('toast-container')
  if (!container) return

  const toastElement = document.createElement('div')
  toastElement.className = `
    mb-2 p-4 rounded-lg shadow-lg max-w-sm animate-slide-up
    ${type === 'success' ? 'bg-green-500 text-white' : ''}
    ${type === 'error' ? 'bg-red-500 text-white' : ''}
    ${type === 'info' ? 'bg-blue-500 text-white' : ''}
  `
  toastElement.textContent = message

  container.appendChild(toastElement)

  setTimeout(() => {
    toastElement.remove()
  }, 3000)
}
