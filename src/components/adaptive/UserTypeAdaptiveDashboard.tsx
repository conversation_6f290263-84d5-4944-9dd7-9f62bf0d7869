import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Grid,
  Chip,
  Stack,
  Button,
  Alert,
  LinearProgress,
  Avatar,
  Divider
} from '@mui/material';
import {
  Person,
  Build,
  School,
  Engineering,
  Business,
  TrendingUp,
  LocationOn,
  Psychology,
  FlashOn
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import TenderIntelligenceEngine from '../intelligence/TenderIntelligenceEngine';
import TenderMapIntelligence from '../maps/TenderMapIntelligence';

interface UserTypeConfig {
  type: 'bidder' | 'skill_provider' | 'tool_provider' | 'contractor' | 'supplier';
  displayName: string;
  icon: React.ReactNode;
  primaryColor: string;
  dashboardComponents: string[];
  psychologicalTriggers: string[];
  revenueStreams: string[];
}

interface TenderOpportunity {
  id: string;
  title: string;
  value: number;
  relevanceScore: number;
  urgency: 'critical' | 'high' | 'medium' | 'low';
  location: string;
  requirements: string[];
  psychTrigger: string;
  actionText: string;
}

const UserTypeAdaptiveDashboard: React.FC = () => {
  const { user } = useAuth();
  
  const [userType, setUserType] = useState<'bidder' | 'skill_provider' | 'tool_provider' | 'contractor' | 'supplier'>('bidder');
  
  const userTypeConfigs: Record<string, UserTypeConfig> = {
    bidder: {
      type: 'bidder',
      displayName: 'Bidder',
      icon: <Person />,
      primaryColor: '#2196f3',
      dashboardComponents: ['tender_intelligence', 'compliance_gaps', 'map_view', 'rfq_opportunities'],
      psychologicalTriggers: ['tender_scarcity', 'compliance_terror', 'bee_scarcity', 'success_correlation'],
      revenueStreams: ['tender_wins', 'rfq_success', 'compliance_services', 'bee_services']
    },
    skill_provider: {
      type: 'skill_provider',
      displayName: 'Skill Provider',
      icon: <School />,
      primaryColor: '#4caf50',
      dashboardComponents: ['training_demand', 'skill_gaps', 'map_view', 'certification_opportunities'],
      psychologicalTriggers: ['demand_surge', 'revenue_opportunity', 'scarcity_pricing', 'expertise_validation'],
      revenueStreams: ['training_courses', 'certification_fees', 'consulting', 'premium_content']
    },
    tool_provider: {
      type: 'tool_provider',
      displayName: 'Tool Provider',
      icon: <Build />,
      primaryColor: '#ff9800',
      dashboardComponents: ['tool_demand', 'license_opportunities', 'map_view', 'software_gaps'],
      psychologicalTriggers: ['tool_shortage', 'license_urgency', 'competitive_advantage', 'revenue_surge'],
      revenueStreams: ['license_sales', 'subscription_fees', 'support_services', 'training']
    },
    contractor: {
      type: 'contractor',
      displayName: 'Contractor',
      icon: <Engineering />,
      primaryColor: '#9c27b0',
      dashboardComponents: ['subcontract_opportunities', 'skill_matching', 'map_view', 'project_collaboration'],
      psychologicalTriggers: ['project_scarcity', 'location_advantage', 'skill_match', 'revenue_potential'],
      revenueStreams: ['subcontract_work', 'skill_services', 'equipment_rental', 'consulting']
    },
    supplier: {
      type: 'supplier',
      displayName: 'Supplier',
      icon: <Business />,
      primaryColor: '#f44336',
      dashboardComponents: ['rfq_alerts', 'quote_opportunities', 'map_view', 'demand_analysis'],
      psychologicalTriggers: ['rfq_surge', 'quote_competition', 'location_preference', 'volume_opportunity'],
      revenueStreams: ['product_sales', 'quote_wins', 'bulk_orders', 'premium_services']
    }
  };

  const [opportunities, setOpportunities] = useState<TenderOpportunity[]>([]);

  useEffect(() => {
    generateUserTypeOpportunities();
  }, [userType]);

  const generateUserTypeOpportunities = () => {
    const config = userTypeConfigs[userType];
    let generatedOpportunities: TenderOpportunity[] = [];

    switch (userType) {
      case 'bidder':
        generatedOpportunities = [
          {
            id: 'bid-1',
            title: 'Municipal Infrastructure - R15.6M',
            value: 15600000,
            relevanceScore: 89,
            urgency: 'critical',
            location: 'Johannesburg, Gauteng',
            requirements: ['PMP Certification', 'AutoCAD', 'B-BBEE Level 4'],
            psychTrigger: 'PERFECT MATCH: 89% compatibility - missing PMP blocks entry!',
            actionText: 'Get SkillSync PMP Now'
          },
          {
            id: 'bid-2',
            title: 'Highway Construction - R22.4M',
            value: 22400000,
            relevanceScore: 94,
            urgency: 'high',
            location: 'Pretoria, Gauteng',
            requirements: ['Civil Engineering', 'Primavera P6', 'Grade 7+ CIDB'],
            psychTrigger: 'EXCLUSIVE ACCESS: You qualify for Grade 7+ tender!',
            actionText: 'Bid Now'
          }
        ];
        break;

      case 'skill_provider':
        generatedOpportunities = [
          {
            id: 'skill-1',
            title: 'PMP Training Demand Surge',
            value: 250000,
            relevanceScore: 95,
            urgency: 'critical',
            location: 'Gauteng Province',
            requirements: ['PMP Expertise', 'Training Certification', 'SkillSync Partnership'],
            psychTrigger: 'REVENUE SURGE: 47 bidders need PMP for R156M in tenders!',
            actionText: 'Offer Training Course'
          },
          {
            id: 'skill-2',
            title: 'ISO 9001 Certification Demand',
            value: 180000,
            relevanceScore: 87,
            urgency: 'high',
            location: 'Western Cape',
            requirements: ['ISO Expertise', 'Auditor Certification'],
            psychTrigger: 'HIGH DEMAND: 23 companies need ISO certification urgently!',
            actionText: 'Create Course'
          }
        ];
        break;

      case 'tool_provider':
        generatedOpportunities = [
          {
            id: 'tool-1',
            title: 'AutoCAD License Shortage',
            value: 320000,
            relevanceScore: 92,
            urgency: 'critical',
            location: 'National',
            requirements: ['AutoCAD Licenses', 'ToolSync Partnership', 'Support Services'],
            psychTrigger: 'CRITICAL SHORTAGE: 34 bidders need AutoCAD for R89M tenders!',
            actionText: 'Provide Licenses'
          },
          {
            id: 'tool-2',
            title: 'Microsoft Project Demand',
            value: 150000,
            relevanceScore: 78,
            urgency: 'medium',
            location: 'Gauteng',
            requirements: ['MS Project Licenses', 'Training Support'],
            psychTrigger: 'OPPORTUNITY: Project management tools in high demand!',
            actionText: 'Offer Package'
          }
        ];
        break;

      case 'contractor':
        generatedOpportunities = [
          {
            id: 'contract-1',
            title: 'Subcontract: Infrastructure Project',
            value: 2340000,
            relevanceScore: 91,
            urgency: 'high',
            location: '25km from you',
            requirements: ['Construction Skills', 'Local Presence', 'Equipment'],
            psychTrigger: 'PERFECT LOCATION: Major contractor needs local subcontractor!',
            actionText: 'Apply for Subcontract'
          },
          {
            id: 'contract-2',
            title: 'Skill Partnership Opportunity',
            value: 890000,
            relevanceScore: 85,
            urgency: 'medium',
            location: 'Regional',
            requirements: ['Specialized Skills', 'ContractorSync Profile'],
            psychTrigger: 'SKILL MATCH: Your expertise needed for multiple projects!',
            actionText: 'Join Partnership'
          }
        ];
        break;

      case 'supplier':
        generatedOpportunities = [
          {
            id: 'supply-1',
            title: 'RFQ Surge: Construction Materials',
            value: 1250000,
            relevanceScore: 88,
            urgency: 'critical',
            location: 'Gauteng',
            requirements: ['Construction Materials', 'Bulk Supply', 'Fast Delivery'],
            psychTrigger: 'RFQ EXPLOSION: 12 new RFQs for your products this week!',
            actionText: 'Submit Quotes'
          },
          {
            id: 'supply-2',
            title: 'Bulk Order Opportunity',
            value: 780000,
            relevanceScore: 82,
            urgency: 'high',
            location: 'Western Cape',
            requirements: ['Office Supplies', 'Volume Discount', 'Delivery'],
            psychTrigger: 'VOLUME OPPORTUNITY: Large order potential from multiple bidders!',
            actionText: 'Prepare Quote'
          }
        ];
        break;
    }

    setOpportunities(generatedOpportunities);
  };

  const currentConfig = userTypeConfigs[userType];

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return '#f44336';
      case 'high': return '#ff9800';
      case 'medium': return '#2196f3';
      case 'low': return '#4caf50';
      default: return '#757575';
    }
  };

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `R${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `R${(value / 1000).toFixed(0)}k`;
    return `R${value}`;
  };

  return (
    <Box>
      {/* User Type Header */}
      <Card sx={{ mb: 3, background: `linear-gradient(135deg, ${currentConfig.primaryColor}20, ${currentConfig.primaryColor}10)` }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Avatar sx={{ backgroundColor: currentConfig.primaryColor }}>
              {currentConfig.icon}
            </Avatar>
            <Box>
              <Typography variant="h5" fontWeight="bold">
                {currentConfig.displayName} Dashboard
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Personalized intelligence for {currentConfig.displayName.toLowerCase()}s
              </Typography>
            </Box>
          </Box>

          {/* User Type Selector */}
          <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
            {Object.values(userTypeConfigs).map(config => (
              <Button
                key={config.type}
                variant={userType === config.type ? 'contained' : 'outlined'}
                size="small"
                startIcon={config.icon}
                onClick={() => setUserType(config.type)}
                sx={{
                  backgroundColor: userType === config.type ? config.primaryColor : 'transparent',
                  borderColor: config.primaryColor,
                  color: userType === config.type ? 'white' : config.primaryColor,
                  '&:hover': {
                    backgroundColor: config.primaryColor,
                    color: 'white'
                  }
                }}
              >
                {config.displayName}
              </Button>
            ))}
          </Stack>
        </CardContent>
      </Card>

      {/* Opportunities for Current User Type */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" fontWeight="bold" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
            <Psychology color="primary" />
            Personalized Opportunities for {currentConfig.displayName}s
          </Typography>

          <Stack spacing={2}>
            {opportunities.map(opportunity => (
              <Card 
                key={opportunity.id}
                variant="outlined"
                sx={{ 
                  border: `2px solid ${getUrgencyColor(opportunity.urgency)}40`,
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    boxShadow: 2
                  }
                }}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="body1" fontWeight="bold" sx={{ mb: 0.5 }}>
                        {opportunity.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        📍 {opportunity.location}
                      </Typography>
                      <Typography variant="caption" color="primary.main" fontWeight="bold">
                        {opportunity.psychTrigger}
                      </Typography>
                    </Box>
                    <Stack spacing={1} alignItems="flex-end">
                      <Chip 
                        label={`${opportunity.relevanceScore}% MATCH`}
                        size="small"
                        color={opportunity.relevanceScore >= 90 ? 'success' : 'warning'}
                        sx={{ fontWeight: 'bold' }}
                      />
                      <Chip 
                        label={opportunity.urgency.toUpperCase()}
                        size="small"
                        sx={{ 
                          backgroundColor: getUrgencyColor(opportunity.urgency),
                          color: 'white',
                          fontWeight: 'bold'
                        }}
                      />
                    </Stack>
                  </Box>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Requirements:
                  </Typography>
                  <Stack direction="row" spacing={1} flexWrap="wrap" gap={1} sx={{ mb: 2 }}>
                    {opportunity.requirements.map((req, index) => (
                      <Chip key={index} label={req} size="small" />
                    ))}
                  </Stack>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="h6" fontWeight="bold" color="success.main">
                      {formatCurrency(opportunity.value)}
                    </Typography>
                    <Button
                      variant="contained"
                      startIcon={<FlashOn />}
                      sx={{
                        backgroundColor: getUrgencyColor(opportunity.urgency),
                        '&:hover': {
                          backgroundColor: getUrgencyColor(opportunity.urgency),
                          opacity: 0.8
                        }
                      }}
                    >
                      {opportunity.actionText}
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            ))}
          </Stack>
        </CardContent>
      </Card>

      {/* Tender Intelligence Engine */}
      <Box sx={{ mb: 3 }}>
        <TenderIntelligenceEngine />
      </Box>

      {/* Map Intelligence */}
      <Box sx={{ mb: 3 }}>
        <TenderMapIntelligence />
      </Box>

      {/* User Type Success Metrics */}
      <Alert severity="success">
        <Typography variant="body2" fontWeight="bold">
          🎯 {currentConfig.displayName.toUpperCase()} SUCCESS: 
          {userType === 'bidder' && ' Tender-driven intelligence increases win rates by 67%!'}
          {userType === 'skill_provider' && ' Demand-based training increases revenue by 340%!'}
          {userType === 'tool_provider' && ' Gap-based licensing increases sales by 280%!'}
          {userType === 'contractor' && ' Location-based matching increases project wins by 190%!'}
          {userType === 'supplier' && ' RFQ intelligence increases quote success by 220%!'}
        </Typography>
        <Typography variant="caption">
          All intelligence flows from tender document analysis and user profile matching
        </Typography>
      </Alert>
    </Box>
  );
};

export default UserTypeAdaptiveDashboard;
