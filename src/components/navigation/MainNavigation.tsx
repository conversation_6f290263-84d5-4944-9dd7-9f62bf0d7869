import React from 'react';
import { useRouter } from 'next/router';
import { 
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Chip,
  Box,
  Typography,
  Divider
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Description as FileTextIcon,
  Gavel as GavelIcon,
  Shield as ShieldIcon,
  EmojiEvents as TrophyIcon,
  Psychology as BrainIcon,
  Groups as UsersIcon,
  Person as UserIcon,
  Settings as SettingsIcon,
  Business as BuildingIcon,
  Map as MapIcon,
  School as SchoolIcon,
  Build as BuildIcon,
  FlashOn as RFQIcon,
  Analytics as AnalyticsIcon,
  WhatsApp as WhatsAppIcon,
  Store as SupplierIcon,
  Assessment as ReportsIcon
} from '@mui/icons-material';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType;
  description?: string;
  badge?: string;
  isNew?: boolean;
  userTypes?: string[];
  roles?: string[];
}

interface MainNavigationProps {
  user?: {
    user_metadata?: {
      user_type?: string;
    };
    role?: string;
  };
}

const MainNavigation: React.FC<MainNavigationProps> = ({ user }) => {
  const router = useRouter();
  
  // Determine user type for conditional navigation
  const userType = user?.user_metadata?.user_type || 'bidder';
  const userRole = user?.role || 'user';

  const navigationItems: NavigationItem[] = [
    {
      name: 'Dashboard',
      href: '/',
      icon: DashboardIcon,
      description: 'Tender-driven intelligence overview'
    },
    {
      name: 'Tender Intelligence',
      href: '/tender-intelligence',
      icon: BrainIcon,
      description: 'AI-powered tender analysis'
    },
    {
      name: 'Map View',
      href: '/map',
      icon: MapIcon,
      description: 'Geographic tender opportunities'
    },
    {
      name: 'Tenders',
      href: '/tenders',
      icon: FileTextIcon,
      description: 'Browse and manage tenders'
    },
    {
      name: 'RFQ Management',
      href: '/rfq',
      icon: RFQIcon,
      description: 'Create and manage RFQs'
    },
    {
      name: 'Bids',
      href: '/bids',
      icon: GavelIcon,
      description: 'Your bidding activity'
    },
    {
      name: 'SkillSync',
      href: '/skillsync',
      icon: SchoolIcon,
      description: 'Skills and certifications'
    },
    {
      name: 'ToolSync',
      href: '/toolsync',
      icon: BuildIcon,
      description: 'Software tools and licenses'
    },
    {
      name: 'Compliance',
      href: '/compliance',
      icon: ShieldIcon,
      description: 'Compliance management'
    },
    {
      name: 'Gamification',
      href: '/gamification',
      icon: TrophyIcon,
      description: 'Achievements and rewards'
    },

    // NEW: Analytics Features
    {
      name: 'Bid Analytics',
      href: '/analytics',
      icon: AnalyticsIcon,
      description: 'Performance insights and reports',
      badge: 'New',
      isNew: true
    },

    // NEW: WhatsApp Auto-Bidding
    {
      name: 'WhatsApp Auto-Bid',
      href: '/whatsapp',
      icon: WhatsAppIcon,
      description: 'Automated bidding via WhatsApp',
      badge: 'Premium',
      isNew: true,
      userTypes: ['bidder', 'contractor']
    },

    // NEW: Supplier Features
    {
      name: 'Supplier Dashboard',
      href: '/supplier',
      icon: SupplierIcon,
      description: 'Supplier revenue and management',
      badge: 'Revenue',
      isNew: true,
      userTypes: ['supplier', 'bidder', 'contractor']
    },

    // NEW: Psychological Systems (conditional based on user type)
    {
      name: 'Sales Rep Centre',
      href: '/sales-rep-centre',
      icon: BrainIcon,
      description: 'Psychological sales intelligence',
      badge: 'New',
      isNew: true,
      userTypes: ['supplier', 'sales_rep']
    },

    {
      name: 'Supplier Access',
      href: '/contractor-supplier',
      icon: UsersIcon,
      description: 'Access supplier network',
      badge: 'New',
      isNew: true,
      userTypes: ['contractor']
    },
    
    // Settings
    {
      name: 'Settings',
      href: '/settings',
      icon: SettingsIcon,
      description: 'Account and app preferences'
    },

    // Admin/Management items
    {
      name: 'User Management',
      href: '/admin/users',
      icon: UserIcon,
      description: 'Manage platform users',
      roles: ['admin']
    }
  ];

  // Filter navigation items based on user type and role
  const filteredItems = navigationItems.filter(item => {
    // Check user type restrictions
    if (item.userTypes && !item.userTypes.includes(userType)) {
      return false;
    }
    
    // Check role restrictions
    if (item.roles && !item.roles.includes(userRole)) {
      return false;
    }
    
    return true;
  });

  const isActive = (href: string) => {
    if (href === '/') {
      return router.pathname === '/';
    }
    return router.pathname.startsWith(href);
  };

  const handleNavigation = (href: string) => {
    router.push(href);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <List sx={{ py: 0 }}>
        {filteredItems.map((item, index) => {
          const active = isActive(item.href);
          const IconComponent = item.icon;
          
          return (
            <React.Fragment key={item.name}>
              <ListItem disablePadding>
                <ListItemButton
                  onClick={() => handleNavigation(item.href)}
                  sx={{
                    py: 1.5,
                    px: 2,
                    borderRadius: 1,
                    mx: 1,
                    mb: 0.5,
                    backgroundColor: active ? 'primary.50' : 'transparent',
                    borderLeft: active ? '3px solid' : '3px solid transparent',
                    borderLeftColor: active ? 'primary.main' : 'transparent',
                    '&:hover': {
                      backgroundColor: active ? 'primary.100' : 'grey.50',
                    },
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <Box
                      component={IconComponent}
                      sx={{ 
                        color: active ? 'primary.main' : 'grey.600',
                        fontSize: 20
                      }} 
                    />
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: active ? 600 : 500,
                            color: active ? 'primary.main' : 'text.primary',
                          }}
                        >
                          {item.name}
                        </Typography>
                        {item.badge && (
                          <Chip
                            label={item.badge}
                            size="small"
                            sx={{
                              height: 20,
                              fontSize: '0.7rem',
                              backgroundColor: item.isNew ? 'success.100' : 'primary.100',
                              color: item.isNew ? 'success.800' : 'primary.800',
                              fontWeight: 600,
                            }}
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      item.description && (
                        <Typography
                          variant="caption"
                          sx={{
                            color: 'text.secondary',
                            fontSize: '0.75rem',
                            mt: 0.25,
                            display: 'block',
                          }}
                        >
                          {item.description}
                        </Typography>
                      )
                    }
                  />
                </ListItemButton>
              </ListItem>
              
              {/* Add divider after main navigation items */}
              {index === 4 && filteredItems.length > 5 && (
                <Divider sx={{ my: 1, mx: 2 }} />
              )}
            </React.Fragment>
          );
        })}
      </List>
      
      {/* Feature highlight for new psychological systems */}
      {(userType === 'supplier' || userType === 'sales_rep' || userType === 'contractor') && (
        <Box sx={{ p: 2, m: 1, backgroundColor: 'primary.50', borderRadius: 2, mt: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <BrainIcon sx={{ color: 'primary.main', fontSize: 20 }} />
            <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'primary.main' }}>
              New Features!
            </Typography>
          </Box>
          <Typography variant="caption" color="text.secondary">
            {userType === 'contractor' 
              ? 'Access our supplier network with AI-powered matching'
              : 'Unlock psychological sales intelligence and personalized targets'
            }
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default MainNavigation;
