/**
 * Cognitive-Aware Document Management
 * Adapts to user's mental capacity and reduces information overload
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Menu,
  MenuItem,
  Alert,
  LinearProgress,
  Collapse,
  Stack,
  Divider,
  Paper,
  Tooltip,
  Badge
} from '@mui/material';
import {
  Search as SearchIcon,
  Upload as UploadIcon,
  Description as DocumentIcon,
  Folder as FolderIcon,
  MoreVert as MoreIcon,
  Download as DownloadIcon,
  Share as ShareIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Psychology as PsychologyIcon,
  AutoAwesome as AIIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Timer as TimerIcon,
  FilterList as FilterIcon,
  Sort as SortIcon,
  ViewList as ListViewIcon,
  ViewModule as GridViewIcon
} from '@mui/icons-material';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';
import { Document, DocumentStatus } from '../../types/tender.types';

interface DocumentLibraryProps {
  bidId?: string;
  tenderId?: string;
}

const DocumentLibrary: React.FC<DocumentLibraryProps> = ({ bidId, tenderId }) => {
  // Behavioral tracking
  const {
    psychologicalState,
    isHighCognitiveLoad,
    isStressed,
    needsSimplification,
    trackEngagement,
    trackCognitiveLoad
  } = useNeuroMarketing();

  // State management
  const [documents, setDocuments] = useState<Document[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'status'>('date');
  const [showFilters, setShowFilters] = useState(false);
  const [cognitiveLoadWarning, setCognitiveLoadWarning] = useState(false);
  const [simplifiedView, setSimplifiedView] = useState(false);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);

  // Load documents
  useEffect(() => {
    loadDocuments();
  }, [bidId, tenderId]);

  // Monitor cognitive load
  useEffect(() => {
    if (psychologicalState?.cognitiveLoad > 0.7) {
      setCognitiveLoadWarning(true);
      setSimplifiedView(true);
      trackCognitiveLoad(0.8, Date.now());
    }
  }, [psychologicalState]);

  // Filter and sort documents
  useEffect(() => {
    let filtered = documents;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(doc => 
        doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        doc.type.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(doc => doc.type === selectedCategory);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'date':
          return new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime();
        case 'status':
          return a.status.localeCompare(b.status);
        default:
          return 0;
      }
    });

    setFilteredDocuments(filtered);
  }, [documents, searchTerm, selectedCategory, sortBy]);

  const loadDocuments = () => {
    // Mock document data
    const mockDocuments: Document[] = [
      {
        id: 'doc-1',
        name: 'B-BBEE Certificate Level 2.pdf',
        type: 'compliance',
        size: 2048576,
        uploadDate: '2024-01-15T10:30:00Z',
        status: DocumentStatus.VERIFIED,
        version: 1,
        uploadedBy: 'John Doe',
        verifiedBy: 'Legal Team',
        notes: 'Valid until December 2024'
      },
      {
        id: 'doc-2',
        name: 'Tax Clearance Certificate.pdf',
        type: 'compliance',
        size: 1536000,
        uploadDate: '2024-01-16T14:20:00Z',
        status: DocumentStatus.IN_PROGRESS,
        version: 1,
        uploadedBy: 'John Doe'
      },
      {
        id: 'doc-3',
        name: 'Technical Proposal Draft.docx',
        type: 'proposal',
        size: 5242880,
        uploadDate: '2024-01-18T09:15:00Z',
        status: DocumentStatus.IN_PROGRESS,
        version: 3,
        uploadedBy: 'Jane Smith',
        notes: 'Needs final review before submission'
      },
      {
        id: 'doc-4',
        name: 'Company Profile 2024.pdf',
        type: 'company',
        size: 3145728,
        uploadDate: '2024-01-10T16:45:00Z',
        status: DocumentStatus.COMPLETED,
        version: 1,
        uploadedBy: 'Admin'
      },
      {
        id: 'doc-5',
        name: 'Professional Indemnity Insurance.pdf',
        type: 'insurance',
        size: 1048576,
        uploadDate: '2024-01-20T11:30:00Z',
        status: DocumentStatus.NOT_STARTED,
        version: 1,
        uploadedBy: 'John Doe'
      }
    ];

    setDocuments(mockDocuments);
    
    trackEngagement('document_library_viewed', {
      documentCount: mockDocuments.length,
      psychologicalState
    });
  };

  const getStatusIcon = (status: DocumentStatus) => {
    switch (status) {
      case DocumentStatus.VERIFIED:
      case DocumentStatus.COMPLETED:
        return <CheckIcon color="success" />;
      case DocumentStatus.IN_PROGRESS:
        return <TimerIcon color="warning" />;
      case DocumentStatus.REJECTED:
        return <WarningIcon color="error" />;
      default:
        return <DocumentIcon color="action" />;
    }
  };

  const getStatusColor = (status: DocumentStatus) => {
    switch (status) {
      case DocumentStatus.VERIFIED:
      case DocumentStatus.COMPLETED:
        return 'success';
      case DocumentStatus.IN_PROGRESS:
        return 'warning';
      case DocumentStatus.REJECTED:
        return 'error';
      default:
        return 'default';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getDocumentCategories = () => {
    const categories = ['all', ...new Set(documents.map(doc => doc.type))];
    return categories;
  };

  const handleDocumentAction = (action: string, document: Document) => {
    trackEngagement('document_action', {
      action,
      documentId: document.id,
      documentType: document.type,
      psychologicalState
    });

    switch (action) {
      case 'view':
        // Open document viewer
        break;
      case 'download':
        // Download document
        break;
      case 'share':
        // Share document
        break;
      case 'delete':
        // Delete document
        break;
    }
  };

  const renderSimplifiedView = () => (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        📁 Your Documents ({filteredDocuments.length})
      </Typography>
      
      {filteredDocuments.map((document) => (
        <Card key={document.id} sx={{ mb: 2 }}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={8}>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                  {document.name}
                </Typography>
                <Chip 
                  label={document.status.replace('_', ' ').toUpperCase()}
                  size="small"
                  color={getStatusColor(document.status)}
                  sx={{ mt: 1 }}
                />
              </Grid>
              <Grid item xs={4}>
                <Stack direction="row" spacing={1} justifyContent="flex-end">
                  <Button 
                    size="small" 
                    variant="outlined"
                    onClick={() => handleDocumentAction('view', document)}
                  >
                    View
                  </Button>
                </Stack>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      ))}
    </Box>
  );

  const renderFullView = () => (
    <List>
      {filteredDocuments.map((document) => (
        <ListItem key={document.id} divider>
          <ListItemIcon>
            {getStatusIcon(document.status)}
          </ListItemIcon>
          
          <ListItemText
            primary={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                  {document.name}
                </Typography>
                <Chip 
                  label={document.status.replace('_', ' ').toUpperCase()}
                  size="small"
                  color={getStatusColor(document.status)}
                />
                {document.version > 1 && (
                  <Chip 
                    label={`v${document.version}`}
                    size="small"
                    variant="outlined"
                  />
                )}
              </Box>
            }
            secondary={
              <Box>
                <Typography variant="body2" color="text.secondary">
                  {formatFileSize(document.size)} • Uploaded {new Date(document.uploadDate).toLocaleDateString()} by {document.uploadedBy}
                </Typography>
                {document.notes && (
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                    📝 {document.notes}
                  </Typography>
                )}
              </Box>
            }
          />
          
          <ListItemSecondaryAction>
            <IconButton
              onClick={(e) => {
                setMenuAnchor(e.currentTarget);
                setSelectedDocument(document);
              }}
            >
              <MoreIcon />
            </IconButton>
          </ListItemSecondaryAction>
        </ListItem>
      ))}
    </List>
  );

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Card elevation={2} sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <Typography variant={isHighCognitiveLoad ? "h4" : "h5"} sx={{ fontWeight: 'bold', mb: 1 }}>
                📁 Document Library
              </Typography>
              <Typography variant="body1" color="text.secondary">
                {simplifiedView ? 'Simplified view for easier browsing' : 'Manage all your tender documents in one place'}
              </Typography>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Stack direction="row" spacing={1} justifyContent="flex-end">
                <Button
                  variant="contained"
                  startIcon={<UploadIcon />}
                  onClick={() => trackEngagement('upload_document', {})}
                >
                  Upload Document
                </Button>
                
                {!needsSimplification && (
                  <Button
                    variant="outlined"
                    startIcon={<AIIcon />}
                    onClick={() => trackEngagement('ai_document_assistant', {})}
                  >
                    AI Assistant
                  </Button>
                )}
              </Stack>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Cognitive Load Warning */}
      <Collapse in={cognitiveLoadWarning}>
        <Alert 
          severity="info" 
          icon={<PsychologyIcon />}
          sx={{ mb: 3 }}
          action={
            <Button size="small" onClick={() => setCognitiveLoadWarning(false)}>
              Got it
            </Button>
          }
        >
          <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
            🧠 Simplified View Activated
          </Typography>
          We've simplified the interface to reduce cognitive load. You can switch back to full view anytime.
        </Alert>
      </Collapse>

      {/* Search and Filters */}
      {!simplifiedView && (
        <Card elevation={2} sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="Search documents..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    )
                  }}
                />
              </Grid>
              
              <Grid item xs={12} md={8}>
                <Stack direction="row" spacing={1} alignItems="center">
                  <Typography variant="body2" color="text.secondary">
                    Category:
                  </Typography>
                  {getDocumentCategories().map((category) => (
                    <Chip
                      key={category}
                      label={category.charAt(0).toUpperCase() + category.slice(1)}
                      variant={selectedCategory === category ? 'filled' : 'outlined'}
                      onClick={() => setSelectedCategory(category)}
                      size="small"
                    />
                  ))}
                  
                  <Divider orientation="vertical" flexItem />
                  
                  <Tooltip title="List View">
                    <IconButton 
                      onClick={() => setViewMode('list')}
                      color={viewMode === 'list' ? 'primary' : 'default'}
                    >
                      <ListViewIcon />
                    </IconButton>
                  </Tooltip>
                  
                  <Tooltip title="Simplified View">
                    <IconButton 
                      onClick={() => setSimplifiedView(true)}
                      color={simplifiedView ? 'primary' : 'default'}
                    >
                      <PsychologyIcon />
                    </IconButton>
                  </Tooltip>
                </Stack>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Document List */}
      <Card elevation={2}>
        <CardContent>
          {simplifiedView ? renderSimplifiedView() : renderFullView()}
          
          {filteredDocuments.length === 0 && (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <DocumentIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                No documents found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {searchTerm ? 'Try adjusting your search terms' : 'Upload your first document to get started'}
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Document Actions Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
      >
        <MenuItem onClick={() => selectedDocument && handleDocumentAction('view', selectedDocument)}>
          <ListItemIcon><ViewIcon /></ListItemIcon>
          <ListItemText>View</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => selectedDocument && handleDocumentAction('download', selectedDocument)}>
          <ListItemIcon><DownloadIcon /></ListItemIcon>
          <ListItemText>Download</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => selectedDocument && handleDocumentAction('share', selectedDocument)}>
          <ListItemIcon><ShareIcon /></ListItemIcon>
          <ListItemText>Share</ListItemText>
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => selectedDocument && handleDocumentAction('delete', selectedDocument)}>
          <ListItemIcon><DeleteIcon color="error" /></ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default DocumentLibrary;
