import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  CardHeader,
  Ty<PERSON>graphy,
  Button,
  Grid,
  LinearProgress,
  Chip,
  Stack,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Avatar,
  Divider
} from '@mui/material';
import {
  Build,
  Warning,
  CheckCircle,
  Timer,
  TrendingUp,
  FlashOn,
  Computer,
  Engineering,
  Speed,
  Refresh,
  Add,
  Visibility,
  Download
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

interface ToolGap {
  id: string;
  toolName: string;
  category: string;
  urgency: 'critical' | 'high' | 'medium' | 'low';
  tendersBlocked: number;
  potentialValue: number;
  licenseType: 'perpetual' | 'subscription' | 'rental';
  costEstimate: number;
  availableProviders: number;
  setupTime: string;
  successRate: number;
}

interface ToolLicense {
  id: string;
  name: string;
  provider: string;
  status: 'active' | 'expiring' | 'expired' | 'pending';
  expiryDate: string;
  cost: number;
  licenseType: string;
  usageLevel: number;
  tenderRelevance: string[];
  lastUsed: string;
}

const ToolSyncDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [toolGaps, setToolGaps] = useState<ToolGap[]>([
    {
      id: 'tool-1',
      toolName: 'AutoCAD Professional 2024',
      category: 'Design & Engineering',
      urgency: 'critical',
      tendersBlocked: 8,
      potentialValue: 28000000,
      licenseType: 'subscription',
      costEstimate: 12000,
      availableProviders: 2,
      setupTime: '2 days',
      successRate: 95
    },
    {
      id: 'tool-2',
      toolName: 'Microsoft Project Professional',
      category: 'Project Management',
      urgency: 'high',
      tendersBlocked: 12,
      potentialValue: 35000000,
      licenseType: 'subscription',
      costEstimate: 8500,
      availableProviders: 4,
      setupTime: '1 day',
      successRate: 87
    },
    {
      id: 'tool-3',
      toolName: 'Primavera P6 Enterprise',
      category: 'Project Management',
      urgency: 'critical',
      tendersBlocked: 6,
      potentialValue: 45000000,
      licenseType: 'perpetual',
      costEstimate: 25000,
      availableProviders: 1,
      setupTime: '1 week',
      successRate: 92
    }
  ]);

  const [toolLicenses, setToolLicenses] = useState<ToolLicense[]>([
    {
      id: 'license-1',
      name: 'Microsoft Office 365 Business',
      provider: 'ToolSync Pro',
      status: 'active',
      expiryDate: '2024-12-31',
      cost: 4500,
      licenseType: 'Annual Subscription',
      usageLevel: 85,
      tenderRelevance: ['All Categories'],
      lastUsed: '2024-01-20'
    },
    {
      id: 'license-2',
      name: 'Adobe Creative Suite',
      provider: 'ToolSync Elite',
      status: 'expiring',
      expiryDate: '2024-02-28',
      cost: 15000,
      licenseType: 'Annual Subscription',
      usageLevel: 45,
      tenderRelevance: ['Marketing', 'Design', 'Presentations'],
      lastUsed: '2024-01-15'
    }
  ]);

  const [dashboardMetrics, setDashboardMetrics] = useState({
    totalToolGaps: 3,
    criticalGaps: 2,
    tendersBlocked: 26,
    potentialValueBlocked: 108000000,
    activeLicenses: 1,
    expiringLicenses: 1,
    toolComplianceScore: 72,
    industryStandard: 'Above Average'
  });

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadToolData();
  }, []);

  const loadToolData = async () => {
    try {
      // In real implementation, fetch from API
      console.log('Loading tool data...');
    } catch (error) {
      console.error('Failed to load tool data:', error);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadToolData();
    setTimeout(() => setRefreshing(false), 1000);
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return '#f44336';
      case 'high': return '#ff9800';
      case 'medium': return '#2196f3';
      case 'low': return '#4caf50';
      default: return '#757575';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'expiring': return 'warning';
      case 'expired': return 'error';
      case 'pending': return 'info';
      default: return 'default';
    }
  };

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `R${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `R${(value / 1000).toFixed(0)}k`;
    return `R${value}`;
  };

  const handleBookTool = (toolId: string) => {
    navigate(`/toolsync/book/${toolId}`);
  };

  const handleViewLicense = (licenseId: string) => {
    navigate(`/toolsync/license/${licenseId}`);
  };

  const handleRenewLicense = (licenseId: string) => {
    navigate(`/toolsync/renew/${licenseId}`);
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
            🛠️ ToolSync Dashboard
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Software tools and license management for tender compliance
          </Typography>
        </Box>
        
        <Stack direction="row" spacing={2}>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => navigate('/toolsync/browse')}
            sx={{
              background: 'linear-gradient(45deg, #FF9800, #FFB74D)',
              fontSize: '1.1rem',
              fontWeight: 'bold'
            }}
          >
            🚀 Browse Tools
          </Button>
          <IconButton onClick={handleRefresh} disabled={refreshing}>
            <Refresh />
          </IconButton>
        </Stack>
      </Box>

      {/* Critical Alert */}
      {dashboardMetrics.criticalGaps > 0 && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="body2" fontWeight="bold">
            🚨 CRITICAL TOOL GAPS: {dashboardMetrics.criticalGaps} missing tools blocking {formatCurrency(dashboardMetrics.potentialValueBlocked)} in tenders!
          </Typography>
          <Typography variant="caption">
            Essential software tools required for tender submissions
          </Typography>
        </Alert>
      )}

      {/* Expiring License Alert */}
      {dashboardMetrics.expiringLicenses > 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="body2" fontWeight="bold">
            ⚠️ LICENSE EXPIRING: {dashboardMetrics.expiringLicenses} license expiring soon - renew to avoid tender disqualification!
          </Typography>
        </Alert>
      )}

      {/* Dashboard Metrics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="error.main" fontWeight="bold">
                {dashboardMetrics.totalToolGaps}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tool Gaps
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main" fontWeight="bold">
                {dashboardMetrics.tendersBlocked}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tenders Blocked
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary.main" fontWeight="bold">
                {dashboardMetrics.toolComplianceScore}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tool Compliance
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="success.main" fontWeight="bold">
                {dashboardMetrics.activeLicenses}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active Licenses
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Critical Tool Gaps */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardHeader 
              title="🚨 Critical Tool Gaps"
              action={
                <Chip 
                  label={`${dashboardMetrics.criticalGaps} Critical`}
                  color="error"
                  size="small"
                />
              }
            />
            <CardContent>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Tool Required</TableCell>
                      <TableCell>Impact</TableCell>
                      <TableCell>Setup</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {toolGaps.map((gap) => (
                      <TableRow key={gap.id} hover>
                        <TableCell>
                          <Box>
                            <Typography variant="body1" fontWeight="bold">
                              {gap.toolName}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {gap.category}
                            </Typography>
                            <Chip 
                              label={gap.urgency.toUpperCase()}
                              size="small"
                              sx={{ 
                                mt: 0.5,
                                backgroundColor: getUrgencyColor(gap.urgency),
                                color: 'white',
                                fontWeight: 'bold'
                              }}
                            />
                          </Box>
                        </TableCell>
                        
                        <TableCell>
                          <Typography variant="body2" fontWeight="bold" color="error.main">
                            {gap.tendersBlocked} tenders blocked
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {formatCurrency(gap.potentialValue)} at risk
                          </Typography>
                          <Typography variant="caption" color="success.main">
                            {gap.successRate}% success rate
                          </Typography>
                        </TableCell>
                        
                        <TableCell>
                          <Typography variant="body2" fontWeight="bold">
                            {gap.setupTime}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {formatCurrency(gap.costEstimate)}
                          </Typography>
                          <Typography variant="caption">
                            {gap.licenseType}
                          </Typography>
                        </TableCell>
                        
                        <TableCell>
                          <Stack direction="row" spacing={1}>
                            <Button
                              variant="contained"
                              size="small"
                              startIcon={<FlashOn />}
                              onClick={() => handleBookTool(gap.id)}
                              sx={{
                                backgroundColor: getUrgencyColor(gap.urgency),
                                '&:hover': {
                                  backgroundColor: getUrgencyColor(gap.urgency),
                                  opacity: 0.8
                                }
                              }}
                            >
                              {gap.urgency === 'critical' ? 'URGENT' : 'GET TOOL'}
                            </Button>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Current Tool Licenses */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader title="💿 Current Tool Licenses" />
            <CardContent>
              <Stack spacing={2}>
                {toolLicenses.map((license) => (
                  <Card key={license.id} variant="outlined">
                    <CardContent sx={{ p: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                        <Typography variant="body1" fontWeight="bold">
                          {license.name}
                        </Typography>
                        <Chip 
                          label={license.status.toUpperCase()}
                          size="small"
                          color={getStatusColor(license.status) as any}
                        />
                      </Box>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        {license.provider} • {license.licenseType}
                      </Typography>
                      
                      <Box sx={{ mb: 1 }}>
                        <Typography variant="caption" color="text.secondary">
                          Usage Level
                        </Typography>
                        <LinearProgress 
                          variant="determinate" 
                          value={license.usageLevel}
                          sx={{ height: 6, borderRadius: 3 }}
                        />
                        <Typography variant="caption" color="text.secondary">
                          {license.usageLevel}% utilized
                        </Typography>
                      </Box>
                      
                      <Typography variant="caption" color="text.secondary">
                        Expires: {license.expiryDate} • Cost: {formatCurrency(license.cost)}
                      </Typography>
                      
                      <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
                        <Button
                          variant="outlined"
                          size="small"
                          startIcon={<Visibility />}
                          onClick={() => handleViewLicense(license.id)}
                          sx={{ flex: 1 }}
                        >
                          View
                        </Button>
                        {license.status === 'expiring' && (
                          <Button
                            variant="contained"
                            size="small"
                            color="warning"
                            onClick={() => handleRenewLicense(license.id)}
                            sx={{ flex: 1 }}
                          >
                            Renew
                          </Button>
                        )}
                      </Stack>
                    </CardContent>
                  </Card>
                ))}
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Success Alert */}
      <Alert severity="success" sx={{ mt: 3 }}>
        <Typography variant="body2" fontWeight="bold">
          🎯 TOOLSYNC SUCCESS: Bidders with complete tool suites win 58% more tenders!
        </Typography>
        <Typography variant="caption">
          Professional software tools are essential for competitive tender submissions
        </Typography>
      </Alert>
    </Box>
  );
};

export default ToolSyncDashboard;
