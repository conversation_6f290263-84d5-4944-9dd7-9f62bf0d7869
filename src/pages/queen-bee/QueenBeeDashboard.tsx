/**
 * Queen Bee Dashboard
 * Management interface for Queen Bees to oversee worker bees and coordinate tasks
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  Button,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Rating,
  Alert,
  LinearProgress,
  Divider,
  Stack,
  Badge
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Assignment as TaskIcon,
  Star as StarIcon,
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Send as SendIcon
} from '@mui/icons-material';
import QueenBeeManagementSystem, { 
  Queen<PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>T<PERSON>, 
  <PERSON>TaskStatus,
  BeeType 
} from '../../services/QueenBeeManagementSystem';
import BeeTrackingMap from '../../components/maps/BeeTrackingMap';

interface QueenBeeDashboardProps {
  queenBeeId: string;
}

const QueenBeeDashboard: React.FC<QueenBeeDashboardProps> = ({ queenBeeId }) => {
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [selectedWorkerBee, setSelectedWorkerBee] = useState<WorkerBee | null>(null);
  const [selectedTask, setSelectedTask] = useState<BeeTask | null>(null);
  const [assignTaskDialog, setAssignTaskDialog] = useState(false);
  const [qualityCheckDialog, setQualityCheckDialog] = useState(false);
  const [qualityScore, setQualityScore] = useState(5);
  const [qualityFeedback, setQualityFeedback] = useState('');

  const queenBeeSystem = QueenBeeManagementSystem.getInstance();

  useEffect(() => {
    loadDashboardData();
    
    // Refresh data every 30 seconds
    const interval = setInterval(loadDashboardData, 30000);
    return () => clearInterval(interval);
  }, [queenBeeId]);

  const loadDashboardData = () => {
    const data = queenBeeSystem.getQueenBeeDashboard(queenBeeId);
    setDashboardData(data);
  };

  const handleAssignTask = async (taskId: string, workerBeeId: string) => {
    try {
      await queenBeeSystem.queenBeeAssignToWorker(queenBeeId, taskId, workerBeeId);
      setAssignTaskDialog(false);
      loadDashboardData();
    } catch (error) {
      console.error('Failed to assign task:', error);
    }
  };

  const handleQualityCheck = async (approved: boolean) => {
    if (!selectedTask) return;

    try {
      await queenBeeSystem.queenBeeQualityCheck(
        queenBeeId,
        selectedTask.id,
        qualityScore,
        qualityFeedback,
        approved
      );
      setQualityCheckDialog(false);
      setQualityScore(5);
      setQualityFeedback('');
      loadDashboardData();
    } catch (error) {
      console.error('Failed to complete quality check:', error);
    }
  };

  const getBeeTypeIcon = (beeType: BeeType) => {
    switch (beeType) {
      case BeeType.QUEEN_BEE: return '👑';
      case BeeType.SENIOR_BEE: return '⭐';
      case BeeType.SPECIALIST_BEE: return '🎯';
      case BeeType.WORKER_BEE: return '🐝';
      case BeeType.TRAINEE_BEE: return '📚';
      default: return '🐝';
    }
  };

  const getTaskStatusColor = (status: BeeTaskStatus) => {
    switch (status) {
      case BeeTaskStatus.COMPLETED: return 'success';
      case BeeTaskStatus.IN_PROGRESS: return 'primary';
      case BeeTaskStatus.QUALITY_CHECK: return 'warning';
      case BeeTaskStatus.REVISION_REQUIRED: return 'error';
      default: return 'default';
    }
  };

  if (!dashboardData) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>Loading Queen Bee Dashboard...</Typography>
        <LinearProgress sx={{ mt: 2 }} />
      </Box>
    );
  }

  const { queenBee, managedWorkerBees, activeTasks, metrics } = dashboardData;

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          👑 Queen Bee Command Center
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Welcome back, {queenBee.fullName} | Territory: {queenBee.territory.name}
        </Typography>
      </Box>

      {/* Metrics Overview */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <PeopleIcon color="primary" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h4">{metrics.totalWorkerBees}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Managed Bees
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TaskIcon color="warning" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h4">{metrics.activeTasks}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Tasks
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CheckIcon color="success" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h4">{metrics.completedToday}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Completed Today
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon color="info" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h4">{metrics.teamProductivity.toFixed(0)}%</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Team Productivity
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Worker Bees Management */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                <PeopleIcon sx={{ mr: 1 }} />
                Your Worker Bees ({managedWorkerBees.length})
              </Typography>

              <List>
                {managedWorkerBees.map((bee: WorkerBee) => (
                  <ListItem key={bee.id} divider>
                    <ListItemAvatar>
                      <Badge
                        badgeContent={getBeeTypeIcon(bee.beeType)}
                        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                      >
                        <Avatar>
                          {bee.fullName.charAt(0)}
                        </Avatar>
                      </Badge>
                    </ListItemAvatar>
                    
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="subtitle1">{bee.fullName}</Typography>
                          <Chip
                            label={bee.availability.status.toUpperCase()}
                            size="small"
                            color={bee.availability.status === 'available' ? 'success' : 'warning'}
                          />
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2">
                            {bee.beeId} | {bee.skillLevel} | ⭐ {bee.rating.toFixed(1)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {bee.tasksCompleted} tasks completed | {bee.successRate.toFixed(0)}% success rate
                          </Typography>
                          {bee.currentTask && (
                            <Typography variant="caption" color="primary">
                              Current: {bee.currentTask.title}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                    
                    <ListItemSecondaryAction>
                      <IconButton onClick={() => setSelectedWorkerBee(bee)}>
                        <ViewIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Active Tasks */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                <TaskIcon sx={{ mr: 1 }} />
                Active Tasks ({activeTasks.length})
              </Typography>

              <List>
                {activeTasks.map((task: BeeTask) => (
                  <ListItem key={task.id} divider>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="subtitle1">{task.title}</Typography>
                          <Chip
                            label={task.status.replace('_', ' ').toUpperCase()}
                            size="small"
                            color={getTaskStatusColor(task.status)}
                          />
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2">
                            {task.taskType.replace('_', ' ')} | Priority: {task.priority}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Assigned to: {task.assignedWorkerBeeId ? 
                              managedWorkerBees.find(bee => bee.id === task.assignedWorkerBeeId)?.fullName || 'Unknown' : 
                              'Unassigned'
                            }
                          </Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={task.progress} 
                            sx={{ mt: 1, height: 6, borderRadius: 3 }}
                          />
                        </Box>
                      }
                    />
                    
                    <ListItemSecondaryAction>
                      <Stack direction="row" spacing={1}>
                        {task.status === BeeTaskStatus.ASSIGNED && (
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => {
                              setSelectedTask(task);
                              setAssignTaskDialog(true);
                            }}
                          >
                            Assign
                          </Button>
                        )}
                        {task.status === BeeTaskStatus.QUALITY_CHECK && (
                          <Button
                            size="small"
                            variant="contained"
                            color="warning"
                            onClick={() => {
                              setSelectedTask(task);
                              setQualityCheckDialog(true);
                            }}
                          >
                            Review
                          </Button>
                        )}
                        <IconButton onClick={() => setSelectedTask(task)}>
                          <ViewIcon />
                        </IconButton>
                      </Stack>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>

              {metrics.pendingQualityChecks > 0 && (
                <Alert severity="warning" sx={{ mt: 2 }}>
                  <Typography variant="body2">
                    ⚠️ {metrics.pendingQualityChecks} tasks pending quality check
                  </Typography>
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Bee Tracking Map */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                <LocationIcon sx={{ mr: 1 }} />
                Real-time Bee Tracking
              </Typography>
              
              <BeeTrackingMap
                bees={managedWorkerBees.map(bee => ({
                  id: bee.id,
                  beeId: bee.beeId,
                  fullName: bee.fullName,
                  phoneNumber: bee.phoneNumber,
                  email: bee.email,
                  location: {
                    latitude: bee.currentLocation.latitude,
                    longitude: bee.currentLocation.longitude,
                    address: bee.currentLocation.address,
                    lastUpdated: new Date(bee.currentLocation.lastUpdated)
                  },
                  currentTask: bee.currentTask ? {
                    id: bee.currentTask.id,
                    title: bee.currentTask.title,
                    status: bee.currentTask.status,
                    progress: bee.currentTask.progress
                  } : undefined,
                  rating: bee.rating,
                  isActive: bee.isActive
                }))}
                height="400px"
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Assign Task Dialog */}
      <Dialog open={assignTaskDialog} onClose={() => setAssignTaskDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Assign Task to Worker Bee</DialogTitle>
        <DialogContent>
          {selectedTask && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6">{selectedTask.title}</Typography>
              <Typography variant="body2" color="text.secondary">
                {selectedTask.description}
              </Typography>
            </Box>
          )}
          
          <Typography variant="subtitle1" sx={{ mb: 1 }}>Available Worker Bees:</Typography>
          <List>
            {managedWorkerBees
              .filter(bee => bee.availability.status === 'available')
              .map((bee: WorkerBee) => (
                <ListItem 
                  key={bee.id} 
                  button 
                  onClick={() => selectedTask && handleAssignTask(selectedTask.id, bee.id)}
                >
                  <ListItemAvatar>
                    <Avatar>{bee.fullName.charAt(0)}</Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={bee.fullName}
                    secondary={`${bee.skillLevel} | ⭐ ${bee.rating.toFixed(1)} | ${bee.specialties.join(', ')}`}
                  />
                </ListItem>
              ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAssignTaskDialog(false)}>Cancel</Button>
        </DialogActions>
      </Dialog>

      {/* Quality Check Dialog */}
      <Dialog open={qualityCheckDialog} onClose={() => setQualityCheckDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Quality Check</DialogTitle>
        <DialogContent>
          {selectedTask && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6">{selectedTask.title}</Typography>
              <Typography variant="body2" color="text.secondary">
                Completed by: {selectedTask.assignedWorkerBeeId ? 
                  managedWorkerBees.find(bee => bee.id === selectedTask.assignedWorkerBeeId)?.fullName : 
                  'Unknown'
                }
              </Typography>
            </Box>
          )}

          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" sx={{ mb: 1 }}>Quality Score:</Typography>
            <Rating
              value={qualityScore}
              onChange={(event, newValue) => setQualityScore(newValue || 5)}
              size="large"
            />
          </Box>

          <TextField
            fullWidth
            multiline
            rows={4}
            label="Feedback"
            value={qualityFeedback}
            onChange={(e) => setQualityFeedback(e.target.value)}
            placeholder="Provide feedback on the completed task..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setQualityCheckDialog(false)}>Cancel</Button>
          <Button 
            onClick={() => handleQualityCheck(false)} 
            color="warning"
            variant="outlined"
          >
            Request Revision
          </Button>
          <Button 
            onClick={() => handleQualityCheck(true)} 
            color="success"
            variant="contained"
          >
            Approve
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default QueenBeeDashboard;
