/**
 * Supplier Main Dashboard Page
 * Main dashboard for supplier revenue and management features
 */

import React from 'react';
import { Box, Typography, Alert, Grid, Card, CardContent } from '@mui/material';
import { Store, TrendingUp, MonetizationOn, EmojiEvents } from '@mui/icons-material';

const SupplierMainDashboard: React.FC = () => {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main', mb: 1 }}>
        🏢 Supplier Dashboard
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Manage your supplier revenue and performance
      </Typography>
      
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <MonetizationOn color="success" />
                <Typography variant="h6">Revenue</Typography>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 600 }}>
                R2.5M
              </Typography>
              <Typography variant="body2" color="text.secondary">
                This quarter
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <TrendingUp color="info" />
                <Typography variant="h6">Growth</Typography>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 600 }}>
                +23%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                vs last quarter
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <Store color="primary" />
                <Typography variant="h6">Active Quotes</Typography>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 600 }}>
                47
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Pending responses
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <EmojiEvents color="warning" />
                <Typography variant="h6">Success Rate</Typography>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 600 }}>
                68%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Quote conversion
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      <Alert severity="info">
        <Typography variant="subtitle2">Supplier Dashboard</Typography>
        <Typography variant="body2">
          Full supplier dashboard with quote management, revenue tracking, and performance analytics is under development.
        </Typography>
      </Alert>
    </Box>
  );
};

export default SupplierMainDashboard;
