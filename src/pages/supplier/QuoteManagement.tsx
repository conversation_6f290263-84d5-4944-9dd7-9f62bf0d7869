/**
 * Quote Management Page
 * Manage supplier quotes and responses
 */

import React from 'react';
import { Box, Typography, Alert } from '@mui/material';

const QuoteManagement: React.FC = () => {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main', mb: 1 }}>
        📋 Quote Management
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Manage your supplier quotes and bid responses
      </Typography>
      
      <Alert severity="info">
        <Typography variant="subtitle2">Coming Soon</Typography>
        <Typography variant="body2">
          Quote management interface is under development.
        </Typography>
      </Alert>
    </Box>
  );
};

export default QuoteManagement;
