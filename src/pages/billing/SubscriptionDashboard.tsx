/**
 * Subscription Dashboard
 * Comprehensive subscription management with NeuroMarketing optimization
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  CreditCard as CreditCardIcon,
  Receipt as ReceiptIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Upgrade as UpgradeIcon,
  Psychology as PsychologyIcon,
  Help as HelpIcon,
  Star as StarIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useNeuroMarketing, useComplianceOptimization } from '../../hooks/useNeuroMarketing';
import {
  useGetUserSubscriptionQuery,
  useGetUsageMetricsQuery,
  useGetFeatureUsageQuery,
  useCancelSubscriptionMutation,
  useReactivateSubscriptionMutation
} from '../../services/api/subscription.api';
import { useAuth } from '../../contexts/AuthContext';
import AdaptiveInterface from '../../components/adaptive/AdaptiveInterface';
import {
  UserSubscription,
  SubscriptionStatus,
  SubscriptionTier,
  UsageMetrics,
  FeatureUsage
} from '../../types/subscription';

const SubscriptionDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [upgradeDialogOpen, setUpgradeDialogOpen] = useState(false);

  // NeuroMarketing optimization
  const {
    psychologicalState,
    isStressed,
    needsSimplification,
    shouldShowHelp,
    shouldShowEncouragement
  } = useNeuroMarketing();

  const {
    protestUIMode,
    stressLevel,
    showEncouragement,
    showCostWarnings,
    emphasizeSupport
  } = useComplianceOptimization();

  // API queries
  const {
    data: subscription,
    isLoading: subscriptionLoading,
    error: subscriptionError
  } = useGetUserSubscriptionQuery(user?.id || '');

  const {
    data: usageMetrics,
    isLoading: usageLoading
  } = useGetUsageMetricsQuery({
    userId: user?.id || '',
    period: 'current'
  });

  const {
    data: featureUsage,
    isLoading: featureLoading
  } = useGetFeatureUsageQuery(user?.id || '');

  const [cancelSubscription] = useCancelSubscriptionMutation();
  const [reactivateSubscription] = useReactivateSubscriptionMutation();

  // Psychological adaptations
  const getCardElevation = () => {
    return isStressed ? 1 : 3;
  };

  const getButtonSize = () => {
    return psychologicalState.cognitiveLoad > 0.7 ? 'large' : 'medium';
  };

  const getStatusColor = (status: SubscriptionStatus) => {
    switch (status) {
      case SubscriptionStatus.ACTIVE:
        return 'success';
      case SubscriptionStatus.TRIALING:
        return 'info';
      case SubscriptionStatus.PAST_DUE:
        return 'warning';
      case SubscriptionStatus.CANCELLED:
        return 'error';
      default:
        return 'default';
    }
  };

  const getTierColor = (tier: SubscriptionTier) => {
    switch (tier) {
      case SubscriptionTier.FREE:
        return 'default';
      case SubscriptionTier.BASIC:
        return 'primary';
      case SubscriptionTier.PROFESSIONAL:
        return 'secondary';
      case SubscriptionTier.ENTERPRISE:
        return 'warning';
      case SubscriptionTier.COMPLIANCE_PRO:
        return 'success';
      default:
        return 'default';
    }
  };

  const handleCancelSubscription = async () => {
    if (!subscription) return;

    try {
      await cancelSubscription({
        subscriptionId: subscription.id,
        cancelAtPeriodEnd: true,
        reason: 'User requested cancellation'
      }).unwrap();
      setCancelDialogOpen(false);
    } catch (error) {
      console.error('Failed to cancel subscription:', error);
    }
  };

  const handleReactivateSubscription = async () => {
    if (!subscription) return;

    try {
      await reactivateSubscription(subscription.id).unwrap();
    } catch (error) {
      console.error('Failed to reactivate subscription:', error);
    }
  };

  const renderSubscriptionStatus = () => {
    if (!subscription) return null;

    return (
      <Card elevation={getCardElevation()}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
            <Box>
              <Typography variant="h6" sx={{ mb: 1 }}>
                Current Plan
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <Chip
                  label={subscription.tier.toUpperCase()}
                  color={getTierColor(subscription.tier)}
                  variant="filled"
                />
                <Chip
                  label={subscription.status.toUpperCase()}
                  color={getStatusColor(subscription.status)}
                  variant="outlined"
                  size="small"
                />
              </Box>
            </Box>

            {subscription.tier === SubscriptionTier.COMPLIANCE_PRO && (
              <Tooltip title="Compliance Pro includes full SA legal compliance features">
                <SecurityIcon color="success" />
              </Tooltip>
            )}
          </Box>

          {showEncouragement && subscription.tier !== SubscriptionTier.FREE && (
            <Alert severity="success" sx={{ mb: 2 }}>
              🎉 Great choice! You're maximizing your tender success potential.
            </Alert>
          )}

          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Billing Cycle
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                {subscription.billingCycle.charAt(0).toUpperCase() + subscription.billingCycle.slice(1)}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Next Billing Date
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                {new Date(subscription.nextBillingDate).toLocaleDateString()}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Next Payment
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                R{subscription.nextPaymentAmount.toFixed(2)}
              </Typography>
            </Grid>

            {subscription.complianceEnabled && (
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Compliance Features
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <CheckCircleIcon color="success" fontSize="small" />
                  <Typography variant="body1" color="success.main">
                    Enabled
                  </Typography>
                </Box>
              </Grid>
            )}
          </Grid>

          <Box sx={{ mt: 3, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {subscription.status === SubscriptionStatus.ACTIVE && (
              <>
                <Button
                  variant="outlined"
                  startIcon={<UpgradeIcon />}
                  onClick={() => setUpgradeDialogOpen(true)}
                  size={getButtonSize()}
                >
                  Upgrade Plan
                </Button>
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<CancelIcon />}
                  onClick={() => setCancelDialogOpen(true)}
                  size={getButtonSize()}
                >
                  Cancel Plan
                </Button>
              </>
            )}

            {subscription.status === SubscriptionStatus.CANCELLED && (
              <Button
                variant="contained"
                startIcon={<CheckCircleIcon />}
                onClick={handleReactivateSubscription}
                size={getButtonSize()}
              >
                Reactivate Plan
              </Button>
            )}

            <Button
              variant="outlined"
              startIcon={<CreditCardIcon />}
              onClick={() => navigate('/billing/payment-methods')}
              size={getButtonSize()}
            >
              Payment Methods
            </Button>

            <Button
              variant="outlined"
              startIcon={<ReceiptIcon />}
              onClick={() => navigate('/billing/invoices')}
              size={getButtonSize()}
            >
              Billing History
            </Button>
          </Box>
        </CardContent>
      </Card>
    );
  };

  const renderUsageMetrics = () => {
    if (!usageMetrics || !featureUsage) return null;

    return (
      <Card elevation={getCardElevation()}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Usage This Month
          </Typography>

          {featureUsage.map((feature, index) => (
            <Box key={index} sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="body2">
                  {feature.feature.replace('_', ' ').toUpperCase()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {feature.unlimited ? 'Unlimited' : `${feature.used}/${feature.limit}`}
                </Typography>
              </Box>
              
              {!feature.unlimited && (
                <LinearProgress
                  variant="determinate"
                  value={feature.percentage}
                  color={feature.percentage > 80 ? 'warning' : 'primary'}
                  sx={{ height: 6, borderRadius: 3 }}
                />
              )}

              {feature.percentage > 90 && !feature.unlimited && (
                <Alert severity="warning" sx={{ mt: 1 }}>
                  <Typography variant="caption">
                    You're approaching your {feature.feature} limit. Consider upgrading your plan.
                  </Typography>
                </Alert>
              )}
            </Box>
          ))}

          {showCostWarnings && (
            <Alert severity="info" sx={{ mt: 2 }}>
              💡 Monitor your usage to avoid overage charges. Upgrade for unlimited access.
            </Alert>
          )}
        </CardContent>
      </Card>
    );
  };

  const renderComplianceFeatures = () => {
    if (!subscription?.complianceEnabled) return null;

    return (
      <Card elevation={getCardElevation()}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <SecurityIcon color="success" sx={{ mr: 1 }} />
            <Typography variant="h6">
              SA Compliance Features
            </Typography>
            {shouldShowHelp && (
              <Tooltip title="Professional legal compliance tools for SA tenders">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon />
                </IconButton>
              </Tooltip>
            )}
          </Box>

          <List>
            <ListItem>
              <ListItemIcon>
                <CheckCircleIcon color="success" />
              </ListItemIcon>
              <ListItemText
                primary="Bid Protest Management"
                secondary="Professional protest letters and legal templates"
              />
            </ListItem>

            <ListItem>
              <ListItemIcon>
                <CheckCircleIcon color="success" />
              </ListItemIcon>
              <ListItemText
                primary="SME Analysis & Benchmarking"
                secondary="Industry comparison and improvement recommendations"
              />
            </ListItem>

            <ListItem>
              <ListItemIcon>
                <CheckCircleIcon color="success" />
              </ListItemIcon>
              <ListItemText
                primary="Legal Framework Integration"
                secondary="PFMA, PPPFA, and Municipal Systems Act compliance"
              />
            </ListItem>

            <ListItem>
              <ListItemIcon>
                <CheckCircleIcon color="success" />
              </ListItemIcon>
              <ListItemText
                primary="Deadline Tracking"
                secondary="Automatic legal deadline calculations"
              />
            </ListItem>
          </List>

          {subscription.complianceUsage && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Compliance Usage This Month:
              </Typography>
              <Grid container spacing={1}>
                <Grid item xs={6}>
                  <Typography variant="caption" color="text.secondary">
                    Protests Generated
                  </Typography>
                  <Typography variant="body2">
                    {subscription.complianceUsage.protestsGenerated}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="caption" color="text.secondary">
                    Templates Used
                  </Typography>
                  <Typography variant="body2">
                    {subscription.complianceUsage.templatesUsed}
                  </Typography>
                </Grid>
              </Grid>
            </Box>
          )}
        </CardContent>
      </Card>
    );
  };

  if (subscriptionLoading || usageLoading || featureLoading) {
    return (
      <AdaptiveInterface>
        <Box sx={{ p: 3 }}>
          <LinearProgress />
          <Typography variant="h6" sx={{ mt: 2, textAlign: 'center' }}>
            Loading your subscription details...
          </Typography>
        </Box>
      </AdaptiveInterface>
    );
  }

  return (
    <AdaptiveInterface>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography 
              variant={needsSimplification ? 'h5' : 'h4'} 
              component="h1"
              sx={{ fontWeight: isStressed ? 400 : 500 }}
            >
              Subscription & Billing
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Manage your subscription, usage, and billing preferences
            </Typography>
          </Box>

          {shouldShowHelp && (
            <Tooltip title="Get help with billing and subscriptions">
              <IconButton 
                color="primary" 
                onClick={() => navigate('/help/billing')}
                size={getButtonSize()}
              >
                <HelpIcon />
              </IconButton>
            </Tooltip>
          )}
        </Box>

        {/* Stress level indicator */}
        {stressLevel === 'high' && (
          <Alert 
            severity="info" 
            sx={{ mb: 3 }}
            icon={<PsychologyIcon />}
          >
            We've simplified your billing dashboard. All your subscription details are clearly organized below.
          </Alert>
        )}

        {/* Main Content */}
        <Grid container spacing={3}>
          {/* Left Column */}
          <Grid item xs={12} md={8}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                {renderSubscriptionStatus()}
              </Grid>

              <Grid item xs={12}>
                {renderUsageMetrics()}
              </Grid>
            </Grid>
          </Grid>

          {/* Right Column */}
          <Grid item xs={12} md={4}>
            <Grid container spacing={3}>
              {subscription?.complianceEnabled && (
                <Grid item xs={12}>
                  {renderComplianceFeatures()}
                </Grid>
              )}

              <Grid item xs={12}>
                <Card elevation={getCardElevation()}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      Quick Actions
                    </Typography>
                    
                    <List>
                      <ListItem button onClick={() => navigate('/billing/plans')}>
                        <ListItemIcon>
                          <UpgradeIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText primary="View All Plans" />
                      </ListItem>

                      <ListItem button onClick={() => navigate('/billing/usage')}>
                        <ListItemIcon>
                          <TrendingUpIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText primary="Detailed Usage" />
                      </ListItem>

                      <ListItem button onClick={() => navigate('/compliance')}>
                        <ListItemIcon>
                          <SecurityIcon color="success" />
                        </ListItemIcon>
                        <ListItemText primary="Compliance Tools" />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Grid>
        </Grid>

        {/* Cancel Subscription Dialog */}
        <Dialog
          open={cancelDialogOpen}
          onClose={() => setCancelDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Cancel Subscription</DialogTitle>
          <DialogContent>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Are you sure you want to cancel your subscription? You'll lose access to premium features at the end of your current billing period.
            </Typography>
            {emphasizeSupport && (
              <Alert severity="info">
                💬 Need help? Contact our support team before cancelling - we're here to help!
              </Alert>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCancelDialogOpen(false)}>
              Keep Subscription
            </Button>
            <Button 
              onClick={handleCancelSubscription} 
              color="error"
              variant="contained"
            >
              Cancel Subscription
            </Button>
          </DialogActions>
        </Dialog>

        {/* Upgrade Dialog */}
        <Dialog
          open={upgradeDialogOpen}
          onClose={() => setUpgradeDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Upgrade Your Plan</DialogTitle>
          <DialogContent>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Choose a plan that better fits your needs:
            </Typography>
            {/* Plan comparison would go here */}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setUpgradeDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={() => navigate('/billing/plans')}
              variant="contained"
            >
              View Plans
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </AdaptiveInterface>
  );
};

export default SubscriptionDashboard;
