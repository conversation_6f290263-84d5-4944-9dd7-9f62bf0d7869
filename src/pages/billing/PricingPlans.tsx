/**
 * Pricing Plans Page
 * Psychologically optimized pricing display with SA Compliance focus
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Switch,
  FormControlLabel,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip,
  IconButton,
  Badge
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Star as StarIcon,
  Security as SecurityIcon,
  Business as BusinessIcon,
  TrendingUp as TrendingUpIcon,
  Psychology as PsychologyIcon,
  Help as HelpIcon,
  Verified as VerifiedIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useNeuroMarketing, useComplianceOptimization } from '../../hooks/useNeuroMarketing';
import {
  useGetSubscriptionPlansQuery,
  useGetUserSubscriptionQuery,
  useCreateSubscriptionMutation
} from '../../services/api/subscription.api';
import { useAuth } from '../../contexts/AuthContext';
import AdaptiveInterface from '../../components/adaptive/AdaptiveInterface';
import {
  SubscriptionPlan,
  SubscriptionTier,
  CreateSubscriptionRequest
} from '../../types/subscription';

const PricingPlans: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [yearlyBilling, setYearlyBilling] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [upgradeDialogOpen, setUpgradeDialogOpen] = useState(false);

  // NeuroMarketing optimization
  const {
    psychologicalState,
    isStressed,
    needsSimplification,
    shouldShowHelp,
    shouldShowEncouragement
  } = useNeuroMarketing();

  const {
    protestUIMode,
    stressLevel,
    showEncouragement,
    showCostWarnings,
    emphasizeSupport
  } = useComplianceOptimization();

  // API queries
  const {
    data: plans,
    isLoading: plansLoading
  } = useGetSubscriptionPlansQuery();

  const {
    data: currentSubscription
  } = useGetUserSubscriptionQuery(user?.id || '');

  const [createSubscription] = useCreateSubscriptionMutation();

  // Psychological adaptations
  const getCardElevation = () => {
    return isStressed ? 2 : 4;
  };

  const getButtonSize = () => {
    return psychologicalState.cognitiveLoad > 0.7 ? 'large' : 'medium';
  };

  const getPlanRecommendation = (plan: SubscriptionPlan) => {
    if (plan.tier === SubscriptionTier.COMPLIANCE_PRO) {
      return 'Recommended for SA Tenders';
    }
    if (plan.tier === SubscriptionTier.PROFESSIONAL) {
      return 'Most Popular';
    }
    if (plan.tier === SubscriptionTier.BASIC && user?.isSME) {
      return 'Great for SMEs';
    }
    return null;
  };

  const getPrice = (plan: SubscriptionPlan) => {
    return yearlyBilling ? plan.price.yearly : plan.price.monthly;
  };

  const getSavings = (plan: SubscriptionPlan) => {
    if (!yearlyBilling) return 0;
    const monthlyTotal = plan.price.monthly * 12;
    const yearlyPrice = plan.price.yearly;
    return ((monthlyTotal - yearlyPrice) / monthlyTotal * 100).toFixed(0);
  };

  const handleSelectPlan = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
    setUpgradeDialogOpen(true);
  };

  const handleUpgrade = async () => {
    if (!selectedPlan || !user) return;

    try {
      const subscriptionRequest: CreateSubscriptionRequest = {
        planId: selectedPlan.id,
        billingCycle: yearlyBilling ? 'yearly' : 'monthly',
        paymentMethodId: '', // Would be selected in payment flow
        trialDays: selectedPlan.tier === SubscriptionTier.FREE ? 0 : 14
      };

      // Navigate to payment flow
      navigate('/billing/checkout', { 
        state: { 
          plan: selectedPlan, 
          billingCycle: yearlyBilling ? 'yearly' : 'monthly' 
        } 
      });
    } catch (error) {
      console.error('Failed to create subscription:', error);
    }
  };

  const renderPlanCard = (plan: SubscriptionPlan) => {
    const isCurrentPlan = currentSubscription?.planId === plan.id;
    const recommendation = getPlanRecommendation(plan);
    const price = getPrice(plan);
    const savings = getSavings(plan);

    return (
      <Card
        key={plan.id}
        elevation={getCardElevation()}
        sx={{
          position: 'relative',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          border: plan.popular ? 2 : 1,
          borderColor: plan.popular ? 'primary.main' : 'divider',
          transform: plan.popular && !isStressed ? 'scale(1.05)' : 'none',
          transition: 'transform 0.2s ease-in-out'
        }}
      >
        {/* Popular Badge */}
        {plan.popular && (
          <Box
            sx={{
              position: 'absolute',
              top: -10,
              left: '50%',
              transform: 'translateX(-50%)',
              zIndex: 1
            }}
          >
            <Chip
              label="Most Popular"
              color="primary"
              size="small"
              icon={<StarIcon />}
            />
          </Box>
        )}

        {/* Recommendation Badge */}
        {recommendation && (
          <Box
            sx={{
              position: 'absolute',
              top: plan.popular ? 20 : -10,
              right: -10,
              zIndex: 1
            }}
          >
            <Chip
              label={recommendation}
              color={plan.tier === SubscriptionTier.COMPLIANCE_PRO ? 'success' : 'secondary'}
              size="small"
              icon={plan.tier === SubscriptionTier.COMPLIANCE_PRO ? <SecurityIcon /> : <TrendingUpIcon />}
            />
          </Box>
        )}

        <CardContent sx={{ flexGrow: 1, pt: plan.popular ? 4 : 2 }}>
          {/* Plan Header */}
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1 }}>
              {plan.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {plan.description}
            </Typography>

            {/* Price */}
            <Box sx={{ mb: 1 }}>
              <Typography variant="h3" component="span" sx={{ fontWeight: 'bold' }}>
                R{price}
              </Typography>
              <Typography variant="body1" component="span" color="text.secondary">
                /{yearlyBilling ? 'year' : 'month'}
              </Typography>
            </Box>

            {/* Savings */}
            {yearlyBilling && savings !== '0' && (
              <Chip
                label={`Save ${savings}%`}
                color="success"
                size="small"
                variant="outlined"
              />
            )}
          </Box>

          {/* Features */}
          <List dense>
            {plan.features.slice(0, needsSimplification ? 5 : 8).map((feature, index) => (
              <ListItem key={index} sx={{ px: 0 }}>
                <ListItemIcon sx={{ minWidth: 32 }}>
                  {feature.included ? (
                    <CheckCircleIcon color="success" fontSize="small" />
                  ) : (
                    <CancelIcon color="disabled" fontSize="small" />
                  )}
                </ListItemIcon>
                <ListItemText
                  primary={feature.name}
                  secondary={feature.limit ? `Up to ${feature.limit}` : undefined}
                  primaryTypographyProps={{
                    variant: 'body2',
                    color: feature.included ? 'text.primary' : 'text.disabled'
                  }}
                />
                {feature.premium && (
                  <Tooltip title="Premium feature">
                    <VerifiedIcon color="primary" fontSize="small" />
                  </Tooltip>
                )}
              </ListItem>
            ))}
          </List>

          {/* Compliance Features Highlight */}
          {plan.complianceIncluded && (
            <Alert severity="success" sx={{ mt: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <SecurityIcon fontSize="small" />
                <Typography variant="body2">
                  Full SA Compliance Tools Included
                </Typography>
              </Box>
            </Alert>
          )}

          {/* SME Focus */}
          {plan.businessFocused && user?.isSME && (
            <Alert severity="info" sx={{ mt: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <BusinessIcon fontSize="small" />
                <Typography variant="body2">
                  Designed for SMEs like yours
                </Typography>
              </Box>
            </Alert>
          )}
        </CardContent>

        <CardActions sx={{ p: 2, pt: 0 }}>
          {isCurrentPlan ? (
            <Button
              fullWidth
              variant="outlined"
              disabled
              size={getButtonSize()}
              startIcon={<CheckCircleIcon />}
            >
              Current Plan
            </Button>
          ) : (
            <Button
              fullWidth
              variant={plan.popular ? 'contained' : 'outlined'}
              color={plan.tier === SubscriptionTier.COMPLIANCE_PRO ? 'success' : 'primary'}
              onClick={() => handleSelectPlan(plan)}
              size={getButtonSize()}
            >
              {plan.tier === SubscriptionTier.FREE ? 'Get Started' : 'Upgrade Now'}
            </Button>
          )}
        </CardActions>
      </Card>
    );
  };

  if (plansLoading) {
    return (
      <AdaptiveInterface>
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6">Loading pricing plans...</Typography>
        </Box>
      </AdaptiveInterface>
    );
  }

  return (
    <AdaptiveInterface>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography 
            variant={needsSimplification ? 'h4' : 'h3'} 
            component="h1"
            sx={{ fontWeight: 'bold', mb: 2 }}
          >
            Choose Your Plan
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
            Unlock the full potential of SA tender management
          </Typography>

          {showEncouragement && (
            <Alert severity="success" sx={{ mb: 3, maxWidth: 600, mx: 'auto' }}>
              🎯 Investing in the right tools increases your tender success rate by up to 40%!
            </Alert>
          )}

          {/* Billing Toggle */}
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: 2, mb: 3 }}>
            <Typography variant="body1">Monthly</Typography>
            <FormControlLabel
              control={
                <Switch
                  checked={yearlyBilling}
                  onChange={(e) => setYearlyBilling(e.target.checked)}
                  color="primary"
                />
              }
              label=""
            />
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="body1">Yearly</Typography>
              <Chip label="Save up to 20%" color="success" size="small" />
            </Box>
          </Box>
        </Box>

        {/* Stress level indicator */}
        {stressLevel === 'high' && (
          <Alert 
            severity="info" 
            sx={{ mb: 3, maxWidth: 800, mx: 'auto' }}
            icon={<PsychologyIcon />}
          >
            We've simplified the plan comparison. Focus on the features that matter most to your business.
          </Alert>
        )}

        {/* Plans Grid */}
        <Grid container spacing={3} justifyContent="center">
          {plans?.map((plan) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={plan.id}>
              {renderPlanCard(plan)}
            </Grid>
          ))}
        </Grid>

        {/* Additional Information */}
        <Box sx={{ mt: 6, textAlign: 'center' }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Why Choose BidBeez?
          </Typography>
          <Grid container spacing={3} justifyContent="center">
            <Grid item xs={12} sm={4}>
              <Box sx={{ textAlign: 'center' }}>
                <SecurityIcon color="success" sx={{ fontSize: 48, mb: 1 }} />
                <Typography variant="h6" sx={{ mb: 1 }}>
                  SA Legal Compliance
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Full compliance with PFMA, PPPFA, and Municipal Systems Act
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box sx={{ textAlign: 'center' }}>
                <PsychologyIcon color="primary" sx={{ fontSize: 48, mb: 1 }} />
                <Typography variant="h6" sx={{ mb: 1 }}>
                  AI-Powered Insights
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Psychological optimization and success prediction
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box sx={{ textAlign: 'center' }}>
                <BusinessIcon color="warning" sx={{ fontSize: 48, mb: 1 }} />
                <Typography variant="h6" sx={{ mb: 1 }}>
                  SME Focused
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Designed specifically for South African SMEs
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>

        {/* Help Section */}
        {shouldShowHelp && (
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <Alert severity="info" sx={{ maxWidth: 600, mx: 'auto' }}>
              <Typography variant="body2">
                Need help choosing the right plan? 
                <Button 
                  variant="text" 
                  onClick={() => navigate('/help/pricing')}
                  sx={{ ml: 1 }}
                >
                  Contact our team
                </Button>
              </Typography>
            </Alert>
          </Box>
        )}

        {/* Upgrade Confirmation Dialog */}
        <Dialog
          open={upgradeDialogOpen}
          onClose={() => setUpgradeDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            Upgrade to {selectedPlan?.name}
          </DialogTitle>
          <DialogContent>
            <Typography variant="body1" sx={{ mb: 2 }}>
              You're about to upgrade to the {selectedPlan?.name} plan.
            </Typography>
            
            {selectedPlan?.complianceIncluded && (
              <Alert severity="success" sx={{ mb: 2 }}>
                🎉 This plan includes full SA Compliance Tools - perfect for professional tender management!
              </Alert>
            )}

            {showCostWarnings && (
              <Alert severity="info" sx={{ mb: 2 }}>
                💰 You'll be charged R{selectedPlan ? getPrice(selectedPlan) : 0} {yearlyBilling ? 'yearly' : 'monthly'}.
              </Alert>
            )}

            <Typography variant="body2" color="text.secondary">
              You can cancel or change your plan at any time.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setUpgradeDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleUpgrade}
              variant="contained"
              color="primary"
            >
              Continue to Payment
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </AdaptiveInterface>
  );
};

export default PricingPlans;
