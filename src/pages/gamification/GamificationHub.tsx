/**
 * Advanced Gamification Engine
 * Complete achievement system, leaderboards, mentorship, and social features
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Avatar,
  LinearProgress,
  Chip,
  Button,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Tabs,
  Tab,
  Paper,
  Stack,
  Divider,
  Badge,
  IconButton,
  Tooltip,
  Alert,
  Collapse
} from '@mui/material';
import {
  EmojiEvents as TrophyIcon,
  Star as StarIcon,
  TrendingUp as LeaderboardIcon,
  School as MentorshipIcon,
  Group as CommunityIcon,
  Psychology as PsychologyIcon,
  AutoAwesome as MagicIcon,
  Timer as StreakIcon,
  Speed as BoostIcon,
  Favorite as HeartIcon,
  Share as ShareIcon,
  Add as AddIcon,
  ExpandMore as ExpandIcon,
  ExpandLess as CollapseIcon,
  Celebration as CelebrationIcon,
  LocalFireDepartment as FireIcon,
  Diamond as DiamondIcon,
  Bolt as BoltIcon
} from '@mui/icons-material';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';
import confetti from 'canvas-confetti';

interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'bidding' | 'collaboration' | 'learning' | 'social' | 'milestone';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  xpReward: number;
  unlockedDate?: string;
  progress?: {
    current: number;
    required: number;
  };
  psychologicalBenefit: string;
}

interface UserLevel {
  current: number;
  xp: number;
  xpToNext: number;
  title: string;
  perks: string[];
  nextTitle: string;
}

interface LeaderboardEntry {
  rank: number;
  userId: string;
  username: string;
  avatar: string;
  level: number;
  xp: number;
  achievements: number;
  streak: number;
  category: string;
  psychologicalProfile: 'analytical' | 'driver' | 'expressive' | 'amiable';
}

interface Mentor {
  id: string;
  name: string;
  avatar: string;
  expertise: string[];
  rating: number;
  studentsCount: number;
  achievements: string[];
  bio: string;
  availability: 'available' | 'busy' | 'offline';
  psychologicalCompatibility: number;
}

interface Challenge {
  id: string;
  title: string;
  description: string;
  type: 'daily' | 'weekly' | 'monthly' | 'special';
  difficulty: 'easy' | 'medium' | 'hard' | 'expert';
  xpReward: number;
  deadline: string;
  participants: number;
  completed: boolean;
  progress?: number;
  psychologicalFocus: string;
}

const GamificationHub: React.FC = () => {
  // Behavioral tracking
  const {
    psychologicalState,
    isStressed,
    needsSimplification,
    trackEngagement
  } = useNeuroMarketing();

  // State management
  const [selectedTab, setSelectedTab] = useState(0);
  const [userLevel, setUserLevel] = useState<UserLevel | null>(null);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [mentors, setMentors] = useState<Mentor[]>([]);
  const [challenges, setChallenges] = useState<Challenge[]>([]);
  const [showCelebration, setShowCelebration] = useState(false);
  const [expandedAchievements, setExpandedAchievements] = useState(false);

  // Load gamification data
  useEffect(() => {
    loadUserLevel();
    loadAchievements();
    loadLeaderboard();
    loadMentors();
    loadChallenges();
  }, []);

  // Check for new achievements
  useEffect(() => {
    checkForNewAchievements();
  }, [psychologicalState]);

  const loadUserLevel = () => {
    const mockUserLevel: UserLevel = {
      current: 7,
      xp: 2850,
      xpToNext: 350,
      title: 'Tender Specialist',
      perks: ['Priority support', 'Advanced analytics', 'Exclusive challenges'],
      nextTitle: 'Bid Master'
    };
    setUserLevel(mockUserLevel);
  };

  const loadAchievements = () => {
    const mockAchievements: Achievement[] = [
      {
        id: 'ach-001',
        name: 'First Bid',
        description: 'Submit your first tender bid',
        icon: '🎯',
        category: 'bidding',
        rarity: 'common',
        xpReward: 100,
        unlockedDate: '2024-01-15T10:00:00Z',
        psychologicalBenefit: 'Builds confidence and reduces first-time anxiety'
      },
      {
        id: 'ach-002',
        name: 'Stress Master',
        description: 'Complete 10 tasks while maintaining low stress levels',
        icon: '🧘',
        category: 'milestone',
        rarity: 'rare',
        xpReward: 250,
        unlockedDate: '2024-01-20T14:30:00Z',
        psychologicalBenefit: 'Reinforces stress management skills'
      },
      {
        id: 'ach-003',
        name: 'Team Player',
        description: 'Collaborate on 5 successful bids',
        icon: '🤝',
        category: 'collaboration',
        rarity: 'epic',
        xpReward: 500,
        unlockedDate: '2024-01-25T09:15:00Z',
        psychologicalBenefit: 'Enhances social connection and teamwork satisfaction'
      },
      {
        id: 'ach-004',
        name: 'Compliance Champion',
        description: 'Achieve 100% compliance score on 3 consecutive bids',
        icon: '⚖️',
        category: 'bidding',
        rarity: 'legendary',
        xpReward: 1000,
        progress: { current: 2, required: 3 },
        psychologicalBenefit: 'Reduces legal anxiety and builds expertise confidence'
      },
      {
        id: 'ach-005',
        name: 'Mentor',
        description: 'Help 5 new users with their first bids',
        icon: '🎓',
        category: 'social',
        rarity: 'epic',
        xpReward: 750,
        progress: { current: 3, required: 5 },
        psychologicalBenefit: 'Increases sense of purpose and community belonging'
      }
    ];
    setAchievements(mockAchievements);
  };

  const loadLeaderboard = () => {
    const mockLeaderboard: LeaderboardEntry[] = [
      {
        rank: 1,
        userId: 'user-001',
        username: 'TenderPro2024',
        avatar: '/avatars/user1.jpg',
        level: 12,
        xp: 8450,
        achievements: 28,
        streak: 15,
        category: 'Overall',
        psychologicalProfile: 'driver'
      },
      {
        rank: 2,
        userId: 'user-002',
        username: 'BidMaster',
        avatar: '/avatars/user2.jpg',
        level: 11,
        xp: 7890,
        achievements: 25,
        streak: 12,
        category: 'Overall',
        psychologicalProfile: 'analytical'
      },
      {
        rank: 3,
        userId: 'user-003',
        username: 'ComplianceQueen',
        avatar: '/avatars/user3.jpg',
        level: 10,
        xp: 7200,
        achievements: 22,
        streak: 8,
        category: 'Overall',
        psychologicalProfile: 'amiable'
      },
      {
        rank: 4,
        userId: 'current-user',
        username: 'You',
        avatar: '/avatars/current.jpg',
        level: 7,
        xp: 2850,
        achievements: 15,
        streak: 5,
        category: 'Overall',
        psychologicalProfile: 'expressive'
      }
    ];
    setLeaderboard(mockLeaderboard);
  };

  const loadMentors = () => {
    const mockMentors: Mentor[] = [
      {
        id: 'mentor-001',
        name: 'Dr. Sarah Mitchell',
        avatar: '/avatars/mentor1.jpg',
        expertise: ['Government Tenders', 'Compliance', 'Project Management'],
        rating: 4.9,
        studentsCount: 47,
        achievements: ['Master Mentor', 'Compliance Expert', 'Psychology Specialist'],
        bio: 'Former government procurement officer with 15+ years experience. Specializes in reducing bid anxiety and building confidence.',
        availability: 'available',
        psychologicalCompatibility: 92
      },
      {
        id: 'mentor-002',
        name: 'Mike Johnson',
        avatar: '/avatars/mentor2.jpg',
        expertise: ['Technical Bids', 'IT Tenders', 'Innovation'],
        rating: 4.8,
        studentsCount: 33,
        achievements: ['Tech Guru', 'Innovation Leader', 'Stress Reducer'],
        bio: 'Technology consultant who helps reduce technical complexity anxiety and builds systematic approaches to complex bids.',
        availability: 'busy',
        psychologicalCompatibility: 87
      }
    ];
    setMentors(mockMentors);
  };

  const loadChallenges = () => {
    const mockChallenges: Challenge[] = [
      {
        id: 'challenge-001',
        title: 'Stress-Free Week',
        description: 'Complete all tasks this week while maintaining stress levels below 50%',
        type: 'weekly',
        difficulty: 'medium',
        xpReward: 300,
        deadline: '2024-02-01T23:59:59Z',
        participants: 156,
        completed: false,
        progress: 65,
        psychologicalFocus: 'Stress management and mindful productivity'
      },
      {
        id: 'challenge-002',
        title: 'Collaboration Master',
        description: 'Work with 3 different team members on various projects',
        type: 'monthly',
        difficulty: 'easy',
        xpReward: 200,
        deadline: '2024-02-28T23:59:59Z',
        participants: 89,
        completed: false,
        progress: 33,
        psychologicalFocus: 'Social connection and teamwork skills'
      },
      {
        id: 'challenge-003',
        title: 'Perfect Compliance',
        description: 'Achieve 100% compliance score on your next bid',
        type: 'special',
        difficulty: 'hard',
        xpReward: 500,
        deadline: '2024-02-15T17:00:00Z',
        participants: 234,
        completed: true,
        psychologicalFocus: 'Attention to detail and confidence building'
      }
    ];
    setChallenges(mockChallenges);
  };

  const checkForNewAchievements = () => {
    // Simulate achievement checking based on psychological state
    if (psychologicalState?.stressLevel < 0.3 && Math.random() < 0.1) {
      triggerAchievement('Zen Master', 'Maintained low stress for extended period');
    }
  };

  const triggerAchievement = (name: string, description: string) => {
    setShowCelebration(true);
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 }
    });

    setTimeout(() => setShowCelebration(false), 3000);

    trackEngagement('achievement_unlocked', {
      achievementName: name,
      psychologicalState
    });
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return '#FFD700';
      case 'epic': return '#9C27B0';
      case 'rare': return '#2196F3';
      default: return '#4CAF50';
    }
  };

  const getRarityIcon = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return <DiamondIcon />;
      case 'epic': return <BoltIcon />;
      case 'rare': return <StarIcon />;
      default: return <TrophyIcon />;
    }
  };

  const renderAchievement = (achievement: Achievement) => (
    <Card 
      key={achievement.id}
      sx={{ 
        mb: 2,
        border: achievement.unlockedDate ? `2px solid ${getRarityColor(achievement.rarity)}` : '1px solid #e0e0e0',
        opacity: achievement.unlockedDate ? 1 : 0.6
      }}
    >
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ fontSize: '2rem', mr: 2 }}>
            {achievement.icon}
          </Box>
          
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
              {achievement.name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {achievement.description}
            </Typography>
          </Box>

          <Box sx={{ textAlign: 'right' }}>
            <Chip
              icon={getRarityIcon(achievement.rarity)}
              label={achievement.rarity.toUpperCase()}
              size="small"
              sx={{ 
                bgcolor: getRarityColor(achievement.rarity),
                color: 'white',
                mb: 1
              }}
            />
            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
              +{achievement.xpReward} XP
            </Typography>
          </Box>
        </Box>

        {achievement.progress && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              Progress: {achievement.progress.current}/{achievement.progress.required}
            </Typography>
            <LinearProgress
              variant="determinate"
              value={(achievement.progress.current / achievement.progress.required) * 100}
              sx={{ height: 8, borderRadius: 4 }}
            />
          </Box>
        )}

        <Alert severity="info" sx={{ mt: 2 }}>
          <Typography variant="body2">
            🧠 <strong>Psychological Benefit:</strong> {achievement.psychologicalBenefit}
          </Typography>
        </Alert>

        {achievement.unlockedDate && (
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            Unlocked: {new Date(achievement.unlockedDate).toLocaleDateString()}
          </Typography>
        )}
      </CardContent>
    </Card>
  );

  const renderLeaderboardEntry = (entry: LeaderboardEntry) => (
    <ListItem key={entry.userId} sx={{ px: 0 }}>
      <ListItemAvatar>
        <Badge
          badgeContent={entry.rank}
          color={entry.rank <= 3 ? 'primary' : 'default'}
          anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
        >
          <Avatar src={entry.avatar} sx={{ width: 48, height: 48 }}>
            {entry.username.charAt(0)}
          </Avatar>
        </Badge>
      </ListItemAvatar>
      
      <ListItemText
        primary={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
              {entry.username}
            </Typography>
            {entry.userId === 'current-user' && (
              <Chip label="You" size="small" color="primary" />
            )}
          </Box>
        }
        secondary={
          <Box>
            <Typography variant="body2" color="text.secondary">
              Level {entry.level} • {entry.xp.toLocaleString()} XP
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
              <Chip label={`${entry.achievements} achievements`} size="small" />
              <Chip label={`${entry.streak} day streak`} size="small" color="warning" />
            </Box>
          </Box>
        }
      />
      
      <ListItemSecondaryAction>
        {entry.rank <= 3 && (
          <TrophyIcon 
            sx={{ 
              color: entry.rank === 1 ? '#FFD700' : entry.rank === 2 ? '#C0C0C0' : '#CD7F32',
              fontSize: 32
            }} 
          />
        )}
      </ListItemSecondaryAction>
    </ListItem>
  );

  const renderMentor = (mentor: Mentor) => (
    <Card key={mentor.id} sx={{ mb: 2 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Badge
            overlap="circular"
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            badgeContent={
              <Box
                sx={{
                  width: 12,
                  height: 12,
                  borderRadius: '50%',
                  bgcolor: mentor.availability === 'available' ? 'success.main' : 'warning.main',
                  border: '2px solid white'
                }}
              />
            }
          >
            <Avatar src={mentor.avatar} sx={{ width: 56, height: 56 }}>
              {mentor.name.charAt(0)}
            </Avatar>
          </Badge>
          
          <Box sx={{ ml: 2, flexGrow: 1 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
              {mentor.name}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <StarIcon sx={{ fontSize: 16, color: 'warning.main' }} />
              <Typography variant="body2">
                {mentor.rating} ({mentor.studentsCount} students)
              </Typography>
            </Box>
          </Box>
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {mentor.bio}
        </Typography>

        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" sx={{ mb: 1 }}>Expertise:</Typography>
          <Stack direction="row" spacing={0.5} flexWrap="wrap">
            {mentor.expertise.map((skill, index) => (
              <Chip key={index} label={skill} size="small" variant="outlined" />
            ))}
          </Stack>
        </Box>

        <Alert severity="success" sx={{ mb: 2 }}>
          <Typography variant="body2">
            🧠 <strong>{mentor.psychologicalCompatibility}% Compatibility</strong> - 
            This mentor's teaching style aligns well with your learning preferences.
          </Typography>
        </Alert>

        <Button
          variant={mentor.availability === 'available' ? 'contained' : 'outlined'}
          fullWidth
          disabled={mentor.availability === 'offline'}
          onClick={() => trackEngagement('mentor_contact', { mentorId: mentor.id })}
        >
          {mentor.availability === 'available' ? 'Connect Now' : 
           mentor.availability === 'busy' ? 'Join Waitlist' : 'Unavailable'}
        </Button>
      </CardContent>
    </Card>
  );

  const renderChallenge = (challenge: Challenge) => (
    <Card key={challenge.id} sx={{ mb: 2 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
              {challenge.title}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {challenge.description}
            </Typography>
          </Box>

          <Box sx={{ textAlign: 'right' }}>
            <Chip
              label={challenge.type.toUpperCase()}
              size="small"
              color="primary"
              sx={{ mb: 1 }}
            />
            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
              +{challenge.xpReward} XP
            </Typography>
          </Box>
        </Box>

        {challenge.progress !== undefined && !challenge.completed && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              Progress: {challenge.progress}%
            </Typography>
            <LinearProgress
              variant="determinate"
              value={challenge.progress}
              sx={{ height: 8, borderRadius: 4 }}
            />
          </Box>
        )}

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            🏃‍♂️ {challenge.participants} participants
          </Typography>
          <Typography variant="body2" color="text.secondary">
            ⏰ {new Date(challenge.deadline).toLocaleDateString()}
          </Typography>
        </Box>

        <Alert severity="info">
          <Typography variant="body2">
            🧠 <strong>Focus:</strong> {challenge.psychologicalFocus}
          </Typography>
        </Alert>

        {challenge.completed && (
          <Chip
            label="COMPLETED ✅"
            color="success"
            sx={{ mt: 2 }}
          />
        )}
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ maxWidth: 1400, mx: 'auto', p: 3 }}>
      {/* Celebration Alert */}
      <Collapse in={showCelebration}>
        <Alert 
          severity="success" 
          icon={<CelebrationIcon />}
          sx={{ mb: 3, fontSize: '1.2rem' }}
        >
          🎉 <strong>Achievement Unlocked!</strong> You're making great progress!
        </Alert>
      </Collapse>

      {/* Header with Level Progress */}
      <Card elevation={2} sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={8}>
              <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1 }}>
                🎮 Gamification Hub
              </Typography>
              
              {userLevel && (
                <Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <Typography variant="h6">
                      Level {userLevel.current} - {userLevel.title}
                    </Typography>
                    <Chip 
                      label={`${userLevel.xp.toLocaleString()} XP`}
                      color="primary"
                    />
                  </Box>
                  
                  <Box sx={{ mb: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      {userLevel.xpToNext} XP to {userLevel.nextTitle}
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={(userLevel.xp / (userLevel.xp + userLevel.xpToNext)) * 100}
                      sx={{ height: 10, borderRadius: 5, mt: 1 }}
                    />
                  </Box>
                </Box>
              )}
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Stack spacing={1}>
                <Button
                  variant="contained"
                  startIcon={<MagicIcon />}
                  fullWidth
                  onClick={() => trackEngagement('daily_bonus', {})}
                >
                  Claim Daily Bonus
                </Button>
                
                {!needsSimplification && (
                  <Button
                    variant="outlined"
                    startIcon={<PsychologyIcon />}
                    fullWidth
                    onClick={() => trackEngagement('psychology_insights', {})}
                  >
                    Psychology Insights
                  </Button>
                )}
              </Stack>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs 
          value={selectedTab} 
          onChange={(e, newValue) => setSelectedTab(newValue)}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label="Achievements" icon={<TrophyIcon />} />
          <Tab label="Leaderboard" icon={<LeaderboardIcon />} />
          <Tab label="Mentorship" icon={<MentorshipIcon />} />
          <Tab label="Challenges" icon={<FireIcon />} />
          <Tab label="Community" icon={<CommunityIcon />} />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      {selectedTab === 0 && (
        <Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">
              Your Achievements ({achievements.filter(a => a.unlockedDate).length}/{achievements.length})
            </Typography>
            <Button
              onClick={() => setExpandedAchievements(!expandedAchievements)}
              endIcon={expandedAchievements ? <CollapseIcon /> : <ExpandIcon />}
            >
              {expandedAchievements ? 'Show Less' : 'Show All'}
            </Button>
          </Box>
          
          {(expandedAchievements ? achievements : achievements.slice(0, 3)).map(renderAchievement)}
        </Box>
      )}

      {selectedTab === 1 && (
        <Box>
          <Typography variant="h6" sx={{ mb: 3 }}>
            Global Leaderboard
          </Typography>
          <List>
            {leaderboard.map(renderLeaderboardEntry)}
          </List>
        </Box>
      )}

      {selectedTab === 2 && (
        <Box>
          <Typography variant="h6" sx={{ mb: 3 }}>
            Find Your Perfect Mentor
          </Typography>
          {mentors.map(renderMentor)}
        </Box>
      )}

      {selectedTab === 3 && (
        <Box>
          <Typography variant="h6" sx={{ mb: 3 }}>
            Active Challenges
          </Typography>
          {challenges.map(renderChallenge)}
        </Box>
      )}

      {selectedTab === 4 && (
        <Box>
          <Typography variant="h6" sx={{ mb: 3 }}>
            Community Features
          </Typography>
          <Alert severity="info">
            <Typography variant="body1">
              🚧 Community features coming soon! Connect with other bidders, share experiences, and learn together.
            </Typography>
          </Alert>
        </Box>
      )}
    </Box>
  );
};

export default GamificationHub;
