import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  CardHeader,
  <PERSON><PERSON><PERSON>,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  LinearProgress,
  Stack,
  Avatar,
  Divider,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Add,
  Visibility,
  Edit,
  Delete,
  Refresh,
  TrendingUp,
  Timer,
  People,
  MonetizationOn,
  Assessment,
  FlashOn,
  CheckCircle,
  Warning,
  Speed,
  Psychology,
  LocationOn,
  Target,
  Description
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

interface RFQ {
  id: string;
  rfqNumber: string;
  title: string;
  category: string;
  estimatedValue: number;
  status: 'draft' | 'active' | 'closed' | 'awarded';
  submissionDeadline: string;
  responseCount: number;
  targetSuppliers: number;
  createdAt: string;
  timeToCreate: number; // seconds
  avgQuoteValue: number;
  bestQuote: number;
  savings: number;
  urgency: 'low' | 'medium' | 'high' | 'critical';
}

interface RFQResponse {
  id: string;
  rfqId: string;
  supplierName: string;
  totalPrice: number;
  submittedAt: string;
  status: 'pending' | 'reviewed' | 'accepted' | 'rejected';
  deliveryTime: string;
  rating: number;
}

const RFQManagement: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [rfqs, setRfqs] = useState<RFQ[]>([
    {
      id: 'rfq-001',
      rfqNumber: 'RFQ-2024-001',
      title: 'Office Cleaning Services - Monthly Contract',
      category: 'Cleaning Services',
      estimatedValue: 150000,
      status: 'active',
      submissionDeadline: '2024-02-15T17:00:00',
      responseCount: 8,
      targetSuppliers: 10,
      createdAt: '2024-01-20T10:30:00',
      timeToCreate: 87, // 87 seconds - Speed Demon!
      avgQuoteValue: 142500,
      bestQuote: 135000,
      savings: 15000,
      urgency: 'medium'
    },
    {
      id: 'rfq-002',
      rfqNumber: 'RFQ-2024-002',
      title: 'IT Equipment - Laptops and Monitors',
      category: 'IT Equipment',
      estimatedValue: 450000,
      status: 'active',
      submissionDeadline: '2024-02-10T12:00:00',
      responseCount: 12,
      targetSuppliers: 15,
      createdAt: '2024-01-18T14:15:00',
      timeToCreate: 134,
      avgQuoteValue: 425000,
      bestQuote: 398000,
      savings: 52000,
      urgency: 'high'
    },
    {
      id: 'rfq-003',
      rfqNumber: 'RFQ-2024-003',
      title: 'Security Services - 24/7 Monitoring',
      category: 'Security Services',
      estimatedValue: 280000,
      status: 'closed',
      submissionDeadline: '2024-01-25T16:00:00',
      responseCount: 6,
      targetSuppliers: 8,
      createdAt: '2024-01-15T09:45:00',
      timeToCreate: 76, // Speed Demon!
      avgQuoteValue: 265000,
      bestQuote: 245000,
      savings: 35000,
      urgency: 'low'
    }
  ]);

  const [selectedRFQ, setSelectedRFQ] = useState<RFQ | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  // Tender Intelligence for RFQ Opportunities
  const [tenderRFQIntelligence, setTenderRFQIntelligence] = useState([
    {
      id: 'tender-001',
      title: 'Municipal Infrastructure - R15.6M',
      value: 15600000,
      location: 'Johannesburg, Gauteng',
      urgency: 'critical' as const,
      rfqOpportunities: [
        { category: 'Construction Materials', estimatedValue: 2500000, suppliers: 45, psychTrigger: 'HIGH DEMAND: 45 suppliers ready to quote!' },
        { category: 'Steel Supplies', estimatedValue: 1800000, suppliers: 32, psychTrigger: 'COMPETITIVE: 32 suppliers in your area!' },
        { category: 'Concrete & Cement', estimatedValue: 1200000, suppliers: 28, psychTrigger: 'OPPORTUNITY: Local suppliers preferred!' }
      ],
      psychTrigger: 'RFQ GOLDMINE: R5.5M in RFQ opportunities from this tender!',
      successRate: 89,
      avgResponseTime: '2.3 hours'
    },
    {
      id: 'tender-002',
      title: 'Office Building Construction - R8.5M',
      value: 8500000,
      location: 'Pretoria, Gauteng',
      urgency: 'high' as const,
      rfqOpportunities: [
        { category: 'Office Furniture', estimatedValue: 850000, suppliers: 67, psychTrigger: 'SUPPLIER SURGE: 67 furniture suppliers available!' },
        { category: 'IT Equipment', estimatedValue: 650000, suppliers: 89, psychTrigger: 'TECH BOOM: 89 IT suppliers competing!' },
        { category: 'Office Supplies', estimatedValue: 320000, suppliers: 156, psychTrigger: 'MASSIVE CHOICE: 156 suppliers ready!' }
      ],
      psychTrigger: 'RFQ EXPLOSION: R1.82M in quick RFQ wins available!',
      successRate: 94,
      avgResponseTime: '1.8 hours'
    }
  ]);

  const [dashboardMetrics, setDashboardMetrics] = useState({
    totalRFQs: 3,
    activeRFQs: 2,
    totalResponses: 26,
    avgResponseTime: '2.3 days',
    totalSavings: 102000,
    avgSavings: 22.7,
    speedDemonCount: 2,
    successRate: 89
  });

  useEffect(() => {
    loadRFQs();
  }, []);

  const loadRFQs = async () => {
    setLoading(true);
    try {
      // In real implementation, fetch from API
      console.log('Loading RFQs...');
    } catch (error) {
      console.error('Failed to load RFQs:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'closed': return 'info';
      case 'awarded': return 'warning';
      case 'draft': return 'default';
      default: return 'default';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return '#f44336';
      case 'high': return '#ff9800';
      case 'medium': return '#2196f3';
      case 'low': return '#4caf50';
      default: return '#757575';
    }
  };

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `R${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `R${(value / 1000).toFixed(0)}k`;
    return `R${value}`;
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleCreateRFQ = () => {
    navigate('/rfq/create');
  };

  const handleViewRFQ = (rfq: RFQ) => {
    setSelectedRFQ(rfq);
    setDetailsOpen(true);
  };

  const handleEditRFQ = (rfqId: string) => {
    navigate(`/rfq/edit/${rfqId}`);
  };

  const calculateResponseRate = (rfq: RFQ) => {
    return Math.round((rfq.responseCount / rfq.targetSuppliers) * 100);
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
            🚀 RFQ Management Dashboard
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Track your RFQs and supplier responses in real-time
          </Typography>
        </Box>
        
        <Stack direction="row" spacing={2}>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreateRFQ}
            sx={{
              background: 'linear-gradient(45deg, #4CAF50, #8BC34A)',
              fontSize: '1.1rem',
              fontWeight: 'bold'
            }}
          >
            🚀 Create RFQ (90s Challenge!)
          </Button>
          <IconButton onClick={loadRFQs} disabled={loading}>
            <Refresh />
          </IconButton>
        </Stack>
      </Box>

      {/* Dashboard Metrics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary" fontWeight="bold">
                {dashboardMetrics.totalRFQs}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total RFQs
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="success.main" fontWeight="bold">
                {dashboardMetrics.activeRFQs}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active RFQs
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main" fontWeight="bold">
                {formatCurrency(dashboardMetrics.totalSavings)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Savings
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="error.main" fontWeight="bold">
                {dashboardMetrics.speedDemonCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Speed Demon RFQs
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tender Intelligence - RFQ Opportunities */}
      <Card sx={{ mb: 3, border: '2px solid', borderColor: 'success.light', bgcolor: 'success.lighter' }}>
        <CardHeader
          title={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Psychology color="success" />
              <Typography variant="h6" fontWeight="bold">
                🧠 Tender Intelligence - RFQ Goldmine
              </Typography>
            </Box>
          }
          subheader="AI-powered RFQ opportunities from active tenders"
        />
        <CardContent>
          <Grid container spacing={3}>
            {tenderRFQIntelligence.map((tender) => (
              <Grid item xs={12} md={6} key={tender.id}>
                <Card
                  variant="outlined"
                  sx={{
                    border: '2px solid',
                    borderColor: tender.urgency === 'critical' ? 'error.main' : 'warning.main',
                    bgcolor: tender.urgency === 'critical' ? 'error.lighter' : 'warning.lighter'
                  }}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="h6" fontWeight="bold" sx={{ mb: 0.5 }}>
                          {tender.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          📍 {tender.location}
                        </Typography>
                        <Typography variant="body2" color="success.main" fontWeight="bold">
                          {tender.psychTrigger}
                        </Typography>
                      </Box>
                      <Chip
                        label={`${tender.successRate}% SUCCESS`}
                        color="success"
                        size="small"
                        sx={{ fontWeight: 'bold' }}
                      />
                    </Box>

                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      <strong>RFQ Opportunities:</strong>
                    </Typography>

                    <Stack spacing={1} sx={{ mb: 2 }}>
                      {tender.rfqOpportunities.map((opp, index) => (
                        <Card key={index} variant="outlined" sx={{ p: 1 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Box>
                              <Typography variant="body2" fontWeight="bold">
                                {opp.category}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {opp.psychTrigger}
                              </Typography>
                            </Box>
                            <Box sx={{ textAlign: 'right' }}>
                              <Typography variant="body2" fontWeight="bold" color="success.main">
                                {formatCurrency(opp.estimatedValue)}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {opp.suppliers} suppliers
                              </Typography>
                            </Box>
                          </Box>
                        </Card>
                      ))}
                    </Stack>

                    <Grid container spacing={2} sx={{ mb: 2 }}>
                      <Grid item xs={6}>
                        <Typography variant="caption" color="text.secondary">
                          Avg Response Time
                        </Typography>
                        <Typography variant="body2" fontWeight="bold" color="primary.main">
                          {tender.avgResponseTime}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="caption" color="text.secondary">
                          Total RFQ Value
                        </Typography>
                        <Typography variant="body2" fontWeight="bold" color="success.main">
                          {formatCurrency(tender.rfqOpportunities.reduce((sum, opp) => sum + opp.estimatedValue, 0))}
                        </Typography>
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      fullWidth
                      startIcon={<FlashOn />}
                      onClick={handleCreateRFQ}
                      sx={{
                        background: 'linear-gradient(45deg, #4CAF50, #8BC34A)',
                        fontWeight: 'bold'
                      }}
                    >
                      🚀 CREATE RFQs NOW! (90s Challenge)
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          <Alert severity="success" sx={{ mt: 3 }}>
            <Typography variant="body2" fontWeight="bold">
              🎯 RFQ INTELLIGENCE: Tender-driven RFQ creation increases success rates by 340%!
            </Typography>
            <Typography variant="caption">
              AI analyzes tender requirements to suggest high-value RFQ opportunities with ready suppliers
            </Typography>
          </Alert>
        </CardContent>
      </Card>

      {/* Success Rate Alert */}
      <Alert severity="success" sx={{ mb: 3 }}>
        🎯 <strong>Excellent Performance!</strong> Your RFQ success rate is {dashboardMetrics.successRate}%
        with an average savings of {dashboardMetrics.avgSavings}%. Keep creating RFQs to maintain your momentum!
      </Alert>

      {/* RFQ Table */}
      <Card>
        <CardHeader title="Your RFQs" />
        <CardContent>
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>RFQ Details</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Responses</TableCell>
                  <TableCell>Best Quote</TableCell>
                  <TableCell>Savings</TableCell>
                  <TableCell>Speed</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {rfqs.map((rfq) => (
                  <TableRow key={rfq.id} hover>
                    <TableCell>
                      <Box>
                        <Typography variant="body1" fontWeight="bold">
                          {rfq.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {rfq.rfqNumber} • {rfq.category}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Est. Value: {formatCurrency(rfq.estimatedValue)}
                        </Typography>
                      </Box>
                    </TableCell>
                    
                    <TableCell>
                      <Chip 
                        label={rfq.status.toUpperCase()}
                        color={getStatusColor(rfq.status) as any}
                        size="small"
                      />
                    </TableCell>
                    
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {rfq.responseCount}/{rfq.targetSuppliers}
                        </Typography>
                        <LinearProgress 
                          variant="determinate" 
                          value={calculateResponseRate(rfq)}
                          sx={{ mt: 0.5, height: 4, borderRadius: 2 }}
                        />
                        <Typography variant="caption" color="text.secondary">
                          {calculateResponseRate(rfq)}% response rate
                        </Typography>
                      </Box>
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold" color="success.main">
                        {formatCurrency(rfq.bestQuote)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Avg: {formatCurrency(rfq.avgQuoteValue)}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold" color="warning.main">
                        {formatCurrency(rfq.savings)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {Math.round((rfq.savings / rfq.estimatedValue) * 100)}%
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2">
                          {formatTime(rfq.timeToCreate)}
                        </Typography>
                        {rfq.timeToCreate <= 90 && (
                          <Tooltip title="Speed Demon Achievement!">
                            <Speed sx={{ color: '#4caf50', fontSize: 16 }} />
                          </Tooltip>
                        )}
                      </Box>
                    </TableCell>
                    
                    <TableCell>
                      <Stack direction="row" spacing={1}>
                        <Tooltip title="View Details">
                          <IconButton size="small" onClick={() => handleViewRFQ(rfq)}>
                            <Visibility />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit RFQ">
                          <IconButton size="small" onClick={() => handleEditRFQ(rfq.id)}>
                            <Edit />
                          </IconButton>
                        </Tooltip>
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* RFQ Details Dialog */}
      <Dialog open={detailsOpen} onClose={() => setDetailsOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          RFQ Details: {selectedRFQ?.title}
        </DialogTitle>
        <DialogContent>
          {selectedRFQ && (
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary">RFQ Number</Typography>
                <Typography variant="body1" fontWeight="bold">{selectedRFQ.rfqNumber}</Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary">Category</Typography>
                <Typography variant="body1" fontWeight="bold">{selectedRFQ.category}</Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary">Estimated Value</Typography>
                <Typography variant="body1" fontWeight="bold">{formatCurrency(selectedRFQ.estimatedValue)}</Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary">Best Quote</Typography>
                <Typography variant="body1" fontWeight="bold" color="success.main">
                  {formatCurrency(selectedRFQ.bestQuote)}
                </Typography>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsOpen(false)}>Close</Button>
          <Button variant="contained" onClick={() => handleEditRFQ(selectedRFQ?.id || '')}>
            Edit RFQ
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RFQManagement;
