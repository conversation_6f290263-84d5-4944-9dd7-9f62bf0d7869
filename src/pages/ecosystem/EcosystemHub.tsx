/**
 * Ecosystem Integration Hub
 * Connects SkillSync, ToolSync, ContractorSync, and SupplierNetwork with behavioral psychology
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Chip,
  Avatar,
  Rating,
  LinearProgress,
  Alert,
  Tabs,
  Tab,
  Stack,
  Divider,
  IconButton,
  Tooltip,
  Badge,
  Paper,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Hub as EcosystemIcon,
  People as SkillSyncIcon,
  Build as ToolSyncIcon,
  Engineering as ContractorIcon,
  Business as SupplierIcon,
  Psychology as PsychologyIcon,
  Star as StarIcon,
  TrendingUp as TrendingIcon,
  Security as TrustIcon,
  Speed as QuickIcon,
  EmojiEvents as AchievementIcon,
  Handshake as PartnerIcon,
  AutoAwesome as AIIcon,
  LocationOn as LocationIcon,
  AttachMoney as PriceIcon,
  Schedule as TimeIcon,
  Verified as VerifiedIcon,
  Chat as ChatI<PERSON>,
  Favorite as FavoriteIcon
} from '@mui/icons-material';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';

interface EcosystemService {
  id: string;
  name: string;
  type: 'skill' | 'tool' | 'contractor' | 'supplier';
  provider: {
    id: string;
    name: string;
    avatar: string;
    rating: number;
    reviewCount: number;
    verified: boolean;
    responseTime: string;
  };
  description: string;
  price: {
    amount: number;
    currency: string;
    unit: string;
  };
  location: string;
  availability: 'available' | 'busy' | 'unavailable';
  tags: string[];
  psychologicalFit: number; // 0-100 based on user psychology
  trustScore: number; // 0-100 based on behavioral analysis
  matchReasons: string[];
  socialProof: {
    recentHires: number;
    successRate: number;
    repeatClients: number;
  };
}

interface EcosystemRecommendation {
  id: string;
  type: 'collaboration' | 'cost_saving' | 'quality_boost' | 'time_saving';
  title: string;
  description: string;
  services: string[];
  potentialSavings: number;
  confidenceLevel: number;
  psychologicalBenefit: string;
}

const EcosystemHub: React.FC = () => {
  // Behavioral tracking
  const {
    psychologicalState,
    isStressed,
    isHighCognitiveLoad,
    needsSimplification,
    trackEngagement,
    getPersonalizedRecommendations
  } = useNeuroMarketing();

  // State management
  const [selectedTab, setSelectedTab] = useState(0);
  const [services, setServices] = useState<EcosystemService[]>([]);
  const [recommendations, setRecommendations] = useState<EcosystemRecommendation[]>([]);
  const [loading, setLoading] = useState(true);
  const [favoriteServices, setFavoriteServices] = useState<string[]>([]);
  const [showPsychologicalInsights, setShowPsychologicalInsights] = useState(false);

  // Load ecosystem data
  useEffect(() => {
    loadEcosystemServices();
    generateRecommendations();
  }, []);

  // Monitor psychological state for service recommendations
  useEffect(() => {
    if (psychologicalState?.stressLevel > 0.6) {
      setShowPsychologicalInsights(true);
      // Prioritize stress-reducing services
      prioritizeStressReducingServices();
    }
  }, [psychologicalState]);

  const loadEcosystemServices = () => {
    // Mock ecosystem services with psychological optimization
    const mockServices: EcosystemService[] = [
      {
        id: 'skill-001',
        name: 'Senior Project Manager - Government Tenders',
        type: 'skill',
        provider: {
          id: 'provider-001',
          name: 'Sarah Johnson',
          avatar: '/avatars/sarah.jpg',
          rating: 4.9,
          reviewCount: 127,
          verified: true,
          responseTime: '< 2 hours'
        },
        description: 'Experienced project manager specializing in government tender processes with 15+ years experience. Expert in compliance and stakeholder management.',
        price: { amount: 850, currency: 'ZAR', unit: 'per day' },
        location: 'Cape Town, Western Cape',
        availability: 'available',
        tags: ['Project Management', 'Government', 'Compliance', 'Leadership'],
        psychologicalFit: 92,
        trustScore: 96,
        matchReasons: [
          'Specializes in reducing project stress',
          'Excellent communication skills',
          'Proven track record with complex projects'
        ],
        socialProof: {
          recentHires: 8,
          successRate: 94,
          repeatClients: 78
        }
      },
      {
        id: 'tool-001',
        name: 'Professional Surveying Equipment Package',
        type: 'tool',
        provider: {
          id: 'provider-002',
          name: 'TechRent Solutions',
          avatar: '/avatars/techrent.jpg',
          rating: 4.7,
          reviewCount: 89,
          verified: true,
          responseTime: '< 1 hour'
        },
        description: 'Complete surveying equipment package including GPS, total station, and laser level. Perfect for construction and infrastructure projects.',
        price: { amount: 1200, currency: 'ZAR', unit: 'per week' },
        location: 'Johannesburg, Gauteng',
        availability: 'available',
        tags: ['Surveying', 'GPS', 'Construction', 'Infrastructure'],
        psychologicalFit: 85,
        trustScore: 91,
        matchReasons: [
          'Reduces technical complexity',
          'Includes training and support',
          'Reliable equipment with backup'
        ],
        socialProof: {
          recentHires: 15,
          successRate: 98,
          repeatClients: 85
        }
      },
      {
        id: 'contractor-001',
        name: 'Elite Construction Subcontractors',
        type: 'contractor',
        provider: {
          id: 'provider-003',
          name: 'BuildMaster Contractors',
          avatar: '/avatars/buildmaster.jpg',
          rating: 4.8,
          reviewCount: 156,
          verified: true,
          responseTime: '< 30 minutes'
        },
        description: 'Specialized construction team for government infrastructure projects. CIDB Grade 9 certified with extensive experience in public sector work.',
        price: { amount: 25000, currency: 'ZAR', unit: 'per project' },
        location: 'Pretoria, Gauteng',
        availability: 'busy',
        tags: ['Construction', 'Infrastructure', 'CIDB Grade 9', 'Government'],
        psychologicalFit: 88,
        trustScore: 94,
        matchReasons: [
          'Handles all compliance requirements',
          'Reduces project management stress',
          'Proven government project experience'
        ],
        socialProof: {
          recentHires: 12,
          successRate: 96,
          repeatClients: 82
        }
      },
      {
        id: 'supplier-001',
        name: 'Premium IT Hardware & Software Solutions',
        type: 'supplier',
        provider: {
          id: 'provider-004',
          name: 'TechSupply Pro',
          avatar: '/avatars/techsupply.jpg',
          rating: 4.6,
          reviewCount: 203,
          verified: true,
          responseTime: '< 4 hours'
        },
        description: 'Complete IT solutions provider with competitive pricing and excellent support. Specializes in government and enterprise requirements.',
        price: { amount: 15000, currency: 'ZAR', unit: 'per order' },
        location: 'Durban, KwaZulu-Natal',
        availability: 'available',
        tags: ['IT Hardware', 'Software', 'Enterprise', 'Support'],
        psychologicalFit: 79,
        trustScore: 87,
        matchReasons: [
          'Comprehensive support reduces technical stress',
          'Competitive pricing for budget peace of mind',
          'Reliable delivery schedules'
        ],
        socialProof: {
          recentHires: 22,
          successRate: 92,
          repeatClients: 76
        }
      }
    ];

    setServices(mockServices);
    setLoading(false);

    trackEngagement('ecosystem_hub_viewed', {
      serviceCount: mockServices.length,
      psychologicalState
    });
  };

  const generateRecommendations = () => {
    const mockRecommendations: EcosystemRecommendation[] = [
      {
        id: 'rec-001',
        type: 'collaboration',
        title: 'Stress-Reducing Project Team',
        description: 'Combine Sarah Johnson (Project Manager) with BuildMaster Contractors for a stress-free project experience.',
        services: ['skill-001', 'contractor-001'],
        potentialSavings: 15000,
        confidenceLevel: 94,
        psychologicalBenefit: 'Reduces project management stress by 60%'
      },
      {
        id: 'rec-002',
        type: 'cost_saving',
        title: 'Equipment + Training Bundle',
        description: 'TechRent\'s equipment package includes comprehensive training, reducing learning stress and improving efficiency.',
        services: ['tool-001'],
        potentialSavings: 8000,
        confidenceLevel: 87,
        psychologicalBenefit: 'Eliminates technical learning anxiety'
      }
    ];

    setRecommendations(mockRecommendations);
  };

  const prioritizeStressReducingServices = () => {
    // Re-sort services to prioritize those that reduce stress
    setServices(prev => 
      [...prev].sort((a, b) => {
        const aStressReduction = a.matchReasons.some(reason => 
          reason.toLowerCase().includes('stress') || reason.toLowerCase().includes('reduces')
        );
        const bStressReduction = b.matchReasons.some(reason => 
          reason.toLowerCase().includes('stress') || reason.toLowerCase().includes('reduces')
        );
        
        if (aStressReduction && !bStressReduction) return -1;
        if (!aStressReduction && bStressReduction) return 1;
        return b.psychologicalFit - a.psychologicalFit;
      })
    );
  };

  const getServiceIcon = (type: string) => {
    switch (type) {
      case 'skill': return <SkillSyncIcon />;
      case 'tool': return <ToolSyncIcon />;
      case 'contractor': return <ContractorIcon />;
      case 'supplier': return <SupplierIcon />;
      default: return <EcosystemIcon />;
    }
  };

  const getServiceColor = (type: string) => {
    switch (type) {
      case 'skill': return 'primary';
      case 'tool': return 'secondary';
      case 'contractor': return 'success';
      case 'supplier': return 'warning';
      default: return 'default';
    }
  };

  const handleServiceInteraction = (serviceId: string, action: string) => {
    trackEngagement('ecosystem_service_interaction', {
      serviceId,
      action,
      psychologicalState
    });

    switch (action) {
      case 'favorite':
        setFavoriteServices(prev => 
          prev.includes(serviceId) 
            ? prev.filter(id => id !== serviceId)
            : [...prev, serviceId]
        );
        break;
      case 'contact':
        // Open contact modal or redirect
        break;
      case 'hire':
        // Start hiring process
        break;
    }
  };

  const renderServiceCard = (service: EcosystemService) => (
    <Card 
      key={service.id} 
      elevation={2}
      sx={{ 
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        border: service.psychologicalFit > 90 ? '2px solid' : 'none',
        borderColor: 'success.main'
      }}
    >
      <CardContent sx={{ flexGrow: 1 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
          <Avatar 
            src={service.provider.avatar}
            sx={{ width: 48, height: 48, mr: 2 }}
          >
            {service.provider.name.charAt(0)}
          </Avatar>
          
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', lineHeight: 1.2 }}>
              {needsSimplification ? service.name.split(' ').slice(0, 4).join(' ') : service.name}
            </Typography>
            
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <Typography variant="body2" color="text.secondary">
                {service.provider.name}
              </Typography>
              {service.provider.verified && (
                <VerifiedIcon color="primary" sx={{ ml: 0.5, fontSize: 16 }} />
              )}
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <Rating value={service.provider.rating} precision={0.1} size="small" readOnly />
              <Typography variant="caption" sx={{ ml: 0.5 }}>
                ({service.provider.reviewCount})
              </Typography>
            </Box>
          </Box>

          <IconButton
            onClick={() => handleServiceInteraction(service.id, 'favorite')}
            color={favoriteServices.includes(service.id) ? 'error' : 'default'}
          >
            <FavoriteIcon />
          </IconButton>
        </Box>

        {/* Service Type and Psychological Fit */}
        <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
          <Chip 
            icon={getServiceIcon(service.type)}
            label={service.type.charAt(0).toUpperCase() + service.type.slice(1)}
            color={getServiceColor(service.type)}
            size="small"
          />
          
          {service.psychologicalFit > 85 && (
            <Chip 
              icon={<PsychologyIcon />}
              label={`${service.psychologicalFit}% Match`}
              color="success"
              size="small"
            />
          )}
          
          <Chip 
            label={service.availability.charAt(0).toUpperCase() + service.availability.slice(1)}
            color={service.availability === 'available' ? 'success' : 'warning'}
            size="small"
          />
        </Stack>

        {/* Description */}
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {needsSimplification 
            ? service.description.split('.')[0] + '...'
            : service.description
          }
        </Typography>

        {/* Psychological Match Reasons */}
        {service.psychologicalFit > 80 && (
          <Alert severity="success" sx={{ mb: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
              🧠 Perfect Psychological Match:
            </Typography>
            <Typography variant="body2">
              {service.matchReasons[0]}
            </Typography>
          </Alert>
        )}

        {/* Price and Location */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <PriceIcon sx={{ mr: 0.5, fontSize: 16 }} />
            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
              {service.price.currency} {service.price.amount.toLocaleString()} {service.price.unit}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <LocationIcon sx={{ mr: 0.5, fontSize: 16 }} />
            <Typography variant="caption" color="text.secondary">
              {service.location}
            </Typography>
          </Box>
        </Box>

        {/* Social Proof */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="caption" color="text.secondary">
            🔥 {service.socialProof.recentHires} recent hires
          </Typography>
          <Typography variant="caption" color="text.secondary">
            ✅ {service.socialProof.successRate}% success rate
          </Typography>
          <Typography variant="caption" color="text.secondary">
            🔄 {service.socialProof.repeatClients}% repeat clients
          </Typography>
        </Box>

        {/* Trust Score */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="caption" color="text.secondary">
            Trust Score: {service.trustScore}/100
          </Typography>
          <LinearProgress 
            variant="determinate" 
            value={service.trustScore}
            color={service.trustScore > 90 ? 'success' : 'primary'}
            sx={{ height: 4, borderRadius: 2 }}
          />
        </Box>
      </CardContent>

      <CardActions>
        <Button 
          variant="outlined" 
          size="small"
          startIcon={<ChatIcon />}
          onClick={() => handleServiceInteraction(service.id, 'contact')}
        >
          Contact
        </Button>
        
        <Button 
          variant="contained" 
          size="small"
          onClick={() => handleServiceInteraction(service.id, 'hire')}
          disabled={service.availability === 'unavailable'}
        >
          {service.availability === 'available' ? 'Hire Now' : 'Join Waitlist'}
        </Button>
      </CardActions>
    </Card>
  );

  const tabLabels = ['All Services', 'SkillSync', 'ToolSync', 'ContractorSync', 'SupplierNetwork'];
  const serviceTypes = ['all', 'skill', 'tool', 'contractor', 'supplier'];

  const filteredServices = selectedTab === 0 
    ? services 
    : services.filter(service => service.type === serviceTypes[selectedTab]);

  return (
    <Box sx={{ maxWidth: 1400, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Card elevation={2} sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={8}>
              <Typography variant={isHighCognitiveLoad ? "h4" : "h5"} sx={{ fontWeight: 'bold', mb: 1 }}>
                🌐 BidBeez Ecosystem Hub
              </Typography>
              <Typography variant="body1" color="text.secondary">
                {needsSimplification 
                  ? 'Find the right people and tools for your projects'
                  : 'Connect with skilled professionals, rent equipment, find contractors, and source suppliers - all optimized for your psychological profile'
                }
              </Typography>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Stack spacing={1}>
                <Button
                  variant="contained"
                  startIcon={<AIIcon />}
                  fullWidth
                  onClick={() => trackEngagement('ai_ecosystem_recommendations', {})}
                >
                  AI Recommendations
                </Button>
                
                {!needsSimplification && (
                  <Button
                    variant="outlined"
                    startIcon={<PsychologyIcon />}
                    fullWidth
                    onClick={() => setShowPsychologicalInsights(!showPsychologicalInsights)}
                  >
                    Psychological Insights
                  </Button>
                )}
              </Stack>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Psychological Insights */}
      {showPsychologicalInsights && (
        <Alert severity="info" sx={{ mb: 3 }} icon={<PsychologyIcon />}>
          <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
            🧠 Personalized Ecosystem Recommendations
          </Typography>
          <Typography variant="body2">
            Based on your current stress level ({Math.round((psychologicalState?.stressLevel || 0) * 100)}%), 
            we're prioritizing services that reduce project complexity and provide strong support systems.
          </Typography>
        </Alert>
      )}

      {/* Service Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs 
          value={selectedTab} 
          onChange={(e, newValue) => setSelectedTab(newValue)}
          variant="scrollable"
          scrollButtons="auto"
        >
          {tabLabels.map((label, index) => (
            <Tab 
              key={index}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {index > 0 && getServiceIcon(serviceTypes[index])}
                  {label}
                  <Badge 
                    badgeContent={index === 0 ? services.length : services.filter(s => s.type === serviceTypes[index]).length}
                    color="primary"
                  />
                </Box>
              }
            />
          ))}
        </Tabs>
      </Paper>

      {/* AI Recommendations */}
      {recommendations.length > 0 && selectedTab === 0 && (
        <Card elevation={2} sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
              <AIIcon sx={{ mr: 1 }} />
              AI-Powered Recommendations
            </Typography>
            
            <Grid container spacing={2}>
              {recommendations.map((rec) => (
                <Grid item xs={12} md={6} key={rec.id}>
                  <Paper sx={{ p: 2, border: '1px solid', borderColor: 'primary.main' }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
                      {rec.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {rec.description}
                    </Typography>
                    
                    <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
                      <Chip 
                        label={`Save R${rec.potentialSavings.toLocaleString()}`}
                        color="success"
                        size="small"
                      />
                      <Chip 
                        label={`${rec.confidenceLevel}% confidence`}
                        color="info"
                        size="small"
                      />
                    </Stack>
                    
                    <Typography variant="body2" sx={{ fontStyle: 'italic', mb: 2 }}>
                      🧠 {rec.psychologicalBenefit}
                    </Typography>
                    
                    <Button variant="outlined" size="small">
                      View Combination
                    </Button>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Services Grid */}
      <Grid container spacing={3}>
        {filteredServices.map((service) => (
          <Grid item xs={12} md={6} lg={4} key={service.id}>
            {renderServiceCard(service)}
          </Grid>
        ))}
      </Grid>

      {filteredServices.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <EcosystemIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            No services found in this category
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Try browsing other categories or check back later for new services
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default EcosystemHub;
