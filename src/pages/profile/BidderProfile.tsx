import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Ty<PERSON>graphy,
  Avatar,
  Chip,
  Button,
  LinearProgress,
  Tabs,
  Tab,
  Divider,
  Alert,
  IconButton,
  Badge,
  Switch,
  FormControlLabel,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel
} from '@mui/material';
import {
  Edit as EditIcon,
  Psychology as BrainIcon,
  TrendingUp as TrendingUpIcon,
  Shield as ShieldIcon,
  EmojiEvents as TrophyIcon,
  Business as BusinessIcon,
  Settings as SettingsIcon,
  Visibility as VisibilityIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Language as LanguageIcon,
  Palette as PaletteIcon,
  Assessment as AssessmentIcon,
  Star as StarIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';
import usePsychologicalSystems from '../../hooks/usePsychologicalSystems';
import AdaptiveInterface from '../../components/adaptive/AdaptiveInterface';

interface BidderProfileData {
  // Basic Information
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    avatar: string;
    title: string;
    company: string;
  };
  
  // Business Information
  businessInfo: {
    companyRegistration: string;
    taxNumber: string;
    beeLevel: string;
    cidbGrade: string;
    yearsInBusiness: number;
    employeeCount: number;
    annualTurnover: number;
    businessType: string;
    specializations: string[];
  };
  
  // Performance Metrics
  performance: {
    totalBids: number;
    successfulBids: number;
    successRate: number;
    totalEarnings: number;
    averageBidValue: number;
    currentRanking: number;
    totalRanking: number;
    reputationScore: number;
  };
  
  // Psychological Profile
  psychologicalProfile: {
    archetype: string;
    motivationFactors: string[];
    stressLevel: number;
    confidenceLevel: number;
    engagementLevel: number;
    cognitiveLoad: number;
    decisionMakingStyle: string;
    riskTolerance: string;
  };
  
  // Compliance Status
  compliance: {
    profileComplete: boolean;
    documentsVerified: boolean;
    taxCompliant: boolean;
    beeCompliant: boolean;
    cidbValid: boolean;
    lastComplianceCheck: string;
    complianceScore: number;
  };
  
  // Preferences
  preferences: {
    language: string;
    currency: string;
    timezone: string;
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
      deadlineReminders: boolean;
      newTenders: boolean;
      bidUpdates: boolean;
    };
    privacy: {
      profileVisibility: string;
      showPerformance: boolean;
      showCompany: boolean;
      allowContact: boolean;
    };
    interface: {
      theme: string;
      adaptiveInterface: boolean;
      neuroMarketingOptOut: boolean;
      simplifiedMode: boolean;
    };
  };
}

const BidderProfile: React.FC = () => {
  const { user, updateUser } = useAuth();
  const { psychologicalState, isStressed, needsSimplification } = useNeuroMarketing();
  const { profile: psychProfile, loading: psychLoading } = usePsychologicalSystems();
  
  const [activeTab, setActiveTab] = useState(0);
  const [editMode, setEditMode] = useState(false);
  const [profileData, setProfileData] = useState<BidderProfileData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProfileData();
  }, [user]);

  const loadProfileData = async () => {
    try {
      setLoading(true);
      // Mock data - replace with actual API call
      const mockProfileData: BidderProfileData = {
        personalInfo: {
          firstName: user?.username?.split(' ')[0] || 'John',
          lastName: user?.username?.split(' ')[1] || 'Doe',
          email: user?.email || '<EMAIL>',
          phone: '+27 82 123 4567',
          avatar: '/avatars/default.jpg',
          title: 'Senior Project Manager',
          company: user?.companyName || 'ABC Construction'
        },
        businessInfo: {
          companyRegistration: '2019/123456/07',
          taxNumber: '9876543210',
          beeLevel: user?.beeLevel || 'Level 4',
          cidbGrade: user?.cidbGrade || 'Grade 7',
          yearsInBusiness: user?.yearsInBusiness || 8,
          employeeCount: user?.employeeCount || 45,
          annualTurnover: user?.annualTurnover || 15000000,
          businessType: 'Construction & Engineering',
          specializations: ['Road Construction', 'Building', 'Infrastructure']
        },
        performance: {
          totalBids: 127,
          successfulBids: 34,
          successRate: 26.8,
          totalEarnings: 8750000,
          averageBidValue: 257000,
          currentRanking: 23,
          totalRanking: 1247,
          reputationScore: 4.6
        },
        psychologicalProfile: {
          archetype: psychProfile?.archetype || 'achiever',
          motivationFactors: psychProfile?.motivation_factors || ['recognition', 'financial_success', 'competition'],
          stressLevel: psychProfile?.psychological_state?.stress_level || 0.3,
          confidenceLevel: psychProfile?.psychological_state?.confidence_level || 0.75,
          engagementLevel: psychProfile?.psychological_state?.motivation_level || 0.8,
          cognitiveLoad: psychologicalState?.cognitiveLoad || 0.5,
          decisionMakingStyle: 'Analytical',
          riskTolerance: 'Moderate'
        },
        compliance: {
          profileComplete: user?.profileComplete || true,
          documentsVerified: true,
          taxCompliant: user?.taxCompliant || true,
          beeCompliant: true,
          cidbValid: true,
          lastComplianceCheck: '2024-01-15',
          complianceScore: 94
        },
        preferences: {
          language: user?.preferences?.language || 'en',
          currency: 'ZAR',
          timezone: 'Africa/Johannesburg',
          notifications: {
            email: user?.preferences?.notifications || true,
            sms: true,
            push: true,
            deadlineReminders: true,
            newTenders: true,
            bidUpdates: true
          },
          privacy: {
            profileVisibility: 'public',
            showPerformance: true,
            showCompany: true,
            allowContact: true
          },
          interface: {
            theme: 'light',
            adaptiveInterface: user?.preferences?.adaptiveInterface || true,
            neuroMarketingOptOut: user?.preferences?.neuroMarketingOptOut || false,
            simplifiedMode: needsSimplification
          }
        }
      };
      
      setProfileData(mockProfileData);
    } catch (error) {
      console.error('Failed to load profile data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getArchetypeColor = (archetype: string) => {
    const colors = {
      'achiever': '#9c27b0',
      'hunter': '#f44336', 
      'relationship_builder': '#2196f3',
      'analyst': '#4caf50'
    };
    return colors[archetype as keyof typeof colors] || '#757575';
  };

  const getArchetypeIcon = (archetype: string) => {
    const icons = {
      'achiever': '👑',
      'hunter': '🎯',
      'relationship_builder': '❤️',
      'analyst': '🧠'
    };
    return icons[archetype as keyof typeof icons] || '⭐';
  };

  const getComplianceColor = (score: number) => {
    if (score >= 90) return 'success';
    if (score >= 70) return 'warning';
    return 'error';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  if (loading || !profileData) {
    return (
      <AdaptiveInterface>
        <Box sx={{ p: 3 }}>
          <Grid container spacing={3}>
            {[1, 2, 3, 4].map((item) => (
              <Grid item xs={12} md={6} key={item}>
                <Card>
                  <CardContent>
                    <Box sx={{ height: 200, bgcolor: 'grey.100', borderRadius: 1 }} />
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      </AdaptiveInterface>
    );
  }

  return (
    <AdaptiveInterface>
      <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box>
            <Typography variant={needsSimplification ? 'h4' : 'h3'} component="h1" gutterBottom>
              My Profile
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Manage your bidder profile, preferences, and psychological insights
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant={editMode ? 'contained' : 'outlined'}
              startIcon={<EditIcon />}
              onClick={() => setEditMode(!editMode)}
            >
              {editMode ? 'Save Changes' : 'Edit Profile'}
            </Button>
            <IconButton>
              <SettingsIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Profile Header Card */}
        <Card sx={{ mb: 3, background: 'linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%)' }}>
          <CardContent sx={{ p: 3 }}>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={3} sx={{ textAlign: 'center' }}>
                <Avatar
                  src={profileData.personalInfo.avatar}
                  sx={{ width: 120, height: 120, mx: 'auto', mb: 2 }}
                >
                  {profileData.personalInfo.firstName[0]}{profileData.personalInfo.lastName[0]}
                </Avatar>
                <Typography variant="h5" gutterBottom>
                  {profileData.personalInfo.firstName} {profileData.personalInfo.lastName}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {profileData.personalInfo.title}
                </Typography>
                <Chip
                  label={profileData.personalInfo.company}
                  icon={<BusinessIcon />}
                  variant="outlined"
                />
              </Grid>

              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="primary" gutterBottom>
                    {profileData.performance.successRate.toFixed(1)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Success Rate
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={profileData.performance.successRate}
                    sx={{ mt: 1, height: 8, borderRadius: 4 }}
                  />
                </Box>
              </Grid>

              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="success.main" gutterBottom>
                    {formatCurrency(profileData.performance.totalEarnings)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Earnings
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                    <StarIcon sx={{ color: 'warning.main', mr: 0.5 }} />
                    <Typography variant="body2" fontWeight="medium">
                      {profileData.performance.reputationScore}/5.0
                    </Typography>
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="info.main" gutterBottom>
                    #{profileData.performance.currentRanking}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Current Ranking
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    of {profileData.performance.totalRanking.toLocaleString()} bidders
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Tabs */}
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)} sx={{ mb: 3 }}>
          <Tab label="Overview" />
          <Tab label="Business Info" />
          <Tab label="Psychology Profile" />
          <Tab label="Compliance" />
          <Tab label="Preferences" />
        </Tabs>

        {/* Tab Content */}
        {activeTab === 0 && (
          <Grid container spacing={3}>
            {/* Performance Overview */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader
                  title={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <TrendingUpIcon />
                      Performance Overview
                    </Box>
                  }
                />
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Total Bids</Typography>
                      <Typography variant="h6">{profileData.performance.totalBids}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Successful Bids</Typography>
                      <Typography variant="h6" color="success.main">
                        {profileData.performance.successfulBids}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Avg Bid Value</Typography>
                      <Typography variant="h6">
                        {formatCurrency(profileData.performance.averageBidValue)}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Reputation</Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <StarIcon sx={{ color: 'warning.main', fontSize: 20 }} />
                        <Typography variant="h6">
                          {profileData.performance.reputationScore}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Psychological State */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader
                  title={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <BrainIcon />
                      Current Psychological State
                    </Box>
                  }
                />
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Stress Level</Typography>
                      <LinearProgress
                        variant="determinate"
                        value={(1 - profileData.psychologicalProfile.stressLevel) * 100}
                        color={profileData.psychologicalProfile.stressLevel < 0.3 ? 'success' : 'warning'}
                        sx={{ mt: 1, height: 6, borderRadius: 3 }}
                      />
                      <Typography variant="caption">
                        {((1 - profileData.psychologicalProfile.stressLevel) * 100).toFixed(0)}% Wellness
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Confidence</Typography>
                      <LinearProgress
                        variant="determinate"
                        value={profileData.psychologicalProfile.confidenceLevel * 100}
                        color="primary"
                        sx={{ mt: 1, height: 6, borderRadius: 3 }}
                      />
                      <Typography variant="caption">
                        {(profileData.psychologicalProfile.confidenceLevel * 100).toFixed(0)}%
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                        <Typography variant="body2">Archetype:</Typography>
                        <Chip
                          label={`${getArchetypeIcon(profileData.psychologicalProfile.archetype)} ${profileData.psychologicalProfile.archetype.replace('_', ' ').toUpperCase()}`}
                          sx={{
                            backgroundColor: getArchetypeColor(profileData.psychologicalProfile.archetype),
                            color: 'white',
                            fontWeight: 'bold'
                          }}
                        />
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Compliance Status */}
            <Grid item xs={12}>
              <Card>
                <CardHeader
                  title={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ShieldIcon />
                      Compliance Status
                    </Box>
                  }
                />
                <CardContent>
                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2">Overall Compliance Score</Typography>
                      <Typography variant="h6" color={`${getComplianceColor(profileData.compliance.complianceScore)}.main`}>
                        {profileData.compliance.complianceScore}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={profileData.compliance.complianceScore}
                      color={getComplianceColor(profileData.compliance.complianceScore)}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={6} md={3}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckCircleIcon color={profileData.compliance.profileComplete ? 'success' : 'disabled'} />
                        <Typography variant="body2">Profile Complete</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} md={3}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckCircleIcon color={profileData.compliance.taxCompliant ? 'success' : 'disabled'} />
                        <Typography variant="body2">Tax Compliant</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} md={3}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckCircleIcon color={profileData.compliance.beeCompliant ? 'success' : 'disabled'} />
                        <Typography variant="body2">B-BBEE Verified</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} md={3}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckCircleIcon color={profileData.compliance.cidbValid ? 'success' : 'disabled'} />
                        <Typography variant="body2">CIDB Valid</Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {/* Additional tab content would go here */}
        {activeTab === 1 && (
          <Card>
            <CardHeader title="Business Information" />
            <CardContent>
              <Typography variant="body1">Business information content...</Typography>
            </CardContent>
          </Card>
        )}

        {activeTab === 2 && (
          <Card>
            <CardHeader title="Psychology Profile" />
            <CardContent>
              <Typography variant="body1">Psychology profile content...</Typography>
            </CardContent>
          </Card>
        )}

        {activeTab === 3 && (
          <Card>
            <CardHeader title="Compliance Details" />
            <CardContent>
              <Typography variant="body1">Compliance details content...</Typography>
            </CardContent>
          </Card>
        )}

        {activeTab === 4 && (
          <Card>
            <CardHeader title="Preferences & Settings" />
            <CardContent>
              <Typography variant="body1">Preferences content...</Typography>
            </CardContent>
          </Card>
        )}
      </Box>
    </AdaptiveInterface>
  );
};

export default BidderProfile;
