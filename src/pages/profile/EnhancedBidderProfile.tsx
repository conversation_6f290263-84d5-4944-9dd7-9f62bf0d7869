/**
 * 🧠 Enhanced Bidder Profile with Demo Integration
 * Combines Frontend V1's sophisticated features with <PERSON>'s demo capabilities
 * Features both production psychological profiling AND demo mode for client presentations
 */

'use client';

import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  Tabs,
  Tab,
  Alert,
  IconButton,
  Switch,
  FormControlLabel,
  Button,
  Stack
} from '@mui/material';
import {
  Edit as EditIcon,
  Psychology as BrainIcon,
  AutoAwesome as AIIcon,
  PlayArrow as DemoIcon,
  Stop as StopIcon
} from '@mui/icons-material';

// Import production hooks
import { useAuth } from '../../contexts/AuthContext';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';
import { usePsychologicalSystems } from '../../hooks/usePsychologicalSystems';
import { useDemoFeatures, FeatureGate } from '../../hooks/useFeatureFlags';

// Import demo components
import AdaptiveInterfaceDemo, { generateDemoPsychologicalState } from '../../components/demo/AdaptiveInterfaceDemo';
import PsychologicalLoadingDemo from '../../components/demo/PsychologicalLoadingDemo';
import { 
  useAuthDemo, 
  useNeuroMarketingDemo, 
  usePsychologicalSystemsDemo,
  usePsychologicalAnalysisLoading 
} from '../../hooks/demo/usePsychologicalDemo';

const EnhancedBidderProfile: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [demoMode, setDemoMode] = useState(false);
  const [demoArchetype, setDemoArchetype] = useState<'Achiever' | 'Hunter' | 'Analyst' | 'Relationship Builder'>('Achiever');
  const [showAdaptations, setShowAdaptations] = useState(true);

  // Feature flags
  const { psychologicalDemoEnabled, adaptiveInterfaceDemoEnabled, isDemoMode } = useDemoFeatures();

  // Production hooks
  const { user: prodUser } = useAuth();
  const prodNeuroMarketing = useNeuroMarketing();
  const prodPsychSystems = usePsychologicalSystems();

  // Demo hooks
  const { user: demoUser } = useAuthDemo('paid');
  const demoNeuroMarketing = useNeuroMarketingDemo(demoArchetype, demoMode);
  const demoPsychSystems = usePsychologicalSystemsDemo(demoArchetype);
  const { isLoading: demoLoading, progress, restart } = usePsychologicalAnalysisLoading(3000);

  // Choose data source based on demo mode
  const user = demoMode ? demoUser : prodUser;
  const psychologicalState = demoMode ? demoNeuroMarketing.psychologicalState : prodNeuroMarketing?.psychologicalState;
  const adaptiveRecommendations = demoMode ? demoNeuroMarketing.adaptiveRecommendations : prodNeuroMarketing?.adaptiveRecommendations || [];
  const personalityInsights = demoMode ? demoPsychSystems.personalityInsights : prodPsychSystems?.personalityInsights;

  const profileData = {
    personalInfo: {
      firstName: user?.username?.split(' ')[0] || 'John',
      lastName: user?.username?.split(' ')[1] || 'Doe',
      email: user?.email || '<EMAIL>',
      title: 'Managing Director',
      company: 'ABC Construction (Pty) Ltd'
    },
    performance: {
      totalBids: 156,
      successfulBids: 42,
      successRate: 26.9,
      currentRanking: 23
    }
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const toggleDemoMode = () => {
    setDemoMode(!demoMode);
    if (!demoMode) {
      restart(); // Start loading animation when entering demo mode
    }
  };

  const WrapperComponent = demoMode && adaptiveInterfaceDemoEnabled 
    ? AdaptiveInterfaceDemo 
    : Box;

  const wrapperProps = demoMode && adaptiveInterfaceDemoEnabled 
    ? { 
        psychologicalState: psychologicalState || generateDemoPsychologicalState(demoArchetype),
        demoMode: true,
        showAdaptations 
      }
    : {};

  // Show loading during demo mode initialization
  if (demoMode && demoLoading && psychologicalDemoEnabled) {
    return (
      <Box sx={{ p: 3 }}>
        <PsychologicalLoadingDemo
          isLoading={demoLoading}
          progress={progress}
          stage="analyzing"
          showStages={true}
        />
      </Box>
    );
  }

  return (
    <WrapperComponent {...wrapperProps}>
      <Box sx={{ p: 3 }}>
        {/* Demo Controls */}
        <FeatureGate feature="psychological_demo">
          <Card sx={{ mb: 3, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Box>
                  <Typography variant="h6" gutterBottom>
                    🎭 Demo Mode Controls
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Toggle demo mode to showcase psychological profiling capabilities to clients
                  </Typography>
                </Box>
                
                <Stack direction="row" spacing={2} alignItems="center">
                  <FormControlLabel
                    control={
                      <Switch
                        checked={demoMode}
                        onChange={toggleDemoMode}
                        color="default"
                      />
                    }
                    label={demoMode ? "Demo Mode ON" : "Demo Mode OFF"}
                  />
                  
                  {demoMode && (
                    <>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => setDemoArchetype('Achiever')}
                        sx={{ color: 'white', borderColor: 'white' }}
                      >
                        Achiever
                      </Button>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => setDemoArchetype('Hunter')}
                        sx={{ color: 'white', borderColor: 'white' }}
                      >
                        Hunter
                      </Button>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => setDemoArchetype('Analyst')}
                        sx={{ color: 'white', borderColor: 'white' }}
                      >
                        Analyst
                      </Button>
                    </>
                  )}
                </Stack>
              </Box>
            </CardContent>
          </Card>
        </FeatureGate>

        {/* Mode Indicator */}
        <Alert 
          severity={demoMode ? "info" : "success"} 
          sx={{ mb: 3 }}
          icon={demoMode ? <DemoIcon /> : <BrainIcon />}
        >
          <Typography variant="body2">
            {demoMode ? (
              <>
                <strong>🎭 Demo Mode Active:</strong> Showcasing psychological profiling for 
                <strong> {demoArchetype}</strong> archetype. Perfect for client presentations!
              </>
            ) : (
              <>
                <strong>🧠 Production Mode:</strong> Using real psychological profiling data and 
                advanced behavioral analytics.
              </>
            )}
          </Typography>
        </Alert>

        {/* Profile Header */}
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2}>
                  <Avatar sx={{ width: 80, height: 80 }}>
                    {profileData.personalInfo.firstName[0]}{profileData.personalInfo.lastName[0]}
                  </Avatar>
                  <Box flex={1}>
                    <Typography variant="h5">
                      {profileData.personalInfo.firstName} {profileData.personalInfo.lastName}
                    </Typography>
                    <Typography variant="subtitle1" color="text.secondary">
                      {profileData.personalInfo.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {profileData.personalInfo.company}
                    </Typography>
                    <Box mt={1} display="flex" flexWrap="wrap" gap={1}>
                      <Chip 
                        label={`Rank #${profileData.performance.currentRanking}`} 
                        color="primary" 
                        size="small" 
                      />
                      <Chip 
                        label={`${profileData.performance.successRate}% Success Rate`} 
                        color="success" 
                        size="small" 
                      />
                      {psychologicalState && (
                        <Chip 
                          label={`${psychologicalState.archetype} Archetype`} 
                          color="secondary" 
                          size="small"
                          icon={<BrainIcon />}
                        />
                      )}
                      {demoMode && (
                        <Chip 
                          label="DEMO MODE" 
                          color="warning" 
                          size="small"
                          icon={<DemoIcon />}
                        />
                      )}
                    </Box>
                  </Box>
                  <IconButton>
                    <EditIcon />
                  </IconButton>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Psychological Insights Panel */}
          {psychologicalState && (
            <Grid item xs={12}>
              <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
                <CardContent>
                  <Box display="flex" alignItems="center" gap={2} mb={2}>
                    <BrainIcon sx={{ fontSize: 32 }} />
                    <Typography variant="h6">
                      {demoMode ? 'Demo: ' : ''}Psychological Analysis
                    </Typography>
                    <Chip 
                      label={demoMode ? "Demo Data" : "Live Data"} 
                      color={demoMode ? "warning" : "success"} 
                      size="small" 
                    />
                  </Box>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={3}>
                      <Box textAlign="center">
                        <Typography variant="caption">Confidence Level</Typography>
                        <Typography variant="h4" fontWeight="bold">
                          {Math.round((psychologicalState.confidenceLevel || 0) * 100)}%
                        </Typography>
                      </Box>
                    </Grid>
                    
                    <Grid item xs={12} md={3}>
                      <Box textAlign="center">
                        <Typography variant="caption">Engagement Level</Typography>
                        <Typography variant="h4" fontWeight="bold">
                          {Math.round((psychologicalState.engagementLevel || 0) * 100)}%
                        </Typography>
                      </Box>
                    </Grid>
                    
                    <Grid item xs={12} md={3}>
                      <Box textAlign="center">
                        <Typography variant="caption">Stress Level</Typography>
                        <Typography variant="h4" fontWeight="bold">
                          {Math.round((psychologicalState.stressLevel || 0) * 100)}%
                        </Typography>
                      </Box>
                    </Grid>
                    
                    <Grid item xs={12} md={3}>
                      <Box textAlign="center">
                        <Typography variant="caption">Cognitive Load</Typography>
                        <Typography variant="h4" fontWeight="bold">
                          {Math.round((psychologicalState.cognitiveLoad || 0) * 100)}%
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          )}

          {/* Profile Tabs */}
          <Grid item xs={12}>
            <Card>
              <Tabs value={activeTab} onChange={handleTabChange}>
                <Tab label="Overview" />
                <Tab label="🧠 Psychology" />
                <Tab label="AI Insights" />
                {demoMode && <Tab label="🎭 Demo Controls" />}
              </Tabs>
              
              <CardContent>
                {activeTab === 0 && (
                  <Typography>Enhanced overview with {demoMode ? 'demo' : 'production'} data...</Typography>
                )}
                
                {activeTab === 1 && psychologicalState && (
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      🧠 Psychological Profile & Behavioral Analysis
                    </Typography>
                    <Typography variant="body1">
                      Archetype: <strong>{psychologicalState.archetype}</strong>
                    </Typography>
                    <Typography variant="body1">
                      Decision Style: <strong>{psychologicalState.decisionMakingStyle}</strong>
                    </Typography>
                    <Typography variant="body1">
                      Risk Tolerance: <strong>{psychologicalState.riskTolerance}</strong>
                    </Typography>
                  </Box>
                )}
                
                {activeTab === 2 && (
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      🤖 AI-Powered Insights & Recommendations
                    </Typography>
                    {adaptiveRecommendations.map((recommendation, index) => (
                      <Alert key={index} severity="info" sx={{ mb: 1 }}>
                        {recommendation}
                      </Alert>
                    ))}
                  </Box>
                )}

                {activeTab === 3 && demoMode && (
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      🎭 Demo Configuration
                    </Typography>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={showAdaptations}
                          onChange={(e) => setShowAdaptations(e.target.checked)}
                        />
                      }
                      label="Show UI Adaptation Indicators"
                    />
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </WrapperComponent>
  );
};

export default EnhancedBidderProfile;
