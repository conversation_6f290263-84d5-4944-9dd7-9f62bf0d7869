/**
 * Real-time Collaboration System with Psychological Awareness
 * Team collaboration that adapts to stress levels and manages team dynamics
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Avatar,
  AvatarGroup,
  Chip,
  Button,
  TextField,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Badge,
  Alert,
  LinearProgress,
  Tabs,
  Tab,
  Paper,
  Stack,
  Divider,
  Tooltip,
  Collapse,
  Menu,
  MenuItem
} from '@mui/material';
import {
  Group as TeamIcon,
  Psychology as PsychologyIcon,
  Chat as ChatIcon,
  VideoCall as VideoIcon,
  Assignment as TaskIcon,
  Schedule as ScheduleIcon,
  Notifications as NotificationIcon,
  MoreVert as MoreIcon,
  Send as SendIcon,
  AttachFile as AttachIcon,
  Mood as MoodIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Timer as TimerIcon,
  TrendingUp as ProductivityIcon,
  Support as SupportIcon,
  AutoAwesome as AIIcon,
  Lightbulb as InsightIcon,
  ExpandMore as ExpandIcon,
  ExpandLess as CollapseIcon
} from '@mui/icons-material';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';

interface TeamMember {
  id: string;
  name: string;
  avatar: string;
  role: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  psychologicalState: {
    stressLevel: number;
    engagementLevel: number;
    cognitiveLoad: number;
    mood: 'positive' | 'neutral' | 'negative';
  };
  currentTask?: string;
  productivity: number; // 0-1
  lastActive: string;
  workingHours: string;
  timezone: string;
}

interface CollaborationMessage {
  id: string;
  senderId: string;
  senderName: string;
  content: string;
  timestamp: string;
  type: 'text' | 'file' | 'task' | 'system' | 'ai_insight';
  reactions?: { emoji: string; users: string[] }[];
  priority: 'low' | 'medium' | 'high';
  psychologicalTone: 'supportive' | 'neutral' | 'urgent' | 'celebratory';
}

interface TeamInsight {
  id: string;
  type: 'stress_alert' | 'productivity_boost' | 'collaboration_tip' | 'workload_balance';
  title: string;
  description: string;
  affectedMembers: string[];
  recommendation: string;
  urgency: 'low' | 'medium' | 'high';
  aiGenerated: boolean;
}

const TeamCollaboration: React.FC = () => {
  // Behavioral tracking
  const {
    psychologicalState,
    isStressed,
    isHighCognitiveLoad,
    needsSimplification,
    trackEngagement
  } = useNeuroMarketing();

  // State management
  const [selectedTab, setSelectedTab] = useState(0);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [messages, setMessages] = useState<CollaborationMessage[]>([]);
  const [teamInsights, setTeamInsights] = useState<TeamInsight[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [showTeamInsights, setShowTeamInsights] = useState(true);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Load team data
  useEffect(() => {
    loadTeamData();
    loadMessages();
    generateTeamInsights();
  }, []);

  // Auto-scroll to latest message
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Monitor team psychological state
  useEffect(() => {
    const interval = setInterval(() => {
      updateTeamPsychologicalStates();
      generateTeamInsights();
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const loadTeamData = () => {
    const mockTeamMembers: TeamMember[] = [
      {
        id: 'member-001',
        name: 'Sarah Johnson',
        avatar: '/avatars/sarah.jpg',
        role: 'Project Manager',
        status: 'online',
        psychologicalState: {
          stressLevel: 0.3,
          engagementLevel: 0.8,
          cognitiveLoad: 0.6,
          mood: 'positive'
        },
        currentTask: 'Reviewing compliance documents',
        productivity: 0.85,
        lastActive: new Date().toISOString(),
        workingHours: '08:00-17:00',
        timezone: 'SAST'
      },
      {
        id: 'member-002',
        name: 'Mike Chen',
        avatar: '/avatars/mike.jpg',
        role: 'Technical Lead',
        status: 'busy',
        psychologicalState: {
          stressLevel: 0.7,
          engagementLevel: 0.6,
          cognitiveLoad: 0.9,
          mood: 'neutral'
        },
        currentTask: 'Technical proposal writing',
        productivity: 0.7,
        lastActive: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
        workingHours: '09:00-18:00',
        timezone: 'SAST'
      },
      {
        id: 'member-003',
        name: 'Lisa Williams',
        avatar: '/avatars/lisa.jpg',
        role: 'Business Analyst',
        status: 'online',
        psychologicalState: {
          stressLevel: 0.4,
          engagementLevel: 0.9,
          cognitiveLoad: 0.5,
          mood: 'positive'
        },
        currentTask: 'Market research analysis',
        productivity: 0.92,
        lastActive: new Date().toISOString(),
        workingHours: '08:30-17:30',
        timezone: 'SAST'
      }
    ];

    setTeamMembers(mockTeamMembers);
  };

  const loadMessages = () => {
    const mockMessages: CollaborationMessage[] = [
      {
        id: 'msg-001',
        senderId: 'member-001',
        senderName: 'Sarah Johnson',
        content: 'Good morning team! Let\'s review our progress on the government tender bid.',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        type: 'text',
        priority: 'medium',
        psychologicalTone: 'supportive'
      },
      {
        id: 'msg-002',
        senderId: 'ai-assistant',
        senderName: 'AI Assistant',
        content: '🧠 Team Insight: Mike\'s stress level is elevated. Consider redistributing some technical tasks or scheduling a break.',
        timestamp: new Date(Date.now() - 1800000).toISOString(),
        type: 'ai_insight',
        priority: 'high',
        psychologicalTone: 'supportive'
      },
      {
        id: 'msg-003',
        senderId: 'member-003',
        senderName: 'Lisa Williams',
        content: 'I\'ve completed the market analysis. The data looks very promising for our bid! 📊',
        timestamp: new Date(Date.now() - 900000).toISOString(),
        type: 'text',
        priority: 'medium',
        psychologicalTone: 'celebratory',
        reactions: [{ emoji: '🎉', users: ['member-001', 'member-002'] }]
      }
    ];

    setMessages(mockMessages);
  };

  const generateTeamInsights = () => {
    const insights: TeamInsight[] = [];

    // Check for high stress levels
    const stressedMembers = teamMembers.filter(member => member.psychologicalState.stressLevel > 0.6);
    if (stressedMembers.length > 0) {
      insights.push({
        id: 'insight-stress',
        type: 'stress_alert',
        title: 'Team Stress Alert',
        description: `${stressedMembers.length} team member(s) showing elevated stress levels`,
        affectedMembers: stressedMembers.map(m => m.id),
        recommendation: 'Consider redistributing workload or scheduling team breaks',
        urgency: 'high',
        aiGenerated: true
      });
    }

    // Check for productivity imbalance
    const avgProductivity = teamMembers.reduce((sum, member) => sum + member.productivity, 0) / teamMembers.length;
    const lowProductivityMembers = teamMembers.filter(member => member.productivity < avgProductivity - 0.2);
    if (lowProductivityMembers.length > 0) {
      insights.push({
        id: 'insight-productivity',
        type: 'productivity_boost',
        title: 'Productivity Optimization',
        description: 'Some team members could benefit from task reallocation',
        affectedMembers: lowProductivityMembers.map(m => m.id),
        recommendation: 'Pair high-performing members with those needing support',
        urgency: 'medium',
        aiGenerated: true
      });
    }

    // Check for collaboration opportunities
    const highEngagementMembers = teamMembers.filter(member => member.psychologicalState.engagementLevel > 0.8);
    if (highEngagementMembers.length >= 2) {
      insights.push({
        id: 'insight-collaboration',
        type: 'collaboration_tip',
        title: 'Collaboration Opportunity',
        description: 'High engagement levels detected - perfect time for brainstorming',
        affectedMembers: highEngagementMembers.map(m => m.id),
        recommendation: 'Schedule a creative collaboration session',
        urgency: 'low',
        aiGenerated: true
      });
    }

    setTeamInsights(insights);
  };

  const updateTeamPsychologicalStates = () => {
    // Simulate real-time psychological state updates
    setTeamMembers(prev => prev.map(member => ({
      ...member,
      psychologicalState: {
        ...member.psychologicalState,
        stressLevel: Math.max(0, Math.min(1, member.psychologicalState.stressLevel + (Math.random() - 0.5) * 0.1)),
        engagementLevel: Math.max(0, Math.min(1, member.psychologicalState.engagementLevel + (Math.random() - 0.5) * 0.1)),
        cognitiveLoad: Math.max(0, Math.min(1, member.psychologicalState.cognitiveLoad + (Math.random() - 0.5) * 0.1))
      }
    })));
  };

  const sendMessage = () => {
    if (!newMessage.trim()) return;

    const message: CollaborationMessage = {
      id: `msg-${Date.now()}`,
      senderId: 'current-user',
      senderName: 'You',
      content: newMessage,
      timestamp: new Date().toISOString(),
      type: 'text',
      priority: 'medium',
      psychologicalTone: isStressed ? 'urgent' : 'neutral'
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');

    trackEngagement('team_message_sent', {
      messageLength: newMessage.length,
      psychologicalState
    });
  };

  const getMoodIcon = (mood: string) => {
    switch (mood) {
      case 'positive': return '😊';
      case 'negative': return '😟';
      default: return '😐';
    }
  };

  const getStressColor = (stressLevel: number) => {
    if (stressLevel > 0.7) return 'error';
    if (stressLevel > 0.4) return 'warning';
    return 'success';
  };

  const renderTeamMember = (member: TeamMember) => (
    <Card key={member.id} sx={{ mb: 2 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Badge
            overlap="circular"
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            badgeContent={
              <Box
                sx={{
                  width: 12,
                  height: 12,
                  borderRadius: '50%',
                  bgcolor: member.status === 'online' ? 'success.main' : 
                          member.status === 'busy' ? 'warning.main' : 'grey.500',
                  border: '2px solid white'
                }}
              />
            }
          >
            <Avatar src={member.avatar} sx={{ width: 48, height: 48 }}>
              {member.name.charAt(0)}
            </Avatar>
          </Badge>
          
          <Box sx={{ ml: 2, flexGrow: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
              {member.name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {member.role}
            </Typography>
            {member.currentTask && (
              <Typography variant="caption" color="text.secondary">
                📋 {member.currentTask}
              </Typography>
            )}
          </Box>

          <IconButton
            onClick={(e) => {
              setMenuAnchor(e.currentTarget);
              setSelectedMember(member);
            }}
          >
            <MoreIcon />
          </IconButton>
        </Box>

        {/* Psychological State Indicators */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
            Psychological State {getMoodIcon(member.psychologicalState.mood)}
          </Typography>
          
          <Grid container spacing={1}>
            <Grid item xs={4}>
              <Typography variant="caption">Stress</Typography>
              <LinearProgress
                variant="determinate"
                value={member.psychologicalState.stressLevel * 100}
                color={getStressColor(member.psychologicalState.stressLevel)}
                sx={{ height: 6, borderRadius: 3 }}
              />
            </Grid>
            
            <Grid item xs={4}>
              <Typography variant="caption">Engagement</Typography>
              <LinearProgress
                variant="determinate"
                value={member.psychologicalState.engagementLevel * 100}
                color="info"
                sx={{ height: 6, borderRadius: 3 }}
              />
            </Grid>
            
            <Grid item xs={4}>
              <Typography variant="caption">Cognitive Load</Typography>
              <LinearProgress
                variant="determinate"
                value={member.psychologicalState.cognitiveLoad * 100}
                color="secondary"
                sx={{ height: 6, borderRadius: 3 }}
              />
            </Grid>
          </Grid>
        </Box>

        {/* Productivity and Status */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Chip
            label={`${Math.round(member.productivity * 100)}% Productive`}
            color={member.productivity > 0.8 ? 'success' : member.productivity > 0.6 ? 'warning' : 'error'}
            size="small"
          />
          
          <Typography variant="caption" color="text.secondary">
            {member.workingHours} {member.timezone}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );

  const renderMessage = (message: CollaborationMessage) => (
    <ListItem key={message.id} sx={{ alignItems: 'flex-start', px: 0 }}>
      <ListItemAvatar>
        <Avatar sx={{ bgcolor: message.senderId === 'ai-assistant' ? 'primary.main' : 'grey.500' }}>
          {message.senderId === 'ai-assistant' ? <AIIcon /> : message.senderName.charAt(0)}
        </Avatar>
      </ListItemAvatar>
      
      <ListItemText
        primary={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
              {message.senderName}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {new Date(message.timestamp).toLocaleTimeString()}
            </Typography>
            {message.type === 'ai_insight' && (
              <Chip label="AI Insight" size="small" color="primary" />
            )}
          </Box>
        }
        secondary={
          <Box>
            <Typography variant="body2" sx={{ mb: 1 }}>
              {message.content}
            </Typography>
            
            {message.reactions && (
              <Box sx={{ display: 'flex', gap: 0.5 }}>
                {message.reactions.map((reaction, index) => (
                  <Chip
                    key={index}
                    label={`${reaction.emoji} ${reaction.users.length}`}
                    size="small"
                    variant="outlined"
                    onClick={() => {/* Handle reaction */}}
                  />
                ))}
              </Box>
            )}
          </Box>
        }
      />
    </ListItem>
  );

  const renderTeamInsights = () => (
    <Card sx={{ mb: 3 }}>
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <PsychologyIcon sx={{ mr: 1 }} />
            Team Psychological Insights
          </Box>
        }
        action={
          <IconButton onClick={() => setShowTeamInsights(!showTeamInsights)}>
            {showTeamInsights ? <CollapseIcon /> : <ExpandIcon />}
          </IconButton>
        }
      />
      
      <Collapse in={showTeamInsights}>
        <CardContent>
          {teamInsights.length === 0 ? (
            <Typography variant="body2" color="text.secondary">
              🎉 Team is performing well! No immediate concerns detected.
            </Typography>
          ) : (
            <Stack spacing={2}>
              {teamInsights.map((insight) => (
                <Alert
                  key={insight.id}
                  severity={insight.urgency === 'high' ? 'error' : insight.urgency === 'medium' ? 'warning' : 'info'}
                  icon={insight.aiGenerated ? <AIIcon /> : <InsightIcon />}
                >
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                    {insight.title}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    {insight.description}
                  </Typography>
                  <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
                    💡 Recommendation: {insight.recommendation}
                  </Typography>
                </Alert>
              ))}
            </Stack>
          )}
        </CardContent>
      </Collapse>
    </Card>
  );

  return (
    <Box sx={{ maxWidth: 1400, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Card elevation={2} sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={8}>
              <Typography variant={isHighCognitiveLoad ? "h4" : "h5"} sx={{ fontWeight: 'bold', mb: 1 }}>
                🤝 Team Collaboration Hub
              </Typography>
              <Typography variant="body1" color="text.secondary">
                {needsSimplification 
                  ? 'Work together with your team'
                  : 'Real-time collaboration with psychological awareness and team optimization'
                }
              </Typography>
              
              <Box sx={{ mt: 2 }}>
                <AvatarGroup max={4}>
                  {teamMembers.map((member) => (
                    <Avatar key={member.id} src={member.avatar}>
                      {member.name.charAt(0)}
                    </Avatar>
                  ))}
                </AvatarGroup>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Stack spacing={1}>
                <Button
                  variant="contained"
                  startIcon={<VideoIcon />}
                  fullWidth
                  onClick={() => trackEngagement('start_video_call', {})}
                >
                  Start Video Call
                </Button>
                
                {!needsSimplification && (
                  <Button
                    variant="outlined"
                    startIcon={<PsychologyIcon />}
                    fullWidth
                    onClick={() => trackEngagement('team_psychology_report', {})}
                  >
                    Team Psychology Report
                  </Button>
                )}
              </Stack>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Team Insights */}
      {renderTeamInsights()}

      <Grid container spacing={3}>
        {/* Team Members */}
        <Grid item xs={12} md={4}>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <TeamIcon sx={{ mr: 1 }} />
            Team Members ({teamMembers.length})
          </Typography>
          
          {teamMembers.map(renderTeamMember)}
        </Grid>

        {/* Chat and Collaboration */}
        <Grid item xs={12} md={8}>
          <Card sx={{ height: 600, display: 'flex', flexDirection: 'column' }}>
            <CardHeader
              title="Team Chat"
              action={
                <Chip 
                  label={`${teamMembers.filter(m => m.status === 'online').length} online`}
                  color="success"
                  size="small"
                />
              }
            />
            
            <CardContent sx={{ flexGrow: 1, overflow: 'auto' }}>
              <List>
                {messages.map(renderMessage)}
                <div ref={messagesEndRef} />
              </List>
            </CardContent>
            
            <Divider />
            
            <Box sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <TextField
                  fullWidth
                  placeholder={isStressed ? "Take your time typing..." : "Type a message..."}
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                  size="small"
                />
                
                <IconButton onClick={sendMessage} disabled={!newMessage.trim()}>
                  <SendIcon />
                </IconButton>
                
                <IconButton>
                  <AttachIcon />
                </IconButton>
              </Box>
            </Box>
          </Card>
        </Grid>
      </Grid>

      {/* Member Actions Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
      >
        <MenuItem onClick={() => {/* Direct message */}}>
          <ChatIcon sx={{ mr: 1 }} />
          Direct Message
        </MenuItem>
        <MenuItem onClick={() => {/* Video call */}}>
          <VideoIcon sx={{ mr: 1 }} />
          Video Call
        </MenuItem>
        <MenuItem onClick={() => {/* Assign task */}}>
          <TaskIcon sx={{ mr: 1 }} />
          Assign Task
        </MenuItem>
        <MenuItem onClick={() => {/* View psychology */}}>
          <PsychologyIcon sx={{ mr: 1 }} />
          Psychology Report
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default TeamCollaboration;
