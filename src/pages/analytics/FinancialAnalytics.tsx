/**
 * Financial Analytics Page
 * Revenue, profit, ROI analysis and financial performance tracking
 */

import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Chip,
  LinearProgress,
  Alert,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Divider
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  MonetizationOn,
  Assessment,
  AccountBalance,
  PieChart,
  ShowChart,
  Download,
  Refresh,
  AttachMoney,
  Savings,
  TrendingFlat
} from '@mui/icons-material';
import { useGetBidPerformanceQuery } from '../../services/api/analytics.api';

const FinancialAnalytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'quarter' | 'year'>('quarter');
  const [userId] = useState('demo'); // Get from auth context

  const { 
    data: performance, 
    isLoading,
    error,
    refetch 
  } = useGetBidPerformanceQuery({ userId, timeRange });

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous) return <TrendingUp color="success" />;
    if (current < previous) return <TrendingDown color="error" />;
    return <TrendingFlat color="info" />;
  };

  const getTrendColor = (current: number, previous: number) => {
    if (current > previous) return 'success';
    if (current < previous) return 'error';
    return 'info';
  };

  // Mock previous period data for trend analysis
  const previousPeriodData = {
    revenue: performance ? performance.revenue * 0.85 : 0,
    profit: performance ? performance.profit * 0.78 : 0,
    roi: performance ? performance.roi * 0.92 : 0,
    profit_margin: performance ? performance.profit_margin * 0.88 : 0,
  };

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load financial analytics. Please try again later.
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
            💰 Financial Analytics
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Revenue, profit, and ROI analysis for your bidding performance
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              label="Time Range"
              onChange={(e) => setTimeRange(e.target.value as any)}
            >
              <MenuItem value="week">Last Week</MenuItem>
              <MenuItem value="month">Last Month</MenuItem>
              <MenuItem value="quarter">Last Quarter</MenuItem>
              <MenuItem value="year">Last Year</MenuItem>
            </Select>
          </FormControl>

          <Button
            variant="outlined"
            startIcon={<Download />}
            onClick={() => console.log('Export financial report')}
          >
            Export Report
          </Button>

          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={() => refetch()}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {/* Loading State */}
      {isLoading && (
        <Box sx={{ mb: 3 }}>
          <LinearProgress />
          <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
            Loading financial data...
          </Typography>
        </Box>
      )}

      {performance && (
        <>
          {/* Key Financial Metrics */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <MonetizationOn color="success" />
                    <Typography variant="h6">Total Revenue</Typography>
                  </Box>
                  <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
                    {formatCurrency(performance.revenue)}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getTrendIcon(performance.revenue, previousPeriodData.revenue)}
                    <Typography 
                      variant="body2" 
                      color={`${getTrendColor(performance.revenue, previousPeriodData.revenue)}.main`}
                    >
                      {formatPercentage(((performance.revenue - previousPeriodData.revenue) / previousPeriodData.revenue) * 100)} vs previous
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <Savings color="primary" />
                    <Typography variant="h6">Net Profit</Typography>
                  </Box>
                  <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
                    {formatCurrency(performance.profit)}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getTrendIcon(performance.profit, previousPeriodData.profit)}
                    <Typography 
                      variant="body2" 
                      color={`${getTrendColor(performance.profit, previousPeriodData.profit)}.main`}
                    >
                      {formatPercentage(((performance.profit - previousPeriodData.profit) / previousPeriodData.profit) * 100)} vs previous
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <ShowChart color="info" />
                    <Typography variant="h6">ROI</Typography>
                  </Box>
                  <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
                    {formatPercentage(performance.roi)}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getTrendIcon(performance.roi, previousPeriodData.roi)}
                    <Typography 
                      variant="body2" 
                      color={`${getTrendColor(performance.roi, previousPeriodData.roi)}.main`}
                    >
                      {formatPercentage(((performance.roi - previousPeriodData.roi) / previousPeriodData.roi) * 100)} vs previous
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <PieChart color="warning" />
                    <Typography variant="h6">Profit Margin</Typography>
                  </Box>
                  <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
                    {formatPercentage(performance.profit_margin)}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getTrendIcon(performance.profit_margin, previousPeriodData.profit_margin)}
                    <Typography 
                      variant="body2" 
                      color={`${getTrendColor(performance.profit_margin, previousPeriodData.profit_margin)}.main`}
                    >
                      {formatPercentage(((performance.profit_margin - previousPeriodData.profit_margin) / previousPeriodData.profit_margin) * 100)} vs previous
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Financial Breakdown */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <Paper>
                <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
                  <Typography variant="h6">💼 Revenue Breakdown</Typography>
                </Box>
                <CardContent>
                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Total Contract Value:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {formatCurrency(performance.total_value)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Won Contract Value:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600, color: 'success.main' }}>
                        {formatCurrency(performance.won_value)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Average Bid Value:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {formatCurrency(performance.average_bid_value)}
                      </Typography>
                    </Box>
                    <Divider sx={{ my: 1 }} />
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>Revenue per Bid:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600, color: 'primary.main' }}>
                        {formatCurrency(performance.revenue_per_bid)}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Paper>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper>
                <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
                  <Typography variant="h6">📊 Cost Analysis</Typography>
                </Box>
                <CardContent>
                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Cost per Bid:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {formatCurrency(performance.cost_per_bid)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Total Costs:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600, color: 'error.main' }}>
                        {formatCurrency(performance.revenue - performance.profit)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Cost Efficiency:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {formatPercentage((performance.profit / performance.revenue) * 100)}
                      </Typography>
                    </Box>
                    <Divider sx={{ my: 1 }} />
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>Net Profit:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600, color: 'success.main' }}>
                        {formatCurrency(performance.profit)}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Paper>
            </Grid>
          </Grid>

          {/* Economic Impact */}
          <Paper sx={{ mb: 3 }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">🌍 Economic Impact</Typography>
              <Typography variant="body2" color="text.secondary">
                Your contribution to the economy through successful bids
              </Typography>
            </Box>
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h3" sx={{ fontWeight: 600, color: 'success.main', mb: 1 }}>
                      {performance.jobs_created}
                    </Typography>
                    <Typography variant="h6" sx={{ mb: 1 }}>Jobs Created</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Direct and indirect employment opportunities
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h3" sx={{ fontWeight: 600, color: 'primary.main', mb: 1 }}>
                      {formatCurrency(performance.economic_value)}
                    </Typography>
                    <Typography variant="h6" sx={{ mb: 1 }}>Economic Value</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total economic contribution to society
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h3" sx={{ fontWeight: 600, color: 'warning.main', mb: 1 }}>
                      {formatPercentage((performance.economic_value / performance.revenue) * 100)}
                    </Typography>
                    <Typography variant="h6" sx={{ mb: 1 }}>Impact Ratio</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Economic value vs revenue generated
                    </Typography>
                  </Box>
                </Grid>
              </Grid>

              <Alert severity="success" sx={{ mt: 3 }}>
                <Typography variant="subtitle2">🏆 Community Impact Achievement</Typography>
                <Typography variant="body2">
                  Your bidding activities have created {performance.jobs_created} jobs and contributed {formatCurrency(performance.economic_value)} to the economy. 
                  You're making a real difference in your community!
                </Typography>
              </Alert>
            </CardContent>
          </Paper>

          {/* Financial Insights */}
          <Paper>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">💡 Financial Insights</Typography>
            </Box>
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Alert severity={performance.profit_margin > 20 ? 'success' : performance.profit_margin > 10 ? 'info' : 'warning'}>
                    <Typography variant="subtitle2">Profit Margin Analysis</Typography>
                    <Typography variant="body2">
                      {performance.profit_margin > 20 
                        ? 'Excellent profit margins! Your pricing strategy is very effective.'
                        : performance.profit_margin > 10 
                        ? 'Good profit margins. Consider optimizing costs for better returns.'
                        : 'Low profit margins. Review pricing strategy and cost management.'
                      }
                    </Typography>
                  </Alert>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Alert severity={performance.roi > 25 ? 'success' : performance.roi > 15 ? 'info' : 'warning'}>
                    <Typography variant="subtitle2">ROI Performance</Typography>
                    <Typography variant="body2">
                      {performance.roi > 25 
                        ? 'Outstanding ROI! Your investment in bidding is paying off excellently.'
                        : performance.roi > 15 
                        ? 'Good ROI. Your bidding investments are generating solid returns.'
                        : 'ROI could be improved. Consider focusing on higher-value opportunities.'
                      }
                    </Typography>
                  </Alert>
                </Grid>

                <Grid item xs={12}>
                  <Alert severity="info">
                    <Typography variant="subtitle2">💰 Revenue Optimization Tip</Typography>
                    <Typography variant="body2">
                      Based on your current performance, focusing on bids with values between {formatCurrency(performance.average_bid_value * 0.8)} and {formatCurrency(performance.average_bid_value * 1.5)} 
                      could optimize your revenue per bid ratio while maintaining your success rate.
                    </Typography>
                  </Alert>
                </Grid>
              </Grid>
            </CardContent>
          </Paper>
        </>
      )}

      {/* No Data State */}
      {!isLoading && !performance && (
        <Alert severity="info" sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" sx={{ mb: 1 }}>
            No Financial Data Available
          </Typography>
          <Typography variant="body2">
            Start bidding and winning contracts to see your financial analytics.
          </Typography>
        </Alert>
      )}
    </Box>
  );
};

export default FinancialAnalytics;
