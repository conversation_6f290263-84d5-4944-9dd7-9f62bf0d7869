/**
 * Behavioral Analytics Dashboard
 * Provides psychological insights and behavioral pattern recognition
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Tabs,
  Tab,
  Stack,
  Divider,
  Paper,
  CircularProgress
} from '@mui/material';
import {
  Psychology as PsychologyIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  EmojiEvents as AchievementIcon,
  Timer as TimerIcon,
  Lightbulb as InsightIcon,
  AutoAwesome as AIIcon,
  BarChart as ChartIcon,
  Mood as MoodIcon,
  Speed as PerformanceIcon,
  Warning as WarningIcon,
  CheckCircle as SuccessIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';

interface BehavioralMetrics {
  averageStressLevel: number;
  averageCognitiveLoad: number;
  averageEngagementLevel: number;
  optimalWorkingHours: string[];
  productivityPeaks: string[];
  stressTriggers: string[];
  motivationFactors: string[];
  decisionPatterns: {
    quickDecisions: number;
    deliberateDecisions: number;
    averageDecisionTime: number;
  };
  behavioralTrends: {
    weeklyStress: number[];
    weeklyEngagement: number[];
    weeklyProductivity: number[];
  };
}

interface PsychologicalInsight {
  id: string;
  type: 'positive' | 'warning' | 'recommendation';
  title: string;
  description: string;
  actionable: boolean;
  priority: 'low' | 'medium' | 'high';
  category: 'stress' | 'productivity' | 'engagement' | 'decision-making';
}

const BehavioralAnalytics: React.FC = () => {
  // Behavioral tracking
  const {
    psychologicalState
  } = useNeuroMarketing();

  // State management
  const [selectedTab, setSelectedTab] = useState(0);
  const [behavioralMetrics, setBehavioralMetrics] = useState<BehavioralMetrics | null>(null);
  const [insights, setInsights] = useState<PsychologicalInsight[]>([]);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'quarter'>('week');

  // Load analytics data
  useEffect(() => {
    loadBehavioralAnalytics();
  }, [timeRange]);

  // Track page visit
  useEffect(() => {
    // Track engagement would be implemented here
    console.log('Behavioral analytics viewed', {
      psychologicalState,
      timeRange
    });
  }, [psychologicalState, timeRange]);

  const loadBehavioralAnalytics = async () => {
    try {
      setLoading(true);
      
      // Mock behavioral metrics
      const mockMetrics: BehavioralMetrics = {
        averageStressLevel: 0.45,
        averageCognitiveLoad: 0.62,
        averageEngagementLevel: 0.78,
        optimalWorkingHours: ['09:00-11:00', '14:00-16:00'],
        productivityPeaks: ['Tuesday 10:00', 'Thursday 15:00'],
        stressTriggers: ['Complex compliance requirements', 'Tight deadlines', 'Document verification'],
        motivationFactors: ['Achievement recognition', 'Financial rewards', 'Skill development'],
        decisionPatterns: {
          quickDecisions: 65,
          deliberateDecisions: 35,
          averageDecisionTime: 2.3
        },
        behavioralTrends: {
          weeklyStress: [0.3, 0.4, 0.6, 0.5, 0.7, 0.4, 0.2],
          weeklyEngagement: [0.8, 0.7, 0.6, 0.8, 0.5, 0.9, 0.8],
          weeklyProductivity: [0.7, 0.8, 0.6, 0.9, 0.5, 0.8, 0.9]
        }
      };

      setBehavioralMetrics(mockMetrics);
      generateInsights(mockMetrics);
      
    } catch (error) {
      console.error('Failed to load behavioral analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateInsights = (metrics: BehavioralMetrics) => {
    const generatedInsights: PsychologicalInsight[] = [];

    // Stress analysis
    if (metrics.averageStressLevel > 0.6) {
      generatedInsights.push({
        id: 'stress-high',
        type: 'warning',
        title: 'High Stress Levels Detected',
        description: 'Your average stress level is above optimal. Consider taking more breaks and using stress-reduction techniques.',
        actionable: true,
        priority: 'high',
        category: 'stress'
      });
    } else if (metrics.averageStressLevel < 0.3) {
      generatedInsights.push({
        id: 'stress-optimal',
        type: 'positive',
        title: 'Excellent Stress Management',
        description: 'You\'re maintaining healthy stress levels. Keep up the great work!',
        actionable: false,
        priority: 'low',
        category: 'stress'
      });
    }

    // Engagement analysis
    if (metrics.averageEngagementLevel > 0.7) {
      generatedInsights.push({
        id: 'engagement-high',
        type: 'positive',
        title: 'High Engagement Levels',
        description: 'You\'re highly engaged with the platform. This correlates with better bid success rates.',
        actionable: false,
        priority: 'low',
        category: 'engagement'
      });
    }

    // Productivity recommendations
    generatedInsights.push({
      id: 'productivity-timing',
      type: 'recommendation',
      title: 'Optimize Your Schedule',
      description: `Your peak productivity hours are ${metrics.optimalWorkingHours.join(' and ')}. Schedule important tasks during these times.`,
      actionable: true,
      priority: 'medium',
      category: 'productivity'
    });

    // Decision-making patterns
    if (metrics.decisionPatterns.averageDecisionTime > 3) {
      generatedInsights.push({
        id: 'decision-slow',
        type: 'recommendation',
        title: 'Decision Speed Optimization',
        description: 'You tend to take longer on decisions. Consider using AI recommendations for routine choices.',
        actionable: true,
        priority: 'medium',
        category: 'decision-making'
      });
    }

    setInsights(generatedInsights);
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'positive':
        return <SuccessIcon color="success" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'recommendation':
        return <InsightIcon color="info" />;
      default:
        return <InsightIcon />;
    }
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'positive':
        return 'success';
      case 'warning':
        return 'warning';
      case 'recommendation':
        return 'info';
      default:
        return 'default';
    }
  };

  const renderOverviewTab = () => (
    <Grid container spacing={3}>
      {/* Key Metrics */}
      <Grid item xs={12}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Card elevation={2}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <MoodIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">Stress Level</Typography>
                </Box>
                <Typography variant="h3" color={(behavioralMetrics?.averageStressLevel || 0) > 0.6 ? 'error.main' : 'success.main'}>
                  {Math.round((behavioralMetrics?.averageStressLevel || 0) * 100)}%
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={(behavioralMetrics?.averageStressLevel || 0) * 100}
                  color={(behavioralMetrics?.averageStressLevel || 0) > 0.6 ? 'error' : 'success'}
                  sx={{ mt: 1 }}
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card elevation={2}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <PsychologyIcon color="info" sx={{ mr: 1 }} />
                  <Typography variant="h6">Cognitive Load</Typography>
                </Box>
                <Typography variant="h3" color="info.main">
                  {Math.round((behavioralMetrics?.averageCognitiveLoad || 0) * 100)}%
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={(behavioralMetrics?.averageCognitiveLoad || 0) * 100}
                  color="info"
                  sx={{ mt: 1 }}
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card elevation={2}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <TrendingUpIcon color="success" sx={{ mr: 1 }} />
                  <Typography variant="h6">Engagement</Typography>
                </Box>
                <Typography variant="h3" color="success.main">
                  {Math.round((behavioralMetrics?.averageEngagementLevel || 0) * 100)}%
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={(behavioralMetrics?.averageEngagementLevel || 0) * 100}
                  color="success"
                  sx={{ mt: 1 }}
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card elevation={2}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <TimerIcon color="warning" sx={{ mr: 1 }} />
                  <Typography variant="h6">Decision Speed</Typography>
                </Box>
                <Typography variant="h3" color="warning.main">
                  {(behavioralMetrics?.decisionPatterns.averageDecisionTime || 0).toFixed(1)}s
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Average decision time
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Grid>

      {/* Insights */}
      <Grid item xs={12} md={8}>
        <Card elevation={2}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
              <AIIcon sx={{ mr: 1 }} />
              AI-Generated Insights
            </Typography>
            
            <List>
              {insights.map((insight) => (
                <ListItem key={insight.id} sx={{ px: 0 }}>
                  <ListItemIcon>
                    {getInsightIcon(insight.type)}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                          {insight.title}
                        </Typography>
                        <Chip 
                          label={insight.priority.toUpperCase()}
                          size="small"
                          color={insight.priority === 'high' ? 'error' : insight.priority === 'medium' ? 'warning' : 'default'}
                        />
                      </Box>
                    }
                    secondary={insight.description}
                  />
                </ListItem>
              ))}
            </List>
          </CardContent>
        </Card>
      </Grid>

      {/* Quick Stats */}
      <Grid item xs={12} md={4}>
        <Card elevation={2}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Behavioral Patterns
            </Typography>
            
            <Stack spacing={2}>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Optimal Working Hours
                </Typography>
                <Stack direction="row" spacing={1} sx={{ mt: 0.5 }}>
                  {behavioralMetrics?.optimalWorkingHours.map((hour, index) => (
                    <Chip key={index} label={hour} size="small" color="success" />
                  ))}
                </Stack>
              </Box>

              <Divider />

              <Box>
                <Typography variant="body2" color="text.secondary">
                  Top Stress Triggers
                </Typography>
                <List dense>
                  {behavioralMetrics?.stressTriggers.slice(0, 3).map((trigger, index) => (
                    <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
                      <ListItemText 
                        primary={trigger}
                        primaryTypographyProps={{ variant: 'body2' }}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>

              <Divider />

              <Box>
                <Typography variant="body2" color="text.secondary">
                  Motivation Factors
                </Typography>
                <List dense>
                  {behavioralMetrics?.motivationFactors.slice(0, 3).map((factor, index) => (
                    <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
                      <ListItemText 
                        primary={factor}
                        primaryTypographyProps={{ variant: 'body2' }}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            </Stack>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderTrendsTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card elevation={2}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Weekly Behavioral Trends
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Trend charts would be implemented here using a charting library like Chart.js or Recharts.
              This would show stress, engagement, and productivity trends over time.
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderRecommendationsTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card elevation={2}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Personalized Recommendations
            </Typography>
            
            <List>
              {['Focus on high-stress periods for better preparation', 'Schedule complex tasks during peak engagement hours', 'Use AI assistance during high cognitive load periods'].map((recommendation, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <InsightIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary={recommendation} />
                </ListItem>
              ))}
            </List>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Analyzing your behavioral patterns...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Card elevation={2} sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={8}>
              <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1 }}>
                🧠 Behavioral Analytics
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Understand your psychological patterns and optimize your performance
              </Typography>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Stack direction="row" spacing={1} justifyContent="flex-end">
                {['week', 'month', 'quarter'].map((range) => (
                  <Chip
                    key={range}
                    label={range.charAt(0).toUpperCase() + range.slice(1)}
                    variant={timeRange === range ? 'filled' : 'outlined'}
                    onClick={() => setTimeRange(range as any)}
                    size="small"
                  />
                ))}
              </Stack>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs 
          value={selectedTab} 
          onChange={(e, newValue) => setSelectedTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="Overview" />
          <Tab label="Trends" />
          <Tab label="Recommendations" />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      {selectedTab === 0 && renderOverviewTab()}
      {selectedTab === 1 && renderTrendsTab()}
      {selectedTab === 2 && renderRecommendationsTab()}
    </Box>
  );
};

export default BehavioralAnalytics;
