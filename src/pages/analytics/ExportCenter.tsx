/**
 * Export Center Page
 * Comprehensive data export and reporting functionality
 */

import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Chip,
  Alert,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Divider,
  FormControlLabel,
  Checkbox,
  TextField,
  CircularProgress
} from '@mui/material';
import {
  Download,
  PictureAsPdf,
  TableChart,
  InsertDriveFile,
  Schedule,
  Email,
  Settings,
  History,
  CloudDownload,
  Assessment,
  Analytics,
  TrendingUp,
  Psychology
} from '@mui/icons-material';
import { useExportAnalyticsMutation } from '../../services/api/analytics.api';

interface ExportTemplate {
  id: string;
  name: string;
  description: string;
  format: 'pdf' | 'excel' | 'csv';
  icon: React.ReactNode;
  dataTypes: string[];
  estimatedSize: string;
}

const ExportCenter: React.FC = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'quarter' | 'year'>('quarter');
  const [includeCharts, setIncludeCharts] = useState(true);
  const [includeRawData, setIncludeRawData] = useState(false);
  const [emailReport, setEmailReport] = useState(false);
  const [emailAddress, setEmailAddress] = useState('');
  const [isExporting, setIsExporting] = useState(false);
  const [userId] = useState('demo'); // Get from auth context

  const [exportAnalytics] = useExportAnalyticsMutation();

  const exportTemplates: ExportTemplate[] = [
    {
      id: 'comprehensive',
      name: 'Comprehensive Analytics Report',
      description: 'Complete performance analysis with all metrics, charts, and insights',
      format: 'pdf',
      icon: <PictureAsPdf color="error" />,
      dataTypes: ['Performance', 'Financial', 'Competitive', 'Psychological'],
      estimatedSize: '2-5 MB'
    },
    {
      id: 'financial',
      name: 'Financial Performance Report',
      description: 'Revenue, profit, ROI analysis and financial metrics',
      format: 'excel',
      icon: <TableChart color="success" />,
      dataTypes: ['Revenue', 'Costs', 'Profit', 'ROI', 'Economic Impact'],
      estimatedSize: '500 KB - 1 MB'
    },
    {
      id: 'competitive',
      name: 'Competitive Intelligence Report',
      description: 'Market analysis and competitor performance data',
      format: 'pdf',
      icon: <TrendingUp color="info" />,
      dataTypes: ['Competitors', 'Market Position', 'Win Rates', 'Threats'],
      estimatedSize: '1-2 MB'
    },
    {
      id: 'psychological',
      name: 'Behavioral Analytics Report',
      description: 'Psychological insights and behavioral patterns',
      format: 'pdf',
      icon: <Psychology color="warning" />,
      dataTypes: ['Behavioral Patterns', 'Optimal Times', 'Stress Analysis'],
      estimatedSize: '1-3 MB'
    },
    {
      id: 'raw_data',
      name: 'Raw Data Export',
      description: 'All raw data in spreadsheet format for custom analysis',
      format: 'csv',
      icon: <InsertDriveFile color="primary" />,
      dataTypes: ['All Raw Data', 'Timestamps', 'Detailed Metrics'],
      estimatedSize: '100-500 KB'
    }
  ];

  const recentExports = [
    {
      id: '1',
      name: 'Q4 2024 Comprehensive Report',
      format: 'PDF',
      date: '2024-12-10',
      size: '3.2 MB',
      status: 'completed'
    },
    {
      id: '2',
      name: 'Financial Analysis - November',
      format: 'Excel',
      date: '2024-12-01',
      size: '850 KB',
      status: 'completed'
    },
    {
      id: '3',
      name: 'Competitive Intelligence',
      format: 'PDF',
      date: '2024-11-25',
      size: '1.8 MB',
      status: 'completed'
    }
  ];

  const handleExport = async () => {
    if (!selectedTemplate) return;

    setIsExporting(true);
    try {
      const template = exportTemplates.find(t => t.id === selectedTemplate);
      if (!template) return;

      const blob = await exportAnalytics({ 
        userId, 
        format: template.format, 
        timeRange 
      }).unwrap();

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${template.name.toLowerCase().replace(/\s+/g, '-')}-${timeRange}.${template.format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // If email is requested, simulate email sending
      if (emailReport && emailAddress) {
        // In real implementation, this would trigger an email API
        console.log(`Report emailed to: ${emailAddress}`);
      }

    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const getFormatIcon = (format: string) => {
    switch (format.toLowerCase()) {
      case 'pdf': return <PictureAsPdf color="error" />;
      case 'excel': return <TableChart color="success" />;
      case 'csv': return <InsertDriveFile color="primary" />;
      default: return <InsertDriveFile />;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main', mb: 1 }}>
          📤 Export Center
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Export your analytics data in various formats for reporting and analysis
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Export Configuration */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ mb: 3 }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">📋 Export Configuration</Typography>
            </Box>
            <CardContent>
              {/* Template Selection */}
              <Typography variant="subtitle2" sx={{ mb: 2 }}>
                Select Export Template
              </Typography>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                {exportTemplates.map((template) => (
                  <Grid item xs={12} md={6} key={template.id}>
                    <Card 
                      variant="outlined"
                      sx={{ 
                        cursor: 'pointer',
                        border: selectedTemplate === template.id ? 2 : 1,
                        borderColor: selectedTemplate === template.id ? 'primary.main' : 'divider'
                      }}
                      onClick={() => setSelectedTemplate(template.id)}
                    >
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                          {template.icon}
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {template.name}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          {template.description}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1, mb: 1, flexWrap: 'wrap' }}>
                          {template.dataTypes.map((type) => (
                            <Chip key={type} label={type} size="small" variant="outlined" />
                          ))}
                        </Box>
                        <Typography variant="caption" color="text.secondary">
                          Format: {template.format.toUpperCase()} • Size: {template.estimatedSize}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>

              <Divider sx={{ my: 3 }} />

              {/* Export Options */}
              <Typography variant="subtitle2" sx={{ mb: 2 }}>
                Export Options
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Time Range</InputLabel>
                    <Select
                      value={timeRange}
                      label="Time Range"
                      onChange={(e) => setTimeRange(e.target.value as any)}
                    >
                      <MenuItem value="week">Last Week</MenuItem>
                      <MenuItem value="month">Last Month</MenuItem>
                      <MenuItem value="quarter">Last Quarter</MenuItem>
                      <MenuItem value="year">Last Year</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Box>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={includeCharts}
                          onChange={(e) => setIncludeCharts(e.target.checked)}
                        />
                      }
                      label="Include Charts & Visualizations"
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={includeRawData}
                          onChange={(e) => setIncludeRawData(e.target.checked)}
                        />
                      }
                      label="Include Raw Data"
                    />
                  </Box>
                </Grid>

                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={emailReport}
                        onChange={(e) => setEmailReport(e.target.checked)}
                      />
                    }
                    label="Email report after generation"
                  />
                  {emailReport && (
                    <TextField
                      fullWidth
                      size="small"
                      label="Email Address"
                      value={emailAddress}
                      onChange={(e) => setEmailAddress(e.target.value)}
                      sx={{ mt: 1 }}
                      placeholder="Enter email address"
                    />
                  )}
                </Grid>
              </Grid>

              <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                <Button
                  variant="contained"
                  startIcon={isExporting ? <CircularProgress size={20} /> : <Download />}
                  onClick={handleExport}
                  disabled={!selectedTemplate || isExporting}
                  size="large"
                >
                  {isExporting ? 'Generating...' : 'Generate & Download'}
                </Button>

                <Button
                  variant="outlined"
                  startIcon={<Schedule />}
                  disabled
                >
                  Schedule Export (Coming Soon)
                </Button>
              </Box>
            </CardContent>
          </Paper>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          {/* Export Status */}
          <Paper sx={{ mb: 3 }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">📊 Export Status</Typography>
            </Box>
            <CardContent>
              {isExporting ? (
                <Alert severity="info">
                  <Typography variant="subtitle2">Generating Report...</Typography>
                  <Typography variant="body2">
                    Please wait while we compile your analytics data.
                  </Typography>
                </Alert>
              ) : (
                <Alert severity="success">
                  <Typography variant="subtitle2">Ready to Export</Typography>
                  <Typography variant="body2">
                    Select a template and configure your export options.
                  </Typography>
                </Alert>
              )}
            </CardContent>
          </Paper>

          {/* Recent Exports */}
          <Paper>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">📁 Recent Exports</Typography>
            </Box>
            <List>
              {recentExports.map((export_item, index) => (
                <React.Fragment key={export_item.id}>
                  <ListItem>
                    <ListItemIcon>
                      {getFormatIcon(export_item.format)}
                    </ListItemIcon>
                    <ListItemText
                      primary={export_item.name}
                      secondary={`${export_item.date} • ${export_item.size}`}
                    />
                    <ListItemSecondaryAction>
                      <IconButton edge="end" size="small">
                        <CloudDownload />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < recentExports.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ExportCenter;
