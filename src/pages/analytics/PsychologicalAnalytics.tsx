/**
 * Psychological Analytics Page
 * Behavioral pattern analysis and psychological insights for bidding optimization
 */

import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Chip,
  LinearProgress,
  Alert,
  Paper,
  Avatar,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  CircularProgress
} from '@mui/material';
import {
  Psychology,
  TrendingUp,
  TrendingDown,
  AccessTime,
  Mood,
  Speed,
  EmojiEvents,
  Warning,
  Lightbulb,
  Timeline,
  Assessment,
  Refresh,
  Schedule,
  Favorite,
  BatteryChargingFull,
  SentimentSatisfied,
  SentimentDissatisfied
} from '@mui/icons-material';
import { useGetPsychologicalInsightsQuery } from '../../services/api/analytics.api';

const PsychologicalAnalytics: React.FC = () => {
  const [userId] = useState('demo'); // Get from auth context

  const { 
    data: insights, 
    isLoading,
    error,
    refetch 
  } = useGetPsychologicalInsightsQuery({ userId });

  const getMoodIcon = (mood: string) => {
    switch (mood) {
      case 'motivated': return '🚀';
      case 'confident': return '💪';
      case 'stressed': return '😰';
      case 'overwhelmed': return '🤯';
      case 'focused': return '🎯';
      case 'energetic': return '⚡';
      default: return '😐';
    }
  };

  const getMoodColor = (mood: string) => {
    switch (mood) {
      case 'motivated':
      case 'confident':
      case 'focused':
      case 'energetic':
        return 'success';
      case 'stressed':
      case 'overwhelmed':
        return 'error';
      default:
        return 'info';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'positive': return 'success';
      case 'negative': return 'error';
      case 'neutral': return 'info';
      default: return 'default';
    }
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load psychological analytics. Please try again later.
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
            🧠 Psychological Analytics
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Behavioral patterns and psychological insights for optimal bidding performance
          </Typography>
        </Box>

        <Button
          variant="outlined"
          startIcon={<Refresh />}
          onClick={() => refetch()}
        >
          Refresh Analysis
        </Button>
      </Box>

      {/* Loading State */}
      {isLoading && (
        <Box sx={{ mb: 3, textAlign: 'center' }}>
          <CircularProgress sx={{ mb: 2 }} />
          <Typography variant="body2">
            Analyzing your behavioral patterns...
          </Typography>
        </Box>
      )}

      {insights && (
        <>
          {/* Current Psychological State */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h2" sx={{ mb: 1 }}>
                    {getMoodIcon(insights.current_psychological_state.mood)}
                  </Typography>
                  <Typography variant="h6" sx={{ mb: 1 }}>
                    Current Mood
                  </Typography>
                  <Chip 
                    label={insights.current_psychological_state.mood.toUpperCase()}
                    color={getMoodColor(insights.current_psychological_state.mood) as any}
                    variant="outlined"
                  />
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <Favorite color="error" />
                    <Typography variant="h6">Confidence Level</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <CircularProgress 
                      variant="determinate" 
                      value={insights.current_psychological_state.confidence_level * 100}
                      color="success"
                      size={60}
                    />
                    <Typography variant="h4" sx={{ fontWeight: 600 }}>
                      {formatPercentage(insights.current_psychological_state.confidence_level)}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <Warning color="warning" />
                    <Typography variant="h6">Stress Level</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <CircularProgress 
                      variant="determinate" 
                      value={insights.current_psychological_state.stress_level * 100}
                      color={insights.current_psychological_state.stress_level > 0.7 ? 'error' : 'warning'}
                      size={60}
                    />
                    <Typography variant="h4" sx={{ fontWeight: 600 }}>
                      {formatPercentage(insights.current_psychological_state.stress_level)}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <BatteryChargingFull color="info" />
                    <Typography variant="h6">Energy Level</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <CircularProgress 
                      variant="determinate" 
                      value={insights.current_psychological_state.energy_level * 100}
                      color="info"
                      size={60}
                    />
                    <Typography variant="h4" sx={{ fontWeight: 600 }}>
                      {formatPercentage(insights.current_psychological_state.energy_level)}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Optimal Bidding Times */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Schedule color="primary" />
                    Optimal Bidding Times
                  </Typography>
                  
                  <Alert severity="success" sx={{ mb: 2 }}>
                    <Typography variant="subtitle2">Peak Performance Window</Typography>
                    <Typography variant="body2">
                      Your success rate is highest between {insights.optimal_bidding_times.join(' and ')}
                    </Typography>
                  </Alert>

                  <List dense>
                    {insights.optimal_bidding_times.map((time, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <AccessTime color="primary" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={time}
                          secondary="High performance window"
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Assessment color="primary" />
                    Stress Impact Analysis
                  </Typography>
                  
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      Stress Impact on Success Rate
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={Math.abs(insights.stress_impact_on_success) * 100}
                      color={insights.stress_impact_on_success < 0 ? 'error' : 'success'}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      {insights.stress_impact_on_success < 0 ? 'Negative' : 'Positive'} impact: {formatPercentage(Math.abs(insights.stress_impact_on_success))}
                    </Typography>
                  </Box>

                  <Alert severity={insights.stress_impact_on_success < -0.1 ? 'warning' : 'info'}>
                    <Typography variant="body2">
                      {insights.stress_impact_on_success < -0.1 
                        ? 'High stress significantly reduces your bidding success. Consider stress management techniques.'
                        : 'Your stress levels have minimal impact on performance. Good stress management!'
                      }
                    </Typography>
                  </Alert>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Behavioral Patterns */}
          <Paper sx={{ mb: 3 }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">🔍 Behavioral Patterns</Typography>
              <Typography variant="body2" color="text.secondary">
                Identified patterns in your bidding behavior and their impact on success
              </Typography>
            </Box>

            <Box sx={{ p: 2 }}>
              <Grid container spacing={2}>
                {insights.behavioral_patterns.map((pattern, index) => (
                  <Grid item xs={12} md={6} key={index}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                          {pattern.impact === 'positive' ? (
                            <SentimentSatisfied color="success" />
                          ) : pattern.impact === 'negative' ? (
                            <SentimentDissatisfied color="error" />
                          ) : (
                            <Mood color="info" />
                          )}
                          <Chip 
                            label={pattern.impact.toUpperCase()}
                            color={getImpactColor(pattern.impact) as any}
                            size="small"
                          />
                        </Box>
                        
                        <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                          {pattern.pattern}
                        </Typography>
                        
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          Frequency: {pattern.frequency} times
                        </Typography>
                        
                        <Typography variant="body2">
                          {pattern.recommendation}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Paper>

          {/* Recommendations */}
          <Paper>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">💡 Psychological Recommendations</Typography>
              <Typography variant="body2" color="text.secondary">
                Personalized recommendations to optimize your bidding performance
              </Typography>
            </Box>

            <Box sx={{ p: 2 }}>
              <Grid container spacing={2}>
                {insights.recommendations.map((rec, index) => (
                  <Grid item xs={12} md={6} key={index}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                          <Lightbulb color="warning" />
                          <Chip 
                            label={rec.type.toUpperCase()}
                            color={
                              rec.type === 'timing' ? 'info' :
                              rec.type === 'preparation' ? 'warning' :
                              rec.type === 'mindset' ? 'success' : 'primary'
                            }
                            size="small"
                          />
                        </Box>
                        
                        <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                          {rec.title}
                        </Typography>
                        
                        <Typography variant="body2" sx={{ mb: 1 }}>
                          {rec.description}
                        </Typography>
                        
                        <Alert severity="success" sx={{ mt: 1 }}>
                          <Typography variant="caption">
                            Expected improvement: {rec.expected_improvement}
                          </Typography>
                        </Alert>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Paper>
        </>
      )}

      {/* No Data State */}
      {!isLoading && !insights && (
        <Alert severity="info" sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" sx={{ mb: 1 }}>
            Building Your Psychological Profile
          </Typography>
          <Typography variant="body2">
            Continue bidding to gather enough data for psychological analysis. We need at least 10 bids to provide meaningful insights.
          </Typography>
        </Alert>
      )}
    </Box>
  );
};

export default PsychologicalAnalytics;
