/**
 * Advanced Analytics Page
 * Comprehensive bid performance analytics with advanced insights
 */

import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Chip,
  LinearProgress,
  Alert,
  Tabs,
  Tab,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Analytics,
  Timeline,
  Assessment,
  PieChart,
  BarChart,
  ShowChart,
  Download,
  Refresh,
  FilterList
} from '@mui/icons-material';
import {
  useGetBidPerformanceQuery,
  useGetCompetitiveAnalysisQuery,
  useGetCategoryPerformanceQuery,
  useExportAnalyticsMutation
} from '../../services/api/analytics.api';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`analytics-tabpanel-${index}`}
      aria-labelledby={`analytics-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const AdvancedAnalytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'quarter' | 'year'>('quarter');
  const [activeTab, setActiveTab] = useState(0);
  const [userId] = useState('demo'); // Get from auth context

  // API Queries
  const { 
    data: performance, 
    isLoading: performanceLoading,
    error: performanceError 
  } = useGetBidPerformanceQuery({ userId, timeRange });

  const { 
    data: competitive, 
    isLoading: competitiveLoading 
  } = useGetCompetitiveAnalysisQuery({ userId, timeRange });

  const { 
    data: categories, 
    isLoading: categoriesLoading 
  } = useGetCategoryPerformanceQuery({ userId, timeRange });

  const [exportAnalytics] = useExportAnalyticsMutation();

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleExport = async (format: 'pdf' | 'excel' | 'csv') => {
    try {
      const blob = await exportAnalytics({ userId, format, timeRange }).unwrap();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `bid-analytics-${timeRange}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (performanceError) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load analytics data. Please try again later.
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
            📊 Advanced Analytics
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Comprehensive bid performance insights and competitive intelligence
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              label="Time Range"
              onChange={(e) => setTimeRange(e.target.value as any)}
            >
              <MenuItem value="week">Last Week</MenuItem>
              <MenuItem value="month">Last Month</MenuItem>
              <MenuItem value="quarter">Last Quarter</MenuItem>
              <MenuItem value="year">Last Year</MenuItem>
            </Select>
          </FormControl>

          <Button
            variant="outlined"
            startIcon={<Download />}
            onClick={() => handleExport('pdf')}
          >
            Export PDF
          </Button>

          <Button
            variant="outlined"
            startIcon={<Download />}
            onClick={() => handleExport('excel')}
          >
            Export Excel
          </Button>
        </Box>
      </Box>

      {/* Key Metrics Cards */}
      {performance && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <TrendingUp color="success" />
                  <Typography variant="h6">Success Rate</Typography>
                </Box>
                <Typography variant="h4" sx={{ mt: 1, fontWeight: 600 }}>
                  {formatPercentage(performance.success_rate)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {performance.successful_bids} of {performance.total_bids} bids won
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Assessment color="primary" />
                  <Typography variant="h6">Total Value</Typography>
                </Box>
                <Typography variant="h4" sx={{ mt: 1, fontWeight: 600 }}>
                  {formatCurrency(performance.total_value)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Won: {formatCurrency(performance.won_value)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <ShowChart color="info" />
                  <Typography variant="h6">ROI</Typography>
                </Box>
                <Typography variant="h4" sx={{ mt: 1, fontWeight: 600 }}>
                  {formatPercentage(performance.roi)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Profit: {formatCurrency(performance.profit)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Timeline color="warning" />
                  <Typography variant="h6">Market Position</Typography>
                </Box>
                <Typography variant="h4" sx={{ mt: 1, fontWeight: 600 }}>
                  #{performance.market_position}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Competitive ranking
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Loading State */}
      {performanceLoading && (
        <Box sx={{ mb: 3 }}>
          <LinearProgress />
          <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
            Loading analytics data...
          </Typography>
        </Box>
      )}

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} variant="fullWidth">
          <Tab label="Performance Trends" icon={<TrendingUp />} />
          <Tab label="Competitive Analysis" icon={<BarChart />} />
          <Tab label="Category Performance" icon={<PieChart />} />
          <Tab label="Financial Analysis" icon={<Assessment />} />
        </Tabs>

        {/* Performance Trends Tab */}
        <TabPanel value={activeTab} index={0}>
          <Typography variant="h6" sx={{ mb: 2 }}>📈 Performance Trends</Typography>
          <Alert severity="info">
            Performance trend charts will be displayed here. Integration with charting library needed.
          </Alert>
        </TabPanel>

        {/* Competitive Analysis Tab */}
        <TabPanel value={activeTab} index={1}>
          <Typography variant="h6" sx={{ mb: 2 }}>🏆 Competitive Analysis</Typography>
          {competitive && competitive.length > 0 ? (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Competitor</TableCell>
                    <TableCell align="right">Head-to-Head Wins</TableCell>
                    <TableCell align="right">Head-to-Head Losses</TableCell>
                    <TableCell align="right">Win Rate</TableCell>
                    <TableCell align="right">Threat Level</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {competitive.map((competitor) => (
                    <TableRow key={competitor.competitor_name}>
                      <TableCell>{competitor.competitor_name}</TableCell>
                      <TableCell align="right">{competitor.heads_up_wins}</TableCell>
                      <TableCell align="right">{competitor.heads_up_losses}</TableCell>
                      <TableCell align="right">{formatPercentage(competitor.win_rate)}</TableCell>
                      <TableCell align="right">
                        <Chip 
                          label={competitor.threat_level}
                          color={
                            competitor.threat_level === 'critical' ? 'error' :
                            competitor.threat_level === 'high' ? 'warning' :
                            competitor.threat_level === 'medium' ? 'info' : 'success'
                          }
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Alert severity="info">
              No competitive data available for the selected time period.
            </Alert>
          )}
        </TabPanel>

        {/* Category Performance Tab */}
        <TabPanel value={activeTab} index={2}>
          <Typography variant="h6" sx={{ mb: 2 }}>📊 Category Performance</Typography>
          {categories && categories.length > 0 ? (
            <Grid container spacing={2}>
              {categories.map((category) => (
                <Grid item xs={12} md={6} key={category.category}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6">{category.category}</Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                        <Typography variant="body2">Success Rate:</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {formatPercentage(category.success_rate)}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">Total Value:</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {formatCurrency(category.total_value)}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">Profitability:</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {formatPercentage(category.profitability)}
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          ) : (
            <Alert severity="info">
              No category performance data available.
            </Alert>
          )}
        </TabPanel>

        {/* Financial Analysis Tab */}
        <TabPanel value={activeTab} index={3}>
          <Typography variant="h6" sx={{ mb: 2 }}>💰 Financial Analysis</Typography>
          {performance ? (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>Revenue Metrics</Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography>Total Revenue:</Typography>
                      <Typography sx={{ fontWeight: 600 }}>
                        {formatCurrency(performance.revenue)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography>Profit Margin:</Typography>
                      <Typography sx={{ fontWeight: 600 }}>
                        {formatPercentage(performance.profit_margin)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography>Cost per Bid:</Typography>
                      <Typography sx={{ fontWeight: 600 }}>
                        {formatCurrency(performance.cost_per_bid)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography>Revenue per Bid:</Typography>
                      <Typography sx={{ fontWeight: 600 }}>
                        {formatCurrency(performance.revenue_per_bid)}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>Economic Impact</Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography>Jobs Created:</Typography>
                      <Typography sx={{ fontWeight: 600 }}>
                        {performance.jobs_created}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography>Economic Value:</Typography>
                      <Typography sx={{ fontWeight: 600 }}>
                        {formatCurrency(performance.economic_value)}
                      </Typography>
                    </Box>
                    <Alert severity="success" sx={{ mt: 2 }}>
                      Your bidding activity has created {performance.jobs_created} jobs and contributed {formatCurrency(performance.economic_value)} to the economy!
                    </Alert>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          ) : (
            <Alert severity="info">
              Financial analysis data is loading...
            </Alert>
          )}
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default AdvancedAnalytics;
