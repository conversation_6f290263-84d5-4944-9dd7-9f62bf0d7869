/**
 * Competitive Analytics Page
 * Detailed competitor analysis and market intelligence
 */

import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Chip,
  LinearProgress,
  Alert,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  EmojiEvents,
  Warning,
  Info,
  Visibility,
  CompareArrows,
  Timeline,
  Assessment,
  Speed,
  Star,
  StarBorder
} from '@mui/icons-material';
import { useGetCompetitiveAnalysisQuery } from '../../services/api/analytics.api';

const CompetitiveAnalytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'quarter' | 'year'>('quarter');
  const [userId] = useState('demo'); // Get from auth context

  const { 
    data: competitors, 
    isLoading,
    error,
    refetch 
  } = useGetCompetitiveAnalysisQuery({ userId, timeRange });

  const getThreatColor = (level: string) => {
    switch (level) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getThreatIcon = (level: string) => {
    switch (level) {
      case 'critical': return <Warning color="error" />;
      case 'high': return <Warning color="warning" />;
      case 'medium': return <Info color="info" />;
      case 'low': return <EmojiEvents color="success" />;
      default: return <Info />;
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load competitive analytics. Please try again later.
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
            🏆 Competitive Analytics
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Market intelligence and competitor performance analysis
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              label="Time Range"
              onChange={(e) => setTimeRange(e.target.value as any)}
            >
              <MenuItem value="week">Last Week</MenuItem>
              <MenuItem value="month">Last Month</MenuItem>
              <MenuItem value="quarter">Last Quarter</MenuItem>
              <MenuItem value="year">Last Year</MenuItem>
            </Select>
          </FormControl>

          <Button
            variant="outlined"
            startIcon={<Timeline />}
            onClick={() => refetch()}
          >
            Refresh Data
          </Button>
        </Box>
      </Box>

      {/* Loading State */}
      {isLoading && (
        <Box sx={{ mb: 3 }}>
          <LinearProgress />
          <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
            Loading competitive intelligence...
          </Typography>
        </Box>
      )}

      {/* Market Overview */}
      {competitors && competitors.length > 0 && (
        <>
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CompareArrows color="primary" />
                    <Typography variant="h6">Active Competitors</Typography>
                  </Box>
                  <Typography variant="h4" sx={{ mt: 1, fontWeight: 600 }}>
                    {competitors.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    In your market segments
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Warning color="warning" />
                    <Typography variant="h6">High Threats</Typography>
                  </Box>
                  <Typography variant="h4" sx={{ mt: 1, fontWeight: 600 }}>
                    {competitors.filter(c => c.threat_level === 'high' || c.threat_level === 'critical').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Require attention
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <TrendingUp color="success" />
                    <Typography variant="h6">Your Win Rate</Typography>
                  </Box>
                  <Typography variant="h4" sx={{ mt: 1, fontWeight: 600 }}>
                    {formatPercentage(
                      competitors.reduce((acc, c) => acc + c.win_rate, 0) / competitors.length || 0
                    )}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Against competitors
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Assessment color="info" />
                    <Typography variant="h6">Market Position</Typography>
                  </Box>
                  <Typography variant="h4" sx={{ mt: 1, fontWeight: 600 }}>
                    #3
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    In your category
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Competitor Analysis Table */}
          <Paper sx={{ mb: 3 }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">🎯 Competitor Intelligence</Typography>
              <Typography variant="body2" color="text.secondary">
                Head-to-head performance analysis with key competitors
              </Typography>
            </Box>

            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Competitor</TableCell>
                    <TableCell align="center">Threat Level</TableCell>
                    <TableCell align="right">Head-to-Head Wins</TableCell>
                    <TableCell align="right">Head-to-Head Losses</TableCell>
                    <TableCell align="right">Win Rate vs You</TableCell>
                    <TableCell align="right">Avg Bid Difference</TableCell>
                    <TableCell align="right">Last Encounter</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {competitors.map((competitor, index) => (
                    <TableRow key={competitor.competitor_name} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar sx={{ bgcolor: 'primary.main' }}>
                            {competitor.competitor_name.charAt(0)}
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                              {competitor.competitor_name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Rank #{index + 2}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>

                      <TableCell align="center">
                        <Chip 
                          icon={getThreatIcon(competitor.threat_level)}
                          label={competitor.threat_level.toUpperCase()}
                          color={getThreatColor(competitor.threat_level) as any}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>

                      <TableCell align="right">
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: 1 }}>
                          <EmojiEvents color="success" fontSize="small" />
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {competitor.heads_up_wins}
                          </Typography>
                        </Box>
                      </TableCell>

                      <TableCell align="right">
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: 1 }}>
                          <Warning color="error" fontSize="small" />
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {competitor.heads_up_losses}
                          </Typography>
                        </Box>
                      </TableCell>

                      <TableCell align="right">
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: 1 }}>
                          {competitor.win_rate > 50 ? (
                            <TrendingDown color="error" fontSize="small" />
                          ) : (
                            <TrendingUp color="success" fontSize="small" />
                          )}
                          <Typography 
                            variant="body2" 
                            sx={{ 
                              fontWeight: 600,
                              color: competitor.win_rate > 50 ? 'error.main' : 'success.main'
                            }}
                          >
                            {formatPercentage(competitor.win_rate)}
                          </Typography>
                        </Box>
                      </TableCell>

                      <TableCell align="right">
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            fontWeight: 600,
                            color: competitor.avg_bid_difference > 0 ? 'error.main' : 'success.main'
                          }}
                        >
                          {competitor.avg_bid_difference > 0 ? '+' : ''}
                          {formatCurrency(competitor.avg_bid_difference)}
                        </Typography>
                      </TableCell>

                      <TableCell align="right">
                        <Typography variant="body2" color="text.secondary">
                          {competitor.last_encounter}
                        </Typography>
                      </TableCell>

                      <TableCell align="center">
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="View Details">
                            <IconButton size="small">
                              <Visibility fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Add to Watchlist">
                            <IconButton size="small">
                              <StarBorder fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>

          {/* Competitive Insights */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>🎯 Strategic Insights</Typography>
                  
                  <Alert severity="info" sx={{ mb: 2 }}>
                    <Typography variant="subtitle2">Market Opportunity</Typography>
                    <Typography variant="body2">
                      You have a 73% win rate against mid-tier competitors. Focus on these segments for growth.
                    </Typography>
                  </Alert>

                  <Alert severity="warning" sx={{ mb: 2 }}>
                    <Typography variant="subtitle2">Competitive Threat</Typography>
                    <Typography variant="body2">
                      ABC Construction has won 3 recent head-to-heads. Consider adjusting pricing strategy.
                    </Typography>
                  </Alert>

                  <Alert severity="success">
                    <Typography variant="subtitle2">Strength Area</Typography>
                    <Typography variant="body2">
                      Your response time is 40% faster than competitors. Leverage this advantage in marketing.
                    </Typography>
                  </Alert>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>📊 Market Trends</Typography>
                  
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      Average Market Win Rate
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={45} 
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      45% (Industry Average)
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      Your Win Rate vs Market
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={67} 
                      color="success"
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                    <Typography variant="caption" color="success.main">
                      67% (+22% above market)
                    </Typography>
                  </Box>

                  <Box>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      Market Competition Intensity
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={78} 
                      color="warning"
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                    <Typography variant="caption" color="warning.main">
                      High (78% - Increasing)
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </>
      )}

      {/* No Data State */}
      {competitors && competitors.length === 0 && (
        <Alert severity="info" sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" sx={{ mb: 1 }}>
            No Competitive Data Available
          </Typography>
          <Typography variant="body2">
            Start bidding on more tenders to build competitive intelligence data.
          </Typography>
        </Alert>
      )}
    </Box>
  );
};

export default CompetitiveAnalytics;
