/**
 * Behavioral Tender Swipe Interface
 * Gamified tender discovery with psychological optimization
 * Integrates with existing NeuroMarketing Engine
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  IconButton,
  Button,
  LinearProgress,
  Fade,
  Grow,
  Slide,
  Alert,
  Avatar,
  Stack
} from '@mui/material';
import {
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  Favorite as FavoriteIcon,
  Star as StarIcon,
  Timer as TimerIcon,
  LocationOn as LocationIcon,
  Business as BusinessIcon,
  TrendingUp as TrendingUpIcon,
  Psychology as PsychologyIcon,
  EmojiEvents as AchievementIcon
} from '@mui/icons-material';
import { useSwipeable } from 'react-swipeable';
import confetti from 'canvas-confetti';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';
import BehavioralTenderService from '../../services/BehavioralTenderService';
import { Tender, EngagementTrigger, Priority } from '../../types/tender.types';

interface TenderSwipeProps {
  onTenderInteraction?: (tenderId: string, action: 'interested' | 'passed') => void;
  onComplete?: () => void;
}

const TenderSwipe: React.FC<TenderSwipeProps> = ({
  onTenderInteraction,
  onComplete
}) => {
  // State management
  const [tenders, setTenders] = useState<Tender[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [swipeDirection, setSwipeDirection] = useState<'left' | 'right' | null>(null);
  const [showPerfectMatch, setShowPerfectMatch] = useState(false);
  const [dailyStats, setDailyStats] = useState({
    viewed: 0,
    interested: 0,
    perfectMatches: 0
  });

  // Behavioral tracking
  const {
    psychologicalState,
    adaptiveSettings,
    trackEngagement,
    trackDecision,
    isHighCognitiveLoad,
    isStressed,
    needsSimplification
  } = useNeuroMarketing();

  const behavioralService = BehavioralTenderService.getInstance();
  const cardRef = useRef<HTMLDivElement>(null);

  // Load personalized tenders
  useEffect(() => {
    loadPersonalizedTenders();
  }, []);

  // Track psychological state changes
  useEffect(() => {
    if (psychologicalState) {
      trackEngagement('tender_swipe_session', {
        cognitiveLoad: psychologicalState.cognitiveLoad,
        stressLevel: psychologicalState.stressLevel,
        engagementLevel: psychologicalState.engagementLevel
      });
    }
  }, [psychologicalState]);

  const loadPersonalizedTenders = async () => {
    try {
      setLoading(true);
      
      // Mock tender data - in real app, this would come from API
      const mockTenders: Tender[] = [
        {
          id: 'tender-1',
          title: 'Smart City IoT Infrastructure Development',
          description: 'Design and implement IoT sensors across the city for traffic management, environmental monitoring, and public safety.',
          organization: 'City of Cape Town',
          organizationType: 'government',
          category: 'it_services' as any,
          value: 15000000,
          currency: 'ZAR',
          location: 'Cape Town, Western Cape',
          province: 'Western Cape',
          publishDate: '2024-01-15T08:00:00Z',
          closingDate: '2024-02-28T17:00:00Z',
          status: 'open' as any,
          priority: Priority.HIGH,
          referenceNumber: 'CCT/IOT/2024/001',
          psychologicalTriggers: [EngagementTrigger.AUTHORITY, EngagementTrigger.ACHIEVEMENT],
          behavioralNudges: [],
          emotionalTone: 'innovative' as any,
          socialProofData: {
            viewCount: 245,
            interestedCount: 18,
            recentViewers: 12,
            competitorCount: 8
          },
          difficultyLevel: 'advanced' as any,
          estimatedEffort: 120,
          potentialXP: 150,
          achievementUnlocks: ['Tech Pioneer', 'Government Partner'],
          bookmarked: false,
          engagementScore: 85,
          complianceRequirements: [],
          technicalRequirements: [],
          financialRequirements: [],
          tenderDocuments: [],
          clarifications: []
        },
        // Add more mock tenders...
      ];

      // Get personalized recommendations
      const personalizedTenders = await behavioralService.getPersonalizedTenders(mockTenders, 10);
      setTenders(personalizedTenders);
      
    } catch (error) {
      console.error('Failed to load tenders:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSwipe = (direction: 'left' | 'right') => {
    if (currentIndex >= tenders.length) return;

    const currentTender = tenders[currentIndex];
    setSwipeDirection(direction);

    // Track behavioral decision
    trackDecision('tender_swipe', {
      tenderId: currentTender.id,
      direction,
      matchScore: currentTender.matchData?.matchScore || 0,
      psychologicalState: psychologicalState
    });

    // Update stats
    setDailyStats(prev => ({
      ...prev,
      viewed: prev.viewed + 1,
      interested: direction === 'right' ? prev.interested + 1 : prev.interested,
      perfectMatches: direction === 'right' && (currentTender.matchData?.matchScore || 0) > 90 
        ? prev.perfectMatches + 1 
        : prev.perfectMatches
    }));

    // Show perfect match celebration
    if (direction === 'right' && (currentTender.matchData?.matchScore || 0) > 90) {
      setShowPerfectMatch(true);
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });
      setTimeout(() => setShowPerfectMatch(false), 3000);
    }

    // Callback for parent component
    onTenderInteraction?.(currentTender.id, direction === 'right' ? 'interested' : 'passed');

    // Move to next tender
    setTimeout(() => {
      setSwipeDirection(null);
      setCurrentIndex(prev => prev + 1);
      
      // Check if completed
      if (currentIndex + 1 >= tenders.length) {
        onComplete?.();
      }
    }, 300);
  };

  // Swipe gesture handlers
  const swipeHandlers = useSwipeable({
    onSwipedLeft: () => handleSwipe('left'),
    onSwipedRight: () => handleSwipe('right'),
    preventDefaultTouchmoveEvent: true,
    trackMouse: true
  });

  const currentTender = tenders[currentIndex];

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <Box sx={{ textAlign: 'center' }}>
          <PsychologyIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
          <Typography variant="h6">Personalizing your tender feed...</Typography>
          <LinearProgress sx={{ mt: 2, width: 200 }} />
        </Box>
      </Box>
    );
  }

  if (!currentTender) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <AchievementIcon sx={{ fontSize: 80, color: 'success.main', mb: 2 }} />
        <Typography variant="h4" gutterBottom>All Caught Up! 🎉</Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          You've reviewed all available tenders for today.
        </Typography>
        
        {/* Daily Stats */}
        <Card sx={{ maxWidth: 400, mx: 'auto', mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>Today's Discovery Stats:</Typography>
            <Stack direction="row" spacing={3} justifyContent="center">
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary">{dailyStats.viewed}</Typography>
                <Typography variant="caption">Viewed</Typography>
              </Box>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="success.main">{dailyStats.interested}</Typography>
                <Typography variant="caption">Interested</Typography>
              </Box>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main">{dailyStats.perfectMatches}</Typography>
                <Typography variant="caption">Perfect Matches</Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>

        <Button variant="contained" onClick={() => window.location.href = '/dashboard'}>
          Back to Dashboard
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: 400, mx: 'auto', p: 2, minHeight: '80vh' }}>
      {/* Progress Indicator */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          {currentIndex + 1} of {tenders.length} • {tenders.length - currentIndex - 1} left
        </Typography>
        <LinearProgress 
          variant="determinate" 
          value={(currentIndex / tenders.length) * 100}
          sx={{ height: 6, borderRadius: 3 }}
        />
      </Box>

      {/* Perfect Match Alert */}
      <Fade in={showPerfectMatch}>
        <Alert 
          severity="success" 
          icon={<StarIcon />}
          sx={{ mb: 2, fontWeight: 'bold' }}
        >
          🎯 Perfect Match! This tender aligns perfectly with your profile!
        </Alert>
      </Fade>

      {/* Tender Card */}
      <Box
        {...swipeHandlers}
        ref={cardRef}
        sx={{
          position: 'relative',
          transform: swipeDirection === 'left' ? 'translateX(-100%)' : 
                    swipeDirection === 'right' ? 'translateX(100%)' : 'translateX(0)',
          transition: swipeDirection ? 'transform 0.3s ease-out' : 'none',
          opacity: swipeDirection ? 0.7 : 1
        }}
      >
        <Card 
          elevation={8}
          sx={{ 
            minHeight: 500,
            borderRadius: 4,
            background: needsSimplification ? 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)' :
                       'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {/* Match Score Badge */}
          {currentTender.matchData && (
            <Chip
              label={`${currentTender.matchData.matchScore}% Match`}
              color={currentTender.matchData.matchScore > 80 ? 'success' : 'warning'}
              sx={{ 
                position: 'absolute', 
                top: 16, 
                right: 16, 
                zIndex: 1,
                fontWeight: 'bold'
              }}
            />
          )}

          <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
            {/* Header */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1, lineHeight: 1.2 }}>
                {currentTender.personalizedTitle || currentTender.title}
              </Typography>
              
              <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
                <Chip 
                  icon={<BusinessIcon />}
                  label={currentTender.organization}
                  size="small"
                  variant="outlined"
                  sx={{ color: 'white', borderColor: 'white' }}
                />
                <Chip 
                  icon={<LocationIcon />}
                  label={currentTender.location}
                  size="small"
                  variant="outlined"
                  sx={{ color: 'white', borderColor: 'white' }}
                />
              </Stack>
            </Box>

            {/* Value and Deadline */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#FFD700' }}>
                R{(currentTender.value / 1000000).toFixed(1)}M
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                Estimated Contract Value
              </Typography>
            </Box>

            {/* Description */}
            <Typography 
              variant="body1" 
              sx={{ 
                mb: 2, 
                flexGrow: 1,
                fontSize: isHighCognitiveLoad ? '1.1rem' : '1rem',
                lineHeight: isStressed ? 1.8 : 1.6
              }}
            >
              {needsSimplification 
                ? currentTender.description.split('.')[0] + '...'
                : currentTender.personalizedDescription || currentTender.description
              }
            </Typography>

            {/* Behavioral Triggers */}
            {currentTender.behavioralNudges.length > 0 && (
              <Box sx={{ mb: 2 }}>
                {currentTender.behavioralNudges.map((nudge, index) => (
                  <Alert 
                    key={index}
                    severity={nudge.intensity === 'strong' ? 'warning' : 'info'}
                    sx={{ mb: 1, backgroundColor: 'rgba(255,255,255,0.1)' }}
                  >
                    {nudge.message}
                  </Alert>
                ))}
              </Box>
            )}

            {/* Social Proof */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                👥 {currentTender.socialProofData.interestedCount} bidders interested • 
                👀 {currentTender.socialProofData.recentViewers} recently viewed
              </Typography>
            </Box>

            {/* Gamification Elements */}
            <Box sx={{ mb: 3 }}>
              <Stack direction="row" spacing={2}>
                <Chip 
                  icon={<AchievementIcon />}
                  label={`${currentTender.potentialXP} XP`}
                  size="small"
                  sx={{ backgroundColor: 'rgba(255,255,255,0.2)', color: 'white' }}
                />
                <Chip 
                  icon={<TimerIcon />}
                  label={`${currentTender.estimatedEffort}h effort`}
                  size="small"
                  sx={{ backgroundColor: 'rgba(255,255,255,0.2)', color: 'white' }}
                />
              </Stack>
            </Box>
          </CardContent>
        </Card>
      </Box>

      {/* Action Buttons */}
      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 3, mt: 3 }}>
        <IconButton
          onClick={() => handleSwipe('left')}
          sx={{
            backgroundColor: 'error.main',
            color: 'white',
            width: 64,
            height: 64,
            '&:hover': { backgroundColor: 'error.dark', transform: 'scale(1.1)' }
          }}
        >
          <ThumbDownIcon sx={{ fontSize: 32 }} />
        </IconButton>

        <IconButton
          onClick={() => handleSwipe('right')}
          sx={{
            backgroundColor: 'success.main',
            color: 'white',
            width: 64,
            height: 64,
            '&:hover': { backgroundColor: 'success.dark', transform: 'scale(1.1)' }
          }}
        >
          <ThumbUpIcon sx={{ fontSize: 32 }} />
        </IconButton>
      </Box>

      {/* Swipe Instructions */}
      <Typography 
        variant="body2" 
        color="text.secondary" 
        sx={{ textAlign: 'center', mt: 2 }}
      >
        Swipe right to show interest • Swipe left to pass
      </Typography>
    </Box>
  );
};

export default TenderSwipe;
