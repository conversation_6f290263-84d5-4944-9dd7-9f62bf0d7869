/**
 * Psychologically-Optimized Geographic Tender Interface
 * Interactive map with behavioral psychology integration for tender discovery
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Stack,
  Divider,
  Tooltip,
  Badge,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Map as MapIcon,
  MyLocation as LocationIcon,
  FilterList as FilterIcon,
  Layers as LayersIcon,
  Route as RouteIcon,
  Psychology as PsychologyIcon,
  Visibility as ViewIcon,
  DirectionsCar as CarIcon,
  AccessTime as TimeIcon,
  AttachMoney as MoneyIcon,
  Warning as WarningIcon,
  TrendingUp as HeatIcon,
  Group as ClusterIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';
import { Tender, Priority } from '../../types/tender.types';
import GeomapService, { Coordinates } from '../../services/geomap';
import { env, mapConfig } from '../../config/environment';
import BeeTrackingMap from '../../components/maps/BeeTrackingMap';
import TaskLocationMap from '../../components/maps/TaskLocationMap';

interface TenderLocation {
  id: string;
  tender: Tender;
  position: {
    lat: number;
    lng: number;
  };
  address: string;
  distanceFromUser?: number; // km
  travelTime?: number; // minutes
  travelCost?: number; // ZAR
  psychologicalStress?: number; // 0-1 based on distance/complexity
}

interface MapFilters {
  radius: number; // km
  minValue: number;
  maxValue: number;
  categories: string[];
  priorities: Priority[];
  maxTravelTime: number; // minutes
  showHeatmap: boolean;
  showClusters: boolean;
  stressOptimized: boolean; // Only show low-stress tenders
}

const TenderMap: React.FC = () => {
  // Behavioral tracking
  const {
    psychologicalState,
    isStressed,
    isHighCognitiveLoad,
    needsSimplification,
    trackEngagement
  } = useNeuroMarketing();

  // Map state
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [tenderLocations, setTenderLocations] = useState<TenderLocation[]>([]);
  const [selectedTender, setSelectedTender] = useState<TenderLocation | null>(null);
  const [showInfoWindow, setShowInfoWindow] = useState(false);
  const [directionsResponse, setDirectionsResponse] = useState<google.maps.DirectionsResult | null>(null);

  // UI state
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [filters, setFilters] = useState<MapFilters>({
    radius: 100,
    minValue: 0,
    maxValue: 50000000,
    categories: [],
    priorities: [],
    maxTravelTime: 120,
    showHeatmap: false,
    showClusters: true,
    stressOptimized: false
  });

  // Psychological adaptations
  const [simplifiedView, setSimplifiedView] = useState(false);
  const [stressReducedColors, setStressReducedColors] = useState(false);

  // Map configuration
  const mapContainerStyle = {
    width: '100%',
    height: needsSimplification ? '400px' : '600px'
  };

  const defaultCenter = {
    lat: -26.2041, // Johannesburg
    lng: 28.0473
  };

  // Initialize geomap service
  const geoService = GeomapService.getInstance();

  // Load user location and tenders
  useEffect(() => {
    getUserLocation();
    loadTenderLocations();
  }, []);

  // Adapt to psychological state
  useEffect(() => {
    if (psychologicalState) {
      if (psychologicalState.stressLevel > 0.7) {
        setSimplifiedView(true);
        setStressReducedColors(true);
        setFilters(prev => ({ ...prev, stressOptimized: true }));
      }

      if (psychologicalState.cognitiveLoad > 0.8) {
        setFilters(prev => ({ ...prev, showClusters: true, showHeatmap: false }));
      }
    }
  }, [psychologicalState]);

  const getUserLocation = async () => {
    try {
      const location = await geoService.getCurrentLocation();
      setUserLocation({
        lat: location.latitude,
        lng: location.longitude
      });

      trackEngagement('user_location_obtained', {
        accuracy: 'high',
        psychologicalState
      });
    } catch (error) {
      console.error('Error getting location:', error);
      // Fallback to Johannesburg
      setUserLocation(defaultCenter);
    }
  };

  const loadTenderLocations = () => {
    // Mock tender locations across South Africa
    const mockTenderLocations: TenderLocation[] = [
      {
        id: 'tender-loc-001',
        tender: {
          id: 'tender-001',
          title: 'Smart City IoT Infrastructure - Cape Town',
          organization: 'City of Cape Town',
          value: 15000000,
          currency: 'ZAR',
          closingDate: '2024-02-28T17:00:00Z',
          status: 'open' as any,
          priority: Priority.HIGH,
          category: 'it_services' as any
        } as Tender,
        position: { lat: -33.9249, lng: 18.4241 }, // Cape Town
        address: 'Cape Town Civic Centre, 12 Hertzog Blvd, Cape Town',
        distanceFromUser: 1400,
        travelTime: 120,
        travelCost: 2800,
        psychologicalStress: 0.7 // High due to distance
      },
      {
        id: 'tender-loc-002',
        tender: {
          id: 'tender-002',
          title: 'Road Maintenance Contract - Johannesburg',
          organization: 'City of Johannesburg',
          value: 8500000,
          currency: 'ZAR',
          closingDate: '2024-02-15T17:00:00Z',
          status: 'open' as any,
          priority: Priority.MEDIUM,
          category: 'construction' as any
        } as Tender,
        position: { lat: -26.2041, lng: 28.0473 }, // Johannesburg
        address: 'Johannesburg City Hall, 1 Rissik St, Johannesburg',
        distanceFromUser: 15,
        travelTime: 25,
        travelCost: 150,
        psychologicalStress: 0.2 // Low due to proximity
      },
      {
        id: 'tender-loc-003',
        tender: {
          id: 'tender-003',
          title: 'Hospital Equipment Supply - Durban',
          organization: 'KwaZulu-Natal Health',
          value: 12000000,
          currency: 'ZAR',
          closingDate: '2024-03-10T17:00:00Z',
          status: 'open' as any,
          priority: Priority.HIGH,
          category: 'healthcare' as any
        } as Tender,
        position: { lat: -29.8587, lng: 31.0218 }, // Durban
        address: 'Durban City Hall, 251 Anton Lembede St, Durban',
        distanceFromUser: 560,
        travelTime: 60,
        travelCost: 1200,
        psychologicalStress: 0.4 // Medium due to moderate distance
      },
      {
        id: 'tender-loc-004',
        tender: {
          id: 'tender-004',
          title: 'School Building Project - Pretoria',
          organization: 'Gauteng Education',
          value: 25000000,
          currency: 'ZAR',
          closingDate: '2024-02-20T17:00:00Z',
          status: 'closing_soon' as any,
          priority: Priority.CRITICAL,
          category: 'construction' as any
        } as Tender,
        position: { lat: -25.7479, lng: 28.2293 }, // Pretoria
        address: 'Union Buildings, Government Ave, Pretoria',
        distanceFromUser: 55,
        travelTime: 45,
        travelCost: 350,
        psychologicalStress: 0.6 // Higher due to urgency
      }
    ];

    setTenderLocations(mockTenderLocations);
  };

  const getMarkerColor = (tenderLocation: TenderLocation) => {
    if (stressReducedColors) {
      // Use calming colors when user is stressed
      return tenderLocation.psychologicalStress! > 0.5 ? '#81C784' : '#4CAF50'; // Light green to green
    }

    // Normal color coding
    if (tenderLocation.tender.priority === Priority.CRITICAL) return '#F44336'; // Red
    if (tenderLocation.tender.priority === Priority.HIGH) return '#FF9800'; // Orange
    if (tenderLocation.tender.priority === Priority.MEDIUM) return '#2196F3'; // Blue
    return '#4CAF50'; // Green
  };

  const calculateRoute = async (destination: TenderLocation) => {
    if (!userLocation || !window.google) return;

    const directionsService = new google.maps.DirectionsService();
    
    try {
      const result = await directionsService.route({
        origin: userLocation,
        destination: destination.position,
        travelMode: google.maps.TravelMode.DRIVING,
      });

      setDirectionsResponse(result);
      
      trackEngagement('route_calculated', {
        tenderId: destination.tender.id,
        distance: destination.distanceFromUser,
        psychologicalState
      });
    } catch (error) {
      console.error('Error calculating route:', error);
    }
  };

  const handleMarkerClick = (tenderLocation: TenderLocation) => {
    setSelectedTender(tenderLocation);
    setShowInfoWindow(true);
    
    trackEngagement('tender_marker_clicked', {
      tenderId: tenderLocation.tender.id,
      distance: tenderLocation.distanceFromUser,
      psychologicalStress: tenderLocation.psychologicalStress,
      psychologicalState
    });
  };

  const filteredTenderLocations = tenderLocations.filter(location => {
    // Apply filters
    if (filters.stressOptimized && location.psychologicalStress! > 0.5) return false;
    if (location.distanceFromUser! > filters.radius) return false;
    if (location.travelTime! > filters.maxTravelTime) return false;
    if (location.tender.value < filters.minValue || location.tender.value > filters.maxValue) return false;
    
    return true;
  });

  const renderTenderInfo = (tenderLocation: TenderLocation) => (
    <Card sx={{ minWidth: 300, maxWidth: 400 }}>
      <CardContent>
        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
          {tenderLocation.tender.title}
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {tenderLocation.tender.organization}
        </Typography>

        <Stack spacing={1} sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="body2">Value:</Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
              R{tenderLocation.tender.value.toLocaleString()}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="body2">Distance:</Typography>
            <Typography variant="body2">
              {tenderLocation.distanceFromUser}km
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="body2">Travel Time:</Typography>
            <Typography variant="body2">
              {tenderLocation.travelTime} minutes
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="body2">Travel Cost:</Typography>
            <Typography variant="body2">
              R{tenderLocation.travelCost}
            </Typography>
          </Box>
        </Stack>

        {/* Psychological Stress Indicator */}
        <Alert 
          severity={tenderLocation.psychologicalStress! > 0.6 ? 'warning' : 'info'}
          sx={{ mb: 2 }}
        >
          <Typography variant="body2">
            🧠 <strong>Stress Level:</strong> {
              tenderLocation.psychologicalStress! > 0.6 ? 'High - Consider local alternatives' :
              tenderLocation.psychologicalStress! > 0.3 ? 'Medium - Manageable with planning' :
              'Low - Ideal for your well-being'
            }
          </Typography>
        </Alert>

        <Stack direction="row" spacing={1}>
          <Button
            variant="contained"
            size="small"
            onClick={() => calculateRoute(tenderLocation)}
            startIcon={<RouteIcon />}
          >
            Get Directions
          </Button>
          
          <Button
            variant="outlined"
            size="small"
            onClick={() => window.location.href = `/tenders/${tenderLocation.tender.id}`}
            startIcon={<ViewIcon />}
          >
            View Details
          </Button>
        </Stack>
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Card elevation={2} sx={{ m: 2, mb: 1 }}>
        <CardContent sx={{ py: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant={isHighCognitiveLoad ? "h5" : "h6"} sx={{ fontWeight: 'bold' }}>
              🗺️ Geographic Tender Discovery
            </Typography>
            
            <Stack direction="row" spacing={1}>
              <Tooltip title="Filters">
                <IconButton onClick={() => setFiltersOpen(true)}>
                  <Badge badgeContent={Object.values(filters).filter(Boolean).length} color="primary">
                    <FilterIcon />
                  </Badge>
                </IconButton>
              </Tooltip>
              
              <Tooltip title="My Location">
                <IconButton onClick={getUserLocation}>
                  <LocationIcon />
                </IconButton>
              </Tooltip>
              
              {!needsSimplification && (
                <Tooltip title="Psychology Mode">
                  <IconButton 
                    color={stressReducedColors ? 'primary' : 'default'}
                    onClick={() => setStressReducedColors(!stressReducedColors)}
                  >
                    <PsychologyIcon />
                  </IconButton>
                </Tooltip>
              )}
            </Stack>
          </Box>
          
          {isStressed && (
            <Alert severity="info" sx={{ mt: 1 }}>
              <Typography variant="body2">
                🧠 Stress-optimized view enabled. Showing only nearby, low-stress opportunities.
              </Typography>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Map */}
      <Box sx={{ flexGrow: 1, mx: 2, mb: 2 }}>
        <LoadScript googleMapsApiKey={process.env.REACT_APP_GOOGLE_MAPS_API_KEY || ''}>
          <GoogleMap
            mapContainerStyle={mapContainerStyle}
            center={userLocation || defaultCenter}
            zoom={simplifiedView ? 8 : 10}
            onLoad={setMap}
            options={{
              styles: stressReducedColors ? [
                {
                  featureType: 'all',
                  elementType: 'all',
                  stylers: [{ saturation: -20 }, { lightness: 10 }]
                }
              ] : undefined
            }}
          >
            {/* User Location Marker */}
            {userLocation && (
              <Marker
                position={userLocation}
                icon={{
                  url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="12" cy="12" r="8" fill="#2196F3" stroke="white" stroke-width="2"/>
                      <circle cx="12" cy="12" r="3" fill="white"/>
                    </svg>
                  `),
                  scaledSize: new window.google.maps.Size(24, 24)
                }}
                title="Your Location"
              />
            )}

            {/* Tender Markers */}
            {filteredTenderLocations.map((tenderLocation) => (
              <Marker
                key={tenderLocation.id}
                position={tenderLocation.position}
                onClick={() => handleMarkerClick(tenderLocation)}
                icon={{
                  url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
                    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="16" cy="16" r="12" fill="${getMarkerColor(tenderLocation)}" stroke="white" stroke-width="2"/>
                      <text x="16" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">R</text>
                    </svg>
                  `)}`,
                  scaledSize: new window.google.maps.Size(32, 32)
                }}
              />
            ))}

            {/* Info Window */}
            {showInfoWindow && selectedTender && (
              <InfoWindow
                position={selectedTender.position}
                onCloseClick={() => setShowInfoWindow(false)}
              >
                <div>{renderTenderInfo(selectedTender)}</div>
              </InfoWindow>
            )}

            {/* Directions */}
            {directionsResponse && (
              <DirectionsRenderer directions={directionsResponse} />
            )}

            {/* Heatmap */}
            {filters.showHeatmap && (
              <HeatmapLayer
                data={filteredTenderLocations.map(location => ({
                  location: new window.google.maps.LatLng(location.position.lat, location.position.lng),
                  weight: location.tender.value / 1000000 // Weight by value
                }))}
              />
            )}
          </GoogleMap>
        </LoadScript>
      </Box>

      {/* Filters Drawer */}
      <Drawer
        anchor="right"
        open={filtersOpen}
        onClose={() => setFiltersOpen(false)}
        sx={{ '& .MuiDrawer-paper': { width: 320, p: 2 } }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Map Filters</Typography>
          <IconButton onClick={() => setFiltersOpen(false)}>
            <CloseIcon />
          </IconButton>
        </Box>

        <Stack spacing={3}>
          {/* Radius Filter */}
          <Box>
            <Typography variant="body2" gutterBottom>
              Search Radius: {filters.radius}km
            </Typography>
            <Slider
              value={filters.radius}
              onChange={(e, value) => setFilters(prev => ({ ...prev, radius: value as number }))}
              min={10}
              max={500}
              step={10}
              marks={[
                { value: 50, label: '50km' },
                { value: 200, label: '200km' },
                { value: 500, label: '500km' }
              ]}
            />
          </Box>

          {/* Travel Time Filter */}
          <Box>
            <Typography variant="body2" gutterBottom>
              Max Travel Time: {filters.maxTravelTime} minutes
            </Typography>
            <Slider
              value={filters.maxTravelTime}
              onChange={(e, value) => setFilters(prev => ({ ...prev, maxTravelTime: value as number }))}
              min={15}
              max={300}
              step={15}
              marks={[
                { value: 60, label: '1h' },
                { value: 120, label: '2h' },
                { value: 240, label: '4h' }
              ]}
            />
          </Box>

          <Divider />

          {/* Psychological Options */}
          <Box>
            <Typography variant="body2" gutterBottom sx={{ fontWeight: 'bold' }}>
              🧠 Psychological Optimization
            </Typography>
            
            <FormControlLabel
              control={
                <Switch
                  checked={filters.stressOptimized}
                  onChange={(e) => setFilters(prev => ({ ...prev, stressOptimized: e.target.checked }))}
                />
              }
              label="Show only low-stress tenders"
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={stressReducedColors}
                  onChange={(e) => setStressReducedColors(e.target.checked)}
                />
              }
              label="Use calming colors"
            />
          </Box>

          <Divider />

          {/* Map Display Options */}
          <Box>
            <Typography variant="body2" gutterBottom sx={{ fontWeight: 'bold' }}>
              Map Display
            </Typography>
            
            <FormControlLabel
              control={
                <Switch
                  checked={filters.showHeatmap}
                  onChange={(e) => setFilters(prev => ({ ...prev, showHeatmap: e.target.checked }))}
                />
              }
              label="Show value heatmap"
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={filters.showClusters}
                  onChange={(e) => setFilters(prev => ({ ...prev, showClusters: e.target.checked }))}
                />
              }
              label="Cluster nearby tenders"
            />
          </Box>
        </Stack>
      </Drawer>
    </Box>
  );
};

export default TenderMap;
