/**
 * Tender Discovery - MVP Core with Progressive Features
 * Search and filter tenders with advanced features that can be toggled on
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  Pagination,
  Skeleton,
  Badge,
  Fab,
  Dialog,
  DialogContent,
  DialogTitle
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  LocationOn as LocationIcon,
  AttachMoney as MoneyIcon,
  Schedule as ScheduleIcon,
  Security as SecurityIcon,
  Psychology as PsychologyIcon,
  TrendingUp as TrendingUpIcon,
  Add as AddIcon,
  SmartToy as AutobidIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';
import {
  useFeatureFlags,
  useComplianceFeatures,
  useAnalyticsFeatures,
  FeatureGate
} from '../../hooks/useFeatureFlags';
import AutobidFeasibilityEngine from '../../components/autobid/AutobidFeasibilityEngine';
import AdaptiveInterface from '../../components/adaptive/AdaptiveInterface';

// Mock tender data - replace with real API
const mockTenders = [
  {
    id: 1,
    title: 'Road Maintenance and Repair Services',
    description: 'Comprehensive road maintenance services for municipal roads in Johannesburg area.',
    organization: 'City of Johannesburg',
    value: 2500000,
    location: 'Johannesburg, GP',
    deadline: '2024-02-15',
    category: 'Construction',
    status: 'Open',
    complianceRisk: 'Low',
    matchScore: 95,
    bookmarked: false,
    requirements: ['CIDB Grade 7', 'B-BBEE Level 4', 'Tax Compliant']
  },
  {
    id: 2,
    title: 'IT Infrastructure Upgrade Project',
    description: 'Complete IT infrastructure overhaul including servers, networking, and security systems.',
    organization: 'Department of Health - Western Cape',
    value: 8500000,
    location: 'Cape Town, WC',
    deadline: '2024-02-20',
    category: 'Technology',
    status: 'Open',
    complianceRisk: 'Medium',
    matchScore: 78,
    bookmarked: true,
    requirements: ['SITA Registration', 'ISO 27001', 'B-BBEE Level 3']
  },
  {
    id: 3,
    title: 'Cleaning Services for Government Buildings',
    description: 'Professional cleaning services for multiple government facilities.',
    organization: 'Department of Public Works',
    value: 1200000,
    location: 'Pretoria, GP',
    deadline: '2024-02-10',
    category: 'Services',
    status: 'Open',
    complianceRisk: 'Low',
    matchScore: 88,
    bookmarked: false,
    requirements: ['Tax Compliant', 'B-BBEE Level 2']
  }
];

const TenderDiscovery: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [tenders, setTenders] = useState(mockTenders);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [page, setPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);

  // Autobid dialog state
  const [autobidDialogOpen, setAutobidDialogOpen] = useState(false);
  const [selectedTenderForAutobid, setSelectedTenderForAutobid] = useState<any>(null);

  // Feature flags
  const { isFeatureEnabled } = useFeatureFlags();
  const { complianceToolsEnabled } = useComplianceFeatures();
  const { advancedAnalyticsEnabled, neuroMarketingEnabled } = useAnalyticsFeatures();

  // NeuroMarketing optimization (only if enabled)
  const neuroMarketing = neuroMarketingEnabled ? useNeuroMarketing() : null;
  const {
    psychologicalState,
    isStressed,
    needsSimplification,
    shouldShowHelp,
    shouldShowEncouragement
  } = neuroMarketing || {
    psychologicalState: null,
    isStressed: false,
    needsSimplification: false,
    shouldShowHelp: false,
    shouldShowEncouragement: false
  };

  // Psychological adaptations
  const getCardElevation = () => {
    return neuroMarketingEnabled && isStressed ? 1 : 2;
  };

  const getButtonSize = () => {
    return neuroMarketingEnabled && (psychologicalState?.cognitiveLoad ?? 0) > 0.7 ? 'small' : 'medium';
  };

  const getInformationDensity = () => {
    if (!neuroMarketingEnabled) return 'standard';
    if (needsSimplification) return 'minimal';
    return 'standard';
  };

  // Handle search
  const handleSearch = (event: React.FormEvent) => {
    event.preventDefault();
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  // Toggle bookmark
  const toggleBookmark = (tenderId: number) => {
    setTenders(prev =>
      prev.map(tender =>
        tender.id === tenderId
          ? { ...tender, bookmarked: !tender.bookmarked }
          : tender
      )
    );
  };

  // Handle autobid
  const handleAutobid = (tender: any) => {
    setSelectedTenderForAutobid(tender);
    setAutobidDialogOpen(true);
  };

  const handleAutobidStart = (feasibilityReport: any) => {
    console.log('Starting autobid with report:', feasibilityReport);
    setAutobidDialogOpen(false);
    // Navigate to AI bidding engine
    navigate(`/bids/ai-engine?tender=${selectedTenderForAutobid?.id}`);
  };

  const handleResourcesNeeded = (missingResources: any[]) => {
    console.log('Missing resources detected:', missingResources);
    // Could show a notification or redirect to resource onboarding
  };

  // Get compliance risk color
  const getComplianceRiskColor = (risk: string) => {
    switch (risk) {
      case 'Low': return 'success';
      case 'Medium': return 'warning';
      case 'High': return 'error';
      default: return 'default';
    }
  };

  // Render search and filters
  const renderSearchSection = () => (
    <Card elevation={getCardElevation()} sx={{ mb: 3 }}>
      <CardContent>
        <Box component="form" onSubmit={handleSearch}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search tenders..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
                size={getButtonSize()}
              />
            </Grid>

            <Grid item xs={12} md={2}>
              <FormControl fullWidth size={getButtonSize()}>
                <InputLabel>Category</InputLabel>
                <Select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  label="Category"
                >
                  <MenuItem value="">All Categories</MenuItem>
                  <MenuItem value="Construction">Construction</MenuItem>
                  <MenuItem value="Technology">Technology</MenuItem>
                  <MenuItem value="Services">Services</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={2}>
              <FormControl fullWidth size={getButtonSize()}>
                <InputLabel>Location</InputLabel>
                <Select
                  value={selectedLocation}
                  onChange={(e) => setSelectedLocation(e.target.value)}
                  label="Location"
                >
                  <MenuItem value="">All Locations</MenuItem>
                  <MenuItem value="Johannesburg">Johannesburg</MenuItem>
                  <MenuItem value="Cape Town">Cape Town</MenuItem>
                  <MenuItem value="Pretoria">Pretoria</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="contained"
                type="submit"
                size={getButtonSize()}
                disabled={loading}
              >
                Search
              </Button>
            </Grid>
          </Grid>
        </Box>

        {/* Advanced Filters (Feature-gated) */}
        <FeatureGate feature="advanced_analytics">
          <Box sx={{ mt: 2 }}>
            <Button
              startIcon={<FilterIcon />}
              onClick={() => setShowFilters(!showFilters)}
              size="small"
            >
              Advanced Filters
            </Button>
            {showFilters && (
              <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      label="Min Value (R)"
                      type="number"
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      label="Max Value (R)"
                      type="number"
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Compliance Risk</InputLabel>
                      <Select label="Compliance Risk">
                        <MenuItem value="">Any Risk</MenuItem>
                        <MenuItem value="Low">Low Risk</MenuItem>
                        <MenuItem value="Medium">Medium Risk</MenuItem>
                        <MenuItem value="High">High Risk</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Match Score</InputLabel>
                      <Select label="Match Score">
                        <MenuItem value="">Any Score</MenuItem>
                        <MenuItem value="90">90%+ Match</MenuItem>
                        <MenuItem value="80">80%+ Match</MenuItem>
                        <MenuItem value="70">70%+ Match</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>
            )}
          </Box>
        </FeatureGate>
      </CardContent>
    </Card>
  );

  // Render tender card
  const renderTenderCard = (tender: any) => (
    <Card key={tender.id} elevation={getCardElevation()} sx={{ mb: 2 }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" sx={{ mb: 1 }}>
              {tender.title}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              {tender.organization}
            </Typography>
            
            {getInformationDensity() !== 'minimal' && (
              <Typography variant="body2" sx={{ mb: 2 }}>
                {tender.description}
              </Typography>
            )}
          </Box>

          <IconButton onClick={() => toggleBookmark(tender.id)}>
            {tender.bookmarked ? (
              <BookmarkIcon color="primary" />
            ) : (
              <BookmarkBorderIcon />
            )}
          </IconButton>
        </Box>

        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <MoneyIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} />
              <Typography variant="body2">
                R{tender.value.toLocaleString()}
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <LocationIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} />
              <Typography variant="body2">
                {tender.location}
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ScheduleIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} />
              <Typography variant="body2">
                Due: {new Date(tender.deadline).toLocaleDateString()}
              </Typography>
            </Box>
          </Grid>

          {/* Advanced Analytics Features */}
          <FeatureGate feature="advanced_analytics">
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon fontSize="small" sx={{ mr: 0.5, color: 'success.main' }} />
                <Typography variant="body2" color="success.main">
                  {tender.matchScore}% Match
                </Typography>
              </Box>
            </Grid>
          </FeatureGate>
        </Grid>

        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
          <Chip label={tender.category} size="small" />
          <Chip label={tender.status} color="success" size="small" />
          
          {/* Compliance Risk (Feature-gated) */}
          <FeatureGate feature="sa_compliance_tools">
            <Chip 
              label={`${tender.complianceRisk} Risk`}
              color={getComplianceRiskColor(tender.complianceRisk)}
              size="small"
              icon={<SecurityIcon />}
            />
          </FeatureGate>
        </Box>

        {getInformationDensity() !== 'minimal' && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
              Requirements:
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {tender.requirements.map((req: string, index: number) => (
                <Chip key={index} label={req} variant="outlined" size="small" />
              ))}
            </Box>
          </Box>
        )}
      </CardContent>

      <CardActions>
        <Button 
          size={getButtonSize()}
          onClick={() => navigate(`/tenders/${tender.id}`)}
        >
          View Details
        </Button>
        <Button
          size={getButtonSize()}
          variant="contained"
          onClick={() => navigate(`/bids/create?tender=${tender.id}`)}
        >
          Create Bid
        </Button>

        {/* Autobid Button - Always Available */}
        <Button
          size={getButtonSize()}
          variant="contained"
          color="secondary"
          startIcon={<AutobidIcon />}
          onClick={() => handleAutobid(tender)}
          sx={{
            background: 'linear-gradient(45deg, #9c27b0 30%, #e91e63 90%)',
            fontWeight: 'bold',
            '&:hover': {
              background: 'linear-gradient(45deg, #7b1fa2 30%, #c2185b 90%)',
            }
          }}
        >
          🤖 AUTOBID
        </Button>

        {/* Compliance Analysis (Feature-gated) */}
        <FeatureGate feature="sa_compliance_tools">
          <Button
            size={getButtonSize()}
            color="success"
            startIcon={<SecurityIcon />}
            onClick={() => navigate(`/compliance/analyze?tender=${tender.id}`)}
          >
            Compliance Check
          </Button>
        </FeatureGate>
      </CardActions>
    </Card>
  );

  if (loading) {
    return (
      <AdaptiveInterface>
        <Box sx={{ p: 3 }}>
          {[1, 2, 3].map((item) => (
            <Skeleton key={item} variant="rectangular" height={200} sx={{ mb: 2 }} />
          ))}
        </Box>
      </AdaptiveInterface>
    );
  }

  return (
    <AdaptiveInterface>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 3 }}>
          <Typography 
            variant={needsSimplification ? 'h5' : 'h4'} 
            component="h1"
            sx={{ fontWeight: isStressed ? 400 : 500, mb: 1 }}
          >
            Discover Tenders
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Find and bid on government tenders that match your business
          </Typography>
        </Box>

        {/* NeuroMarketing Optimization Messages */}
        {neuroMarketingEnabled && isStressed && (
          <Alert 
            severity="info" 
            sx={{ mb: 3 }}
            icon={<PsychologyIcon />}
          >
            We've simplified the tender search to help you focus on the most relevant opportunities.
          </Alert>
        )}

        {shouldShowEncouragement && (
          <Alert severity="success" sx={{ mb: 3 }}>
            🎯 Great! You're actively searching for new opportunities. This is how successful businesses grow!
          </Alert>
        )}

        {/* Search Section */}
        {renderSearchSection()}

        {/* Results Summary */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            {tenders.length} tenders found
          </Typography>
          
          <FeatureGate feature="advanced_analytics">
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Chip 
                label={`${tenders.filter(t => t.matchScore >= 90).length} High Match`}
                color="success"
                size="small"
              />
              <Chip 
                label={`${tenders.filter(t => t.bookmarked).length} Bookmarked`}
                color="primary"
                size="small"
              />
            </Box>
          </FeatureGate>
        </Box>

        {/* Tender List */}
        <Box sx={{ mb: 3 }}>
          {tenders.map(renderTenderCard)}
        </Box>

        {/* Pagination */}
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          <Pagination 
            count={5} 
            page={page} 
            onChange={(e, value) => setPage(value)}
            color="primary"
          />
        </Box>

        {/* Floating Action Button for Mobile */}
        <Fab
          color="primary"
          aria-label="create bid"
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            display: { xs: 'flex', md: 'none' }
          }}
          onClick={() => navigate('/bids/create')}
        >
          <AddIcon />
        </Fab>

        {/* Autobid Feasibility Dialog */}
        <Dialog
          open={autobidDialogOpen}
          onClose={() => setAutobidDialogOpen(false)}
          maxWidth="lg"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <AutobidIcon color="primary" />
              <Typography variant="h6" fontWeight="bold">
                🤖 Autobid Feasibility Analysis
              </Typography>
            </Box>
          </DialogTitle>
          <DialogContent>
            {selectedTenderForAutobid && (
              <AutobidFeasibilityEngine
                tender={selectedTenderForAutobid}
                onAutobidStart={handleAutobidStart}
                onResourcesNeeded={handleResourcesNeeded}
              />
            )}
          </DialogContent>
        </Dialog>
      </Box>
    </AdaptiveInterface>
  );
};

export default TenderDiscovery;
