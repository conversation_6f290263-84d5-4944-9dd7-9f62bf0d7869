/**
 * SME Analyzer
 * Comprehensive SME compliance analysis and improvement recommendations
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  LinearProgress,
  Alert,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  CircularProgress,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Business as BusinessIcon,
  Assessment as AssessmentIcon,
  Lightbulb as LightbulbIcon,
  Psychology as PsychologyIcon,
  Help as HelpIcon,
  Star as StarIcon
} from '@mui/icons-material';
import { useNeuroMarketing, useComplianceOptimization } from '../../hooks/useNeuroMarketing';
import {
  useGetSMEProfileQuery,
  useAnalyzeSMEComplianceMutation,
  useGetSMEBenchmarksQuery
} from '../../services/api/compliance.api';
import { useAuth } from '../../contexts/AuthContext';
import AdaptiveInterface from '../../components/adaptive/AdaptiveInterface';
import { SMEComplianceProfile, SMEClassification } from '../../types/compliance';

const SMEAnalyzer: React.FC = () => {
  const { user } = useAuth();
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // NeuroMarketing optimization
  const {
    psychologicalState,
    isStressed,
    needsSimplification,
    shouldShowHelp,
    shouldShowEncouragement
  } = useNeuroMarketing();

  const {
    protestUIMode,
    stressLevel,
    showEncouragement,
    emphasizeSupport,
    simplifyLegalLanguage
  } = useComplianceOptimization();

  // API queries
  const {
    data: smeProfile,
    isLoading: profileLoading,
    error: profileError
  } = useGetSMEProfileQuery(user?.id || '');

  const {
    data: benchmarks,
    isLoading: benchmarksLoading
  } = useGetSMEBenchmarksQuery({
    tenantId: user?.id || '',
    filter: {}
  });

  const [analyzeSMECompliance] = useAnalyzeSMEComplianceMutation();

  // Psychological adaptations
  const getCardElevation = () => {
    return isStressed ? 1 : 3;
  };

  const getButtonSize = () => {
    return psychologicalState.cognitiveLoad > 0.7 ? 'large' : 'medium';
  };

  const getInformationDensity = () => {
    if (protestUIMode === 'simplified') return 'low';
    if (protestUIMode === 'detailed') return 'high';
    return 'medium';
  };

  const handleAnalyzeCompliance = async () => {
    if (!user?.id) return;

    setIsAnalyzing(true);
    try {
      const result = await analyzeSMECompliance(user.id).unwrap();
      setAnalysisResults(result.data);
    } catch (error) {
      console.error('SME analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getClassificationColor = (classification: SMEClassification) => {
    switch (classification) {
      case SMEClassification.MICRO:
        return 'success';
      case SMEClassification.VERY_SMALL:
        return 'info';
      case SMEClassification.SMALL:
        return 'warning';
      case SMEClassification.MEDIUM:
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getComplianceScoreColor = (score: number) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'error';
  };

  const renderComplianceScore = () => {
    if (!smeProfile) return null;

    const score = smeProfile.complianceScore;
    const color = getComplianceScoreColor(score);

    return (
      <Card elevation={getCardElevation()}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <AssessmentIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="h6">Compliance Score</Typography>
            {shouldShowHelp && (
              <Tooltip title="Your overall compliance strength for tender participation">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon />
                </IconButton>
              </Tooltip>
            )}
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box sx={{ position: 'relative', display: 'inline-flex', mr: 2 }}>
              <CircularProgress
                variant="determinate"
                value={score}
                size={80}
                thickness={4}
                color={color}
              />
              <Box
                sx={{
                  top: 0,
                  left: 0,
                  bottom: 0,
                  right: 0,
                  position: 'absolute',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Typography variant="h6" component="div" color="text.secondary">
                  {score}
                </Typography>
              </Box>
            </Box>

            <Box>
              <Typography variant="h4" color={`${color}.main`}>
                {score}/100
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {score >= 80 ? 'Excellent' : score >= 60 ? 'Good' : 'Needs Improvement'}
              </Typography>
            </Box>
          </Box>

          {showEncouragement && score < 70 && (
            <Alert severity="info" sx={{ mb: 2 }}>
              💪 Every improvement strengthens your competitive position!
            </Alert>
          )}

          <LinearProgress
            variant="determinate"
            value={score}
            color={color}
            sx={{ height: 8, borderRadius: 4 }}
          />

          {getInformationDensity() !== 'low' && benchmarks && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Industry Average: {benchmarks.industryAverage}% | 
                Your Ranking: {benchmarks.rankingPercentile}th percentile
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    );
  };

  const renderSMEProfile = () => {
    if (!smeProfile) return null;

    return (
      <Card elevation={getCardElevation()}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <BusinessIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="h6">SME Profile</Typography>
          </Box>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Classification
                </Typography>
                <Chip
                  label={smeProfile.smeClassification.replace('_', ' ').toUpperCase()}
                  color={getClassificationColor(smeProfile.smeClassification)}
                  variant="outlined"
                />
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Annual Turnover
                </Typography>
                <Typography variant="body1">
                  R{smeProfile.annualTurnover.toLocaleString()}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Employees
                </Typography>
                <Typography variant="body1">
                  {smeProfile.employeeCount}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Years in Business
                </Typography>
                <Typography variant="body1">
                  {smeProfile.yearsInBusiness} years
                </Typography>
              </Box>
            </Grid>

            {getInformationDensity() !== 'low' && (
              <>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Bid Success Rate
                    </Typography>
                    <Typography variant="body1" color="primary">
                      {smeProfile.bidSuccessRate}%
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Protest Success Rate
                    </Typography>
                    <Typography variant="body1" color="secondary">
                      {smeProfile.protestSuccessRate}%
                    </Typography>
                  </Box>
                </Grid>
              </>
            )}
          </Grid>
        </CardContent>
      </Card>
    );
  };

  const renderWeakAreas = () => {
    if (!smeProfile || !smeProfile.weakAreas.length) return null;

    return (
      <Card elevation={getCardElevation()}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <WarningIcon color="warning" sx={{ mr: 1 }} />
            <Typography variant="h6">Areas for Improvement</Typography>
          </Box>

          {emphasizeSupport && (
            <Alert severity="info" sx={{ mb: 2 }}>
              🎯 Focus on these areas to strengthen your competitive position
            </Alert>
          )}

          <List>
            {smeProfile.weakAreas.map((area, index) => (
              <ListItem key={index}>
                <ListItemIcon>
                  <WarningIcon color="warning" />
                </ListItemIcon>
                <ListItemText
                  primary={area}
                  secondary={
                    simplifyLegalLanguage ?
                      "This area needs attention to improve your tender success rate" :
                      "Consider addressing this compliance gap"
                  }
                />
              </ListItem>
            ))}
          </List>
        </CardContent>
      </Card>
    );
  };

  const renderRecommendations = () => {
    if (!analysisResults) return null;

    return (
      <Card elevation={getCardElevation()}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <LightbulbIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="h6">Improvement Recommendations</Typography>
          </Box>

          {analysisResults.recommendations.map((recommendation: string, index: number) => (
            <Accordion key={index}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="subtitle1">
                  {recommendation}
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography variant="body2">
                  {simplifyLegalLanguage ?
                    "This improvement will help you compete better in tenders and increase your success rate." :
                    "Detailed implementation guidance and expected impact on compliance score."}
                </Typography>
              </AccordionDetails>
            </Accordion>
          ))}

          {analysisResults.improvementPlan && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle1" sx={{ mb: 1 }}>
                Priority Action Plan:
              </Typography>
              {analysisResults.improvementPlan.map((action: any, index: number) => (
                <Chip
                  key={index}
                  label={action.title}
                  variant="outlined"
                  color="primary"
                  sx={{ mr: 1, mb: 1 }}
                  icon={<StarIcon />}
                />
              ))}
            </Box>
          )}
        </CardContent>
      </Card>
    );
  };

  if (profileLoading) {
    return (
      <AdaptiveInterface>
        <Box sx={{ p: 3 }}>
          <LinearProgress />
          <Typography variant="h6" sx={{ mt: 2, textAlign: 'center' }}>
            Loading your SME profile...
          </Typography>
        </Box>
      </AdaptiveInterface>
    );
  }

  return (
    <AdaptiveInterface>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography 
              variant={needsSimplification ? 'h5' : 'h4'} 
              component="h1"
              sx={{ fontWeight: isStressed ? 400 : 500 }}
            >
              SME Compliance Analyzer
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Understand your competitive position and improve your tender success rate
            </Typography>
          </Box>

          <Button
            variant="contained"
            onClick={handleAnalyzeCompliance}
            disabled={isAnalyzing}
            size={getButtonSize()}
            startIcon={isAnalyzing ? <CircularProgress size={20} /> : <TrendingUpIcon />}
          >
            {isAnalyzing ? 'Analyzing...' : 'Analyze Compliance'}
          </Button>
        </Box>

        {/* Stress level indicator */}
        {stressLevel === 'high' && (
          <Alert 
            severity="info" 
            sx={{ mb: 3 }}
            icon={<PsychologyIcon />}
          >
            We've simplified this analysis to focus on the most important insights for your business.
          </Alert>
        )}

        {/* Main Content */}
        <Grid container spacing={3}>
          {/* Left Column */}
          <Grid item xs={12} md={8}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                {renderComplianceScore()}
              </Grid>

              <Grid item xs={12}>
                {renderSMEProfile()}
              </Grid>

              {smeProfile?.weakAreas.length > 0 && (
                <Grid item xs={12}>
                  {renderWeakAreas()}
                </Grid>
              )}

              {analysisResults && (
                <Grid item xs={12}>
                  {renderRecommendations()}
                </Grid>
              )}
            </Grid>
          </Grid>

          {/* Right Column - Benchmarks */}
          <Grid item xs={12} md={4}>
            {benchmarks && !benchmarksLoading && (
              <Card elevation={getCardElevation()}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Industry Benchmarks
                  </Typography>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Industry Average
                    </Typography>
                    <Typography variant="h5" color="primary">
                      {benchmarks.industryAverage}%
                    </Typography>
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Your Ranking
                    </Typography>
                    <Typography variant="h5" color="secondary">
                      {benchmarks.rankingPercentile}th percentile
                    </Typography>
                  </Box>

                  {getInformationDensity() !== 'low' && benchmarks.peerComparison && (
                    <>
                      <Divider sx={{ my: 2 }} />
                      <Typography variant="subtitle2" sx={{ mb: 1 }}>
                        Peer Comparison
                      </Typography>
                      {benchmarks.peerComparison.slice(0, 3).map((peer: any, index: number) => (
                        <Box key={index} sx={{ mb: 1 }}>
                          <Typography variant="body2">
                            {peer.category}: {peer.score}%
                          </Typography>
                          <LinearProgress
                            variant="determinate"
                            value={peer.score}
                            sx={{ height: 4 }}
                          />
                        </Box>
                      ))}
                    </>
                  )}
                </CardContent>
              </Card>
            )}
          </Grid>
        </Grid>
      </Box>
    </AdaptiveInterface>
  );
};

export default SMEAnalyzer;
