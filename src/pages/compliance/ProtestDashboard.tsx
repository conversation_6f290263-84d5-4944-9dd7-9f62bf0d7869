/**
 * Bid Protest Dashboard
 * Main dashboard for managing bid protests with psychological optimization
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
  Alert,
  IconButton,
  Tooltip,
  Fab,
  Badge
} from '@mui/material';
import {
  Add as AddIcon,
  Warning as WarningIcon,
  Schedule as ScheduleIcon,
  Assessment as AssessmentIcon,
  TrendingUp as TrendingUpIcon,
  Help as HelpIcon,
  Psychology as PsychologyIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useNeuroMarketing, useComplianceOptimization } from '../../hooks/useNeuroMarketing';
import {
  useGetBidProtestsQuery,
  useGetComplianceDashboardQuery,
  useGetProtestDeadlinesQuery
} from '../../services/api/compliance.api';
import { useAuth } from '../../contexts/AuthContext';
import AdaptiveInterface from '../../components/adaptive/AdaptiveInterface';
import {
  BidProtest,
  ProtestStatus,
  ProtestDeadline,
  UrgencyStatus,
  ComplianceDashboardData
} from '../../types/compliance';

const ProtestDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [selectedTimeframe, setSelectedTimeframe] = useState<'week' | 'month' | 'quarter'>('month');
  
  // NeuroMarketing optimization
  const {
    psychologicalState,
    isStressed,
    needsSimplification,
    shouldShowHelp,
    shouldShowEncouragement
  } = useNeuroMarketing();
  
  const {
    protestUIMode,
    stressLevel,
    showEncouragement,
    breakIntoSteps
  } = useComplianceOptimization();

  // API queries
  const {
    data: protestsData,
    isLoading: protestsLoading,
    error: protestsError
  } = useGetBidProtestsQuery({
    page: 1,
    pageSize: 10,
    filter: {}
  });

  const {
    data: dashboardData,
    isLoading: dashboardLoading
  } = useGetComplianceDashboardQuery(user?.id || '');

  const {
    data: deadlinesData,
    isLoading: deadlinesLoading
  } = useGetProtestDeadlinesQuery({
    upcoming: true
  });

  // Psychological state-based UI adaptations
  const getCardElevation = () => {
    return isStressed ? 1 : 3;
  };

  const getButtonSize = () => {
    return psychologicalState.cognitiveLoad > 0.7 ? 'large' : 'medium';
  };

  const getInformationDensity = () => {
    if (protestUIMode === 'simplified') return 'low';
    if (protestUIMode === 'detailed') return 'high';
    return 'medium';
  };

  const handleCreateProtest = () => {
    if (breakIntoSteps) {
      // Guide user through step-by-step process
      navigate('/compliance/protests/wizard');
    } else {
      // Direct to creation form
      navigate('/compliance/protests/create');
    }
  };

  const getStatusColor = (status: ProtestStatus) => {
    switch (status) {
      case ProtestStatus.DRAFT:
        return 'default';
      case ProtestStatus.REVIEW:
        return 'warning';
      case ProtestStatus.SUBMITTED:
        return 'info';
      case ProtestStatus.RESOLVED:
        return 'success';
      case ProtestStatus.APPEALED:
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getUrgencyColor = (urgency: UrgencyStatus) => {
    switch (urgency) {
      case UrgencyStatus.CRITICAL:
        return 'error';
      case UrgencyStatus.URGENT:
        return 'warning';
      case UrgencyStatus.MODERATE:
        return 'info';
      default:
        return 'success';
    }
  };

  if (protestsLoading || dashboardLoading) {
    return (
      <AdaptiveInterface>
        <Box sx={{ p: 3 }}>
          <LinearProgress />
          <Typography variant="h6" sx={{ mt: 2, textAlign: 'center' }}>
            Loading your compliance dashboard...
          </Typography>
        </Box>
      </AdaptiveInterface>
    );
  }

  return (
    <AdaptiveInterface>
      <Box sx={{ p: 3 }}>
        {/* Header with psychological optimization */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography 
              variant={needsSimplification ? 'h5' : 'h4'} 
              component="h1"
              sx={{ fontWeight: isStressed ? 400 : 500 }}
            >
              Bid Protest Dashboard
            </Typography>
            {showEncouragement && (
              <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
                💪 You're taking control of your bidding rights - that's empowering!
              </Typography>
            )}
          </Box>
          
          {shouldShowHelp && (
            <Tooltip title="Get help with bid protests">
              <IconButton 
                color="primary" 
                onClick={() => navigate('/compliance/help')}
                size={getButtonSize()}
              >
                <HelpIcon />
              </IconButton>
            </Tooltip>
          )}
        </Box>

        {/* Stress level indicator for high-stress users */}
        {stressLevel === 'high' && (
          <Alert 
            severity="info" 
            sx={{ mb: 3 }}
            icon={<PsychologyIcon />}
          >
            We've simplified this dashboard to reduce complexity. Take your time and remember - 
            you have strong legal rights in the procurement process.
          </Alert>
        )}

        {/* Key Metrics Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card elevation={getCardElevation()}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <AssessmentIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">Active Protests</Typography>
                </Box>
                <Typography variant="h3" color="primary">
                  {dashboardData?.activeProtests || 0}
                </Typography>
                {getInformationDensity() !== 'low' && (
                  <Typography variant="body2" color="text.secondary">
                    Currently under review
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card elevation={getCardElevation()}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <ScheduleIcon color="warning" sx={{ mr: 1 }} />
                  <Typography variant="h6">Pending Deadlines</Typography>
                </Box>
                <Typography variant="h3" color="warning.main">
                  {dashboardData?.pendingDeadlines || 0}
                </Typography>
                {getInformationDensity() !== 'low' && (
                  <Typography variant="body2" color="text.secondary">
                    Require attention
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card elevation={getCardElevation()}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <TrendingUpIcon color="success" sx={{ mr: 1 }} />
                  <Typography variant="h6">Success Rate</Typography>
                </Box>
                <Typography variant="h3" color="success.main">
                  {dashboardData?.successRate || 0}%
                </Typography>
                {getInformationDensity() !== 'low' && (
                  <Typography variant="body2" color="text.secondary">
                    Historical average
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card elevation={getCardElevation()}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <AssessmentIcon color="info" sx={{ mr: 1 }} />
                  <Typography variant="h6">Compliance Score</Typography>
                </Box>
                <Typography variant="h3" color="info.main">
                  {dashboardData?.complianceScore || 0}
                </Typography>
                {getInformationDensity() !== 'low' && (
                  <LinearProgress 
                    variant="determinate" 
                    value={dashboardData?.complianceScore || 0} 
                    sx={{ mt: 1 }}
                  />
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Recent Protests and Upcoming Deadlines */}
        <Grid container spacing={3}>
          {/* Recent Protests */}
          <Grid item xs={12} md={8}>
            <Card elevation={getCardElevation()}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">Recent Protests</Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={handleCreateProtest}
                    size={getButtonSize()}
                  >
                    New Protest
                  </Button>
                </Box>

                {protestsData?.items.length === 0 ? (
                  <Box sx={{ textAlign: 'center', py: 4 }}>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                      No protests filed yet
                    </Typography>
                    {showEncouragement && (
                      <Typography variant="body2" color="primary">
                        When you encounter unfair tender processes, this is where you'll take action!
                      </Typography>
                    )}
                  </Box>
                ) : (
                  <Box>
                    {protestsData?.items.slice(0, needsSimplification ? 3 : 5).map((protest) => (
                      <Box
                        key={protest.id}
                        sx={{
                          p: 2,
                          mb: 2,
                          border: '1px solid',
                          borderColor: 'divider',
                          borderRadius: 1,
                          cursor: 'pointer',
                          '&:hover': {
                            backgroundColor: 'action.hover'
                          }
                        }}
                        onClick={() => navigate(`/compliance/protests/${protest.id}`)}
                      >
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                          <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                            {protest.tenderReference}
                          </Typography>
                          <Chip
                            label={protest.status}
                            color={getStatusColor(protest.status)}
                            size="small"
                          />
                        </Box>
                        
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          {protest.procuringEntity}
                        </Typography>
                        
                        {getInformationDensity() !== 'low' && (
                          <Typography variant="body2" noWrap>
                            {protest.description}
                          </Typography>
                        )}
                      </Box>
                    ))}
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Upcoming Deadlines */}
          <Grid item xs={12} md={4}>
            <Card elevation={getCardElevation()}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Upcoming Deadlines
                </Typography>

                {deadlinesData?.length === 0 ? (
                  <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
                    No upcoming deadlines
                  </Typography>
                ) : (
                  <Box>
                    {deadlinesData?.slice(0, needsSimplification ? 3 : 5).map((deadline) => (
                      <Box
                        key={deadline.id}
                        sx={{
                          p: 2,
                          mb: 1,
                          border: '1px solid',
                          borderColor: getUrgencyColor(deadline.urgencyStatus) + '.main',
                          borderRadius: 1,
                          backgroundColor: getUrgencyColor(deadline.urgencyStatus) + '.50'
                        }}
                      >
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                          <Typography variant="subtitle2">
                            {deadline.deadlineType.replace('_', ' ').toUpperCase()}
                          </Typography>
                          <Badge
                            badgeContent={deadline.daysRemaining}
                            color={getUrgencyColor(deadline.urgencyStatus)}
                          >
                            <ScheduleIcon />
                          </Badge>
                        </Box>
                        
                        <Typography variant="body2" color="text.secondary">
                          {new Date(deadline.deadline).toLocaleDateString()}
                        </Typography>
                        
                        {deadline.urgencyStatus === UrgencyStatus.CRITICAL && (
                          <Typography variant="caption" color="error" sx={{ fontWeight: 'bold' }}>
                            ⚠️ URGENT ACTION REQUIRED
                          </Typography>
                        )}
                      </Box>
                    ))}
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Floating Action Button for Quick Actions */}
        <Fab
          color="primary"
          aria-label="quick actions"
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            display: { xs: 'flex', md: 'none' } // Only show on mobile
          }}
          onClick={handleCreateProtest}
        >
          <AddIcon />
        </Fab>
      </Box>
    </AdaptiveInterface>
  );
};

export default ProtestDashboard;
