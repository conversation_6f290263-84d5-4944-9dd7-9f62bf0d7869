/**
 * Protest Submission Wizard
 * Step-by-step protest filing with SME focus and psychological optimization
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Button,
  Typography,
  Card,
  CardContent,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Help as HelpIcon,
  Psychology as PsychologyIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useNeuroMarketing, useComplianceOptimization } from '../../hooks/useNeuroMarketing';
import {
  useAnalyzeProtestViabilityMutation,
  useDetectIrregularitiesMutation,
  useCreateBidProtestMutation,
  useGenerateProtestDocumentMutation
} from '../../services/api/compliance.api';
import { useAuth } from '../../contexts/AuthContext';
import AdaptiveInterface from '../../components/adaptive/AdaptiveInterface';
import {
  ProtestGround,
  Jurisdiction,
  CreateProtestRequest,
  ProtestViabilityAnalysis,
  ComplianceIssue
} from '../../types/compliance';

// Form validation schema
const protestSchema = yup.object({
  tenderReference: yup.string().required('Tender reference is required'),
  procuringEntity: yup.string().required('Procuring entity is required'),
  issueType: yup.mixed<ProtestGround>().oneOf(Object.values(ProtestGround)).required('Issue type is required'),
  description: yup.string().min(50, 'Description must be at least 50 characters').required('Description is required'),
  estimatedValue: yup.number().positive('Value must be positive').required('Estimated value is required'),
  jurisdiction: yup.mixed<Jurisdiction>().oneOf(Object.values(Jurisdiction)).required('Jurisdiction is required'),
  awardDate: yup.string().optional(),
  smeSetaside: yup.number().optional().nullable()
});

interface ProtestFormData {
  tenderReference: string;
  procuringEntity: string;
  issueType: ProtestGround;
  description: string;
  estimatedValue: number;
  jurisdiction: Jurisdiction;
  awardDate?: string;
  smeSetaside?: number | null;
}

const ProtestWizard: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [activeStep, setActiveStep] = useState(0);
  const [viabilityAnalysis, setViabilityAnalysis] = useState<ProtestViabilityAnalysis | null>(null);
  const [detectedIssues, setDetectedIssues] = useState<ComplianceIssue[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // NeuroMarketing optimization
  const {
    psychologicalState,
    isStressed,
    needsSimplification,
    shouldShowHelp,
    shouldShowEncouragement
  } = useNeuroMarketing();

  const {
    protestUIMode,
    stressLevel,
    showEncouragement,
    breakIntoSteps,
    simplifyLegalLanguage,
    showCostWarnings,
    emphasizeSupport
  } = useComplianceOptimization();

  // API mutations
  const [analyzeViability] = useAnalyzeProtestViabilityMutation();
  const [detectIrregularities] = useDetectIrregularitiesMutation();
  const [createProtest] = useCreateBidProtestMutation();
  const [generateDocument] = useGenerateProtestDocumentMutation();

  // Form management
  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isValid }
  } = useForm<ProtestFormData>({
    resolver: yupResolver(protestSchema),
    mode: 'onChange'
  });

  const watchedValues = watch();

  // Steps configuration based on psychological state
  const getSteps = () => {
    const baseSteps = [
      'Tender Information',
      'Issue Description',
      'Viability Analysis',
      'Document Generation',
      'Review & Submit'
    ];

    if (breakIntoSteps || needsSimplification) {
      return [
        'Basic Information',
        'Tender Details',
        'Problem Description',
        'Success Analysis',
        'Legal Documents',
        'Final Review'
      ];
    }

    return baseSteps;
  };

  const steps = getSteps();

  // Psychological adaptations
  const getStepperOrientation = () => {
    return psychologicalState.cognitiveLoad > 0.7 ? 'vertical' : 'horizontal';
  };

  const getButtonSize = () => {
    return psychologicalState.cognitiveLoad > 0.7 ? 'small' : 'medium';
  };

  // Step 1: Tender Information
  const renderTenderInformation = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Tell us about the tender
        </Typography>
        
        {showEncouragement && (
          <Alert severity="info" sx={{ mb: 2 }}>
            💪 You're taking the right step to protect your business interests!
          </Alert>
        )}

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Controller
              name="tenderReference"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Tender Reference Number"
                  fullWidth
                  error={!!errors.tenderReference}
                  helperText={errors.tenderReference?.message}
                  size={getButtonSize()}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Controller
              name="procuringEntity"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Procuring Entity"
                  fullWidth
                  error={!!errors.procuringEntity}
                  helperText={errors.procuringEntity?.message}
                  size={getButtonSize()}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Controller
              name="estimatedValue"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Tender Value (R)"
                  type="number"
                  fullWidth
                  error={!!errors.estimatedValue}
                  helperText={errors.estimatedValue?.message}
                  size={getButtonSize()}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Controller
              name="jurisdiction"
              control={control}
              render={({ field }) => (
                <FormControl fullWidth error={!!errors.jurisdiction}>
                  <InputLabel>Jurisdiction</InputLabel>
                  <Select {...field} label="Jurisdiction" size={getButtonSize()}>
                    <MenuItem value={Jurisdiction.NATIONAL}>National</MenuItem>
                    <MenuItem value={Jurisdiction.PROVINCIAL}>Provincial</MenuItem>
                    <MenuItem value={Jurisdiction.MUNICIPAL}>Municipal</MenuItem>
                    <MenuItem value={Jurisdiction.SOE}>State-Owned Enterprise</MenuItem>
                  </Select>
                </FormControl>
              )}
            />
          </Grid>

          {!needsSimplification && (
            <>
              <Grid item xs={12} md={6}>
                <Controller
                  name="awardDate"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Award Date"
                      type="date"
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      size={getButtonSize()}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="smeSetaside"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="SME Set-aside (%)"
                      type="number"
                      fullWidth
                      helperText="If applicable"
                      size={getButtonSize()}
                    />
                  )}
                />
              </Grid>
            </>
          )}
        </Grid>
      </CardContent>
    </Card>
  );

  // Step 2: Issue Description
  const renderIssueDescription = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2 }}>
          What's the problem?
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Controller
              name="issueType"
              control={control}
              render={({ field }) => (
                <FormControl fullWidth error={!!errors.issueType}>
                  <InputLabel>Type of Issue</InputLabel>
                  <Select {...field} label="Type of Issue" size={getButtonSize()}>
                    <MenuItem value={ProtestGround.SCORING}>Evaluation Scoring Issues</MenuItem>
                    <MenuItem value={ProtestGround.SPECS}>Unfair Technical Specifications</MenuItem>
                    <MenuItem value={ProtestGround.BBBEE}>B-BBEE Compliance Problems</MenuItem>
                    <MenuItem value={ProtestGround.SME_SETASIDE}>SME Set-aside Non-compliance</MenuItem>
                    <MenuItem value={ProtestGround.UNFAIR_SPECS}>Specifications Favor Large Companies</MenuItem>
                    <MenuItem value={ProtestGround.INADEQUATE_BRIEFING}>Insufficient Tender Briefing</MenuItem>
                    <MenuItem value={ProtestGround.DEADLINE}>Submission Deadline Issues</MenuItem>
                    <MenuItem value={ProtestGround.PRICE}>Unfair Price Evaluation</MenuItem>
                  </Select>
                </FormControl>
              )}
            />
          </Grid>

          <Grid item xs={12}>
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Detailed Description"
                  multiline
                  rows={needsSimplification ? 3 : 5}
                  fullWidth
                  error={!!errors.description}
                  helperText={
                    errors.description?.message || 
                    (simplifyLegalLanguage ? 
                      "Explain in simple terms what went wrong" : 
                      "Provide detailed explanation of the irregularity")
                  }
                  placeholder={
                    simplifyLegalLanguage ?
                      "Example: The tender required 10 years experience, but similar work only needs 5 years. This seems designed to exclude smaller companies like ours." :
                      "Describe the specific irregularities, procedural violations, or unfair practices you observed..."
                  }
                />
              )}
            />
          </Grid>
        </Grid>

        {shouldShowHelp && (
          <Accordion sx={{ mt: 2 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>Need help describing the issue?</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2">
                Common issues include:
                • Evaluation criteria that seem designed for specific companies
                • Requirements that are unnecessarily restrictive
                • Scoring that doesn't match the tender requirements
                • Missing or inadequate briefing sessions
                • B-BBEE requirements not properly applied
              </Typography>
            </AccordionDetails>
          </Accordion>
        )}
      </CardContent>
    </Card>
  );

  // Step 3: Viability Analysis
  const renderViabilityAnalysis = () => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <TrendingUpIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="h6">Success Probability Analysis</Typography>
          {shouldShowHelp && (
            <Tooltip title="We analyze your chances of success based on your compliance status and the issue type">
              <IconButton size="small" sx={{ ml: 1 }}>
                <HelpIcon />
              </IconButton>
            </Tooltip>
          )}
        </Box>

        {!viabilityAnalysis ? (
          <Box sx={{ textAlign: 'center', py: 3 }}>
            <Button
              variant="contained"
              onClick={handleAnalyzeViability}
              disabled={!isValid || isAnalyzing}
              size={getButtonSize()}
              startIcon={isAnalyzing ? undefined : <TrendingUpIcon />}
            >
              {isAnalyzing ? 'Analyzing...' : 'Analyze Success Probability'}
            </Button>
            {isAnalyzing && <LinearProgress sx={{ mt: 2 }} />}
          </Box>
        ) : (
          <Box>
            <Alert 
              severity={viabilityAnalysis.viable ? 'success' : 'warning'}
              sx={{ mb: 2 }}
            >
              <Typography variant="h6">
                Success Probability: {viabilityAnalysis.score}%
              </Typography>
              <Typography variant="body2">
                {viabilityAnalysis.viable ? 
                  'Your protest has a good chance of success!' : 
                  'Consider strengthening your case before proceeding.'}
              </Typography>
            </Alert>

            <Typography variant="subtitle1" sx={{ mb: 1 }}>
              Recommendations:
            </Typography>
            {viabilityAnalysis.recommendations.map((rec, index) => (
              <Chip
                key={index}
                label={rec}
                variant="outlined"
                sx={{ mr: 1, mb: 1 }}
                size="small"
              />
            ))}

            {showCostWarnings && viabilityAnalysis.estimatedCost && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                <Typography variant="subtitle2">Cost Consideration:</Typography>
                <Typography variant="body2">
                  Estimated cost: {viabilityAnalysis.estimatedCost.estimatedCost}
                </Typography>
              </Alert>
            )}
          </Box>
        )}
      </CardContent>
    </Card>
  );

  const handleAnalyzeViability = async () => {
    if (!isValid) return;

    setIsAnalyzing(true);
    try {
      const result = await analyzeViability({
        tenantId: user?.id || '',
        tenderData: {
          value: watchedValues.estimatedValue,
          jurisdiction: watchedValues.jurisdiction,
          smeSetaside: watchedValues.smeSetaside ?? undefined // Convert null to undefined
        }
      }).unwrap();

      setViabilityAnalysis(result.data!);
    } catch (error) {
      console.error('Viability analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const onSubmitProtest = async (data: ProtestFormData) => {
    try {
      const protestRequest: CreateProtestRequest = {
        tenderReference: data.tenderReference,
        procuringEntity: data.procuringEntity,
        issueType: data.issueType,
        description: data.description,
        deadline: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000).toISOString() // 21 days from now
      };

      const result = await createProtest(protestRequest).unwrap();
      
      if (result.success) {
        navigate(`/compliance/protests/${result.data!.id}`);
      }
    } catch (error) {
      console.error('Failed to create protest:', error);
    }
  };

  return (
    <AdaptiveInterface>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" sx={{ mb: 3 }}>
          File a Bid Protest
        </Typography>

        {stressLevel === 'high' && (
          <Alert severity="info" sx={{ mb: 3 }} icon={<PsychologyIcon />}>
            We've broken this process into simple steps. Take your time - you're protecting your business rights.
          </Alert>
        )}

        <Stepper 
          activeStep={activeStep} 
          orientation={getStepperOrientation()}
          sx={{ mb: 3 }}
        >
          {steps.map((label, index) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
              {getStepperOrientation() === 'vertical' && (
                <StepContent>
                  {index === 0 && renderTenderInformation()}
                  {index === 1 && renderIssueDescription()}
                  {index === 2 && renderViabilityAnalysis()}
                </StepContent>
              )}
            </Step>
          ))}
        </Stepper>

        {getStepperOrientation() === 'horizontal' && (
          <Box sx={{ mb: 3 }}>
            {activeStep === 0 && renderTenderInformation()}
            {activeStep === 1 && renderIssueDescription()}
            {activeStep === 2 && renderViabilityAnalysis()}
          </Box>
        )}

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
          <Button
            disabled={activeStep === 0}
            onClick={handleBack}
            size={getButtonSize()}
          >
            Back
          </Button>
          
          <Button
            variant="contained"
            onClick={activeStep === steps.length - 1 ? handleSubmit(onSubmitProtest) : handleNext}
            disabled={!isValid}
            size={getButtonSize()}
          >
            {activeStep === steps.length - 1 ? 'Submit Protest' : 'Next'}
          </Button>
        </Box>
      </Box>
    </AdaptiveInterface>
  );
};

export default ProtestWizard;
