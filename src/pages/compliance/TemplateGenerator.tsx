/**
 * Template Generator
 * Generate professional compliance documents with SME focus and psychological optimization
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Chip,
  Alert,
  <PERSON>per,
  Step,
  StepLabel,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Description as DescriptionIcon,
  Download as DownloadIcon,
  Preview as PreviewIcon,
  Psychology as PsychologyIcon,
  Help as HelpIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Star as StarIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { useNeuroMarketing, useComplianceOptimization } from '../../hooks/useNeuroMarketing';
import {
  useGetDocumentTemplatesQuery,
  useGenerateProtestDocumentMutation,
  useDownloadDocumentMutation
} from '../../services/api/compliance.api';
import { useAuth } from '../../contexts/AuthContext';
import AdaptiveInterface from '../../components/adaptive/AdaptiveInterface';
import {
  DocumentType,
  DocumentTemplate,
  EscalationLevel,
  GenerateDocumentRequest
} from '../../types/compliance';

interface TemplateFormData {
  templateType: DocumentType;
  protestId?: string;
  customizations: {
    tone: 'professional' | 'assertive' | 'diplomatic';
    urgency: 'low' | 'medium' | 'high';
    includeEvidence: boolean;
    includeLegalReferences: boolean;
    smeSpecific: boolean;
  };
}

const TemplateGenerator: React.FC = () => {
  const { user } = useAuth();
  const [selectedTemplate, setSelectedTemplate] = useState<DocumentTemplate | null>(null);
  const [generatedDocument, setGeneratedDocument] = useState<any>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [activeStep, setActiveStep] = useState(0);

  // NeuroMarketing optimization
  const {
    psychologicalState,
    isStressed,
    needsSimplification,
    shouldShowHelp,
    shouldShowEncouragement
  } = useNeuroMarketing();

  const {
    protestUIMode,
    stressLevel,
    showEncouragement,
    breakIntoSteps,
    simplifyLegalLanguage,
    showCostWarnings,
    emphasizeSupport
  } = useComplianceOptimization();

  // API queries
  const {
    data: templates,
    isLoading: templatesLoading
  } = useGetDocumentTemplatesQuery({
    isSMEFocused: true
  });

  const [generateDocument] = useGenerateProtestDocumentMutation();
  const [downloadDocument] = useDownloadDocumentMutation();

  // Form management
  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isValid }
  } = useForm<TemplateFormData>({
    defaultValues: {
      customizations: {
        tone: 'professional',
        urgency: 'medium',
        includeEvidence: true,
        includeLegalReferences: !simplifyLegalLanguage,
        smeSpecific: true
      }
    }
  });

  const watchedValues = watch();

  // Psychological adaptations
  const getCardElevation = () => {
    return isStressed ? 1 : 3;
  };

  const getButtonSize = () => {
    return psychologicalState.cognitiveLoad > 0.7 ? 'large' : 'medium';
  };

  const getSteps = () => {
    if (breakIntoSteps || needsSimplification) {
      return [
        'Choose Document Type',
        'Select Template',
        'Customize Content',
        'Review & Generate'
      ];
    }
    return ['Template Selection', 'Customization', 'Generation'];
  };

  const steps = getSteps();

  const getTemplatesByType = () => {
    if (!templates) return {};
    
    return templates.reduce((acc, template) => {
      if (!acc[template.templateType]) {
        acc[template.templateType] = [];
      }
      acc[template.templateType].push(template);
      return acc;
    }, {} as Record<DocumentType, DocumentTemplate[]>);
  };

  const getEscalationLevelColor = (level: EscalationLevel) => {
    switch (level) {
      case EscalationLevel.INFORMATION_SEEKING:
        return 'success';
      case EscalationLevel.FORMAL_INQUIRY:
        return 'info';
      case EscalationLevel.PROTEST:
        return 'warning';
      case EscalationLevel.APPEAL:
        return 'error';
      default:
        return 'default';
    }
  };

  const getEscalationLevelLabel = (level: EscalationLevel) => {
    switch (level) {
      case EscalationLevel.INFORMATION_SEEKING:
        return 'Information Request';
      case EscalationLevel.FORMAL_INQUIRY:
        return 'Formal Inquiry';
      case EscalationLevel.PROTEST:
        return 'Bid Protest';
      case EscalationLevel.APPEAL:
        return 'Appeal';
      default:
        return 'Unknown';
    }
  };

  const renderTemplateSelection = () => {
    const templatesByType = getTemplatesByType();

    return (
      <Card elevation={getCardElevation()}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Choose Document Type
          </Typography>

          {showEncouragement && (
            <Alert severity="info" sx={{ mb: 2 }}>
              💼 Professional templates designed specifically for SMEs like yours!
            </Alert>
          )}

          <Grid container spacing={2}>
            {Object.entries(templatesByType).map(([type, typeTemplates]) => (
              <Grid item xs={12} sm={6} md={4} key={type}>
                <Card
                  variant="outlined"
                  sx={{
                    cursor: 'pointer',
                    '&:hover': {
                      backgroundColor: 'action.hover'
                    },
                    border: selectedTemplate?.templateType === type ? 2 : 1,
                    borderColor: selectedTemplate?.templateType === type ? 'primary.main' : 'divider'
                  }}
                  onClick={() => setSelectedTemplate(typeTemplates[0])}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <DescriptionIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1">
                        {type.replace('_', ' ').toUpperCase()}
                      </Typography>
                    </Box>

                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {getTemplateDescription(type as DocumentType)}
                    </Typography>

                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {typeTemplates.map((template) => (
                        <Chip
                          key={template.id}
                          label={getEscalationLevelLabel(template.escalationLevel)}
                          color={getEscalationLevelColor(template.escalationLevel)}
                          size="small"
                          variant="outlined"
                        />
                      ))}
                    </Box>

                    {typeTemplates[0]?.successRate && (
                      <Box sx={{ mt: 1 }}>
                        <Typography variant="caption" color="success.main">
                          ✓ {typeTemplates[0].successRate}% success rate
                        </Typography>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {shouldShowHelp && (
            <Accordion sx={{ mt: 2 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>Which document type should I choose?</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <List>
                  <ListItem>
                    <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                    <ListItemText
                      primary="Request for Information (RFI)"
                      secondary="Start here - ask for clarification before formal protest"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><WarningIcon color="warning" /></ListItemIcon>
                    <ListItemText
                      primary="Formal Protest"
                      secondary="When you have clear evidence of irregularities"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><StarIcon color="info" /></ListItemIcon>
                    <ListItemText
                      primary="Appeal Letter"
                      secondary="If your initial protest was unsuccessful"
                    />
                  </ListItem>
                </List>
              </AccordionDetails>
            </Accordion>
          )}
        </CardContent>
      </Card>
    );
  };

  const renderCustomization = () => {
    if (!selectedTemplate) return null;

    return (
      <Card elevation={getCardElevation()}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Customize Your Document
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Controller
                name="customizations.tone"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth>
                    <InputLabel>Document Tone</InputLabel>
                    <Select {...field} label="Document Tone" size={getButtonSize()}>
                      <MenuItem value="professional">Professional</MenuItem>
                      <MenuItem value="assertive">Assertive</MenuItem>
                      <MenuItem value="diplomatic">Diplomatic</MenuItem>
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="customizations.urgency"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth>
                    <InputLabel>Urgency Level</InputLabel>
                    <Select {...field} label="Urgency Level" size={getButtonSize()}>
                      <MenuItem value="low">Low</MenuItem>
                      <MenuItem value="medium">Medium</MenuItem>
                      <MenuItem value="high">High</MenuItem>
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle1" sx={{ mb: 1 }}>
                Include in Document:
              </Typography>
              
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                <Controller
                  name="customizations.includeEvidence"
                  control={control}
                  render={({ field }) => (
                    <Chip
                      label="Compliance Evidence"
                      color={field.value ? 'primary' : 'default'}
                      onClick={() => field.onChange(!field.value)}
                      variant={field.value ? 'filled' : 'outlined'}
                    />
                  )}
                />

                <Controller
                  name="customizations.includeLegalReferences"
                  control={control}
                  render={({ field }) => (
                    <Chip
                      label={simplifyLegalLanguage ? "Legal Framework" : "Legal References"}
                      color={field.value ? 'primary' : 'default'}
                      onClick={() => field.onChange(!field.value)}
                      variant={field.value ? 'filled' : 'outlined'}
                    />
                  )}
                />

                <Controller
                  name="customizations.smeSpecific"
                  control={control}
                  render={({ field }) => (
                    <Chip
                      label="SME-Specific Content"
                      color={field.value ? 'primary' : 'default'}
                      onClick={() => field.onChange(!field.value)}
                      variant={field.value ? 'filled' : 'outlined'}
                    />
                  )}
                />
              </Box>
            </Grid>

            {showCostWarnings && selectedTemplate.escalationLevel >= EscalationLevel.PROTEST && (
              <Grid item xs={12}>
                <Alert severity="warning">
                  <Typography variant="subtitle2">Cost Consideration:</Typography>
                  <Typography variant="body2">
                    Formal protests may incur legal costs. Consider starting with a Request for Information.
                  </Typography>
                </Alert>
              </Grid>
            )}
          </Grid>
        </CardContent>
      </Card>
    );
  };

  const renderPreview = () => {
    if (!selectedTemplate) return null;

    return (
      <Card elevation={getCardElevation()}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Document Preview</Typography>
            <Box>
              <Button
                variant="outlined"
                startIcon={<PreviewIcon />}
                onClick={() => setPreviewOpen(true)}
                sx={{ mr: 1 }}
                size={getButtonSize()}
              >
                Preview
              </Button>
              <Button
                variant="contained"
                startIcon={<DescriptionIcon />}
                onClick={handleGenerateDocument}
                disabled={isGenerating}
                size={getButtonSize()}
              >
                {isGenerating ? 'Generating...' : 'Generate Document'}
              </Button>
            </Box>
          </Box>

          {isGenerating && <LinearProgress sx={{ mb: 2 }} />}

          <Box sx={{ p: 2, backgroundColor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="subtitle1" sx={{ mb: 1 }}>
              {selectedTemplate.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {getTemplateDescription(selectedTemplate.templateType)}
            </Typography>

            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              <Chip
                label={getEscalationLevelLabel(selectedTemplate.escalationLevel)}
                color={getEscalationLevelColor(selectedTemplate.escalationLevel)}
                size="small"
              />
              {selectedTemplate.isSMEFocused && (
                <Chip label="SME Optimized" color="success" size="small" />
              )}
              <Chip
                label={`${selectedTemplate.successRate}% Success Rate`}
                color="info"
                size="small"
              />
            </Box>

            {emphasizeSupport && (
              <Alert severity="info" sx={{ mt: 2 }}>
                🎯 This template is specifically designed for SMEs and includes guidance for your situation.
              </Alert>
            )}
          </Box>
        </CardContent>
      </Card>
    );
  };

  const handleGenerateDocument = async () => {
    if (!selectedTemplate) return;

    setIsGenerating(true);
    try {
      const request: GenerateDocumentRequest = {
        protestId: watchedValues.protestId || '',
        templateType: selectedTemplate.templateType,
        customizations: watchedValues.customizations,
        includeEvidence: watchedValues.customizations.includeEvidence
      };

      const result = await generateDocument(request).unwrap();
      setGeneratedDocument(result.data);
    } catch (error) {
      console.error('Document generation failed:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownload = async () => {
    if (!generatedDocument) return;

    try {
      const blob = await downloadDocument(generatedDocument.id).unwrap();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = generatedDocument.title;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const getTemplateDescription = (type: DocumentType): string => {
    switch (type) {
      case DocumentType.RFI:
        return 'Request information before filing a formal protest';
      case DocumentType.FORMAL_PROTEST:
        return 'Official protest against tender irregularities';
      case DocumentType.APPEAL_LETTER:
        return 'Appeal an unsuccessful protest decision';
      case DocumentType.CLARIFICATION_REQUEST:
        return 'Ask for clarification during tender process';
      default:
        return 'Professional compliance document';
    }
  };

  if (templatesLoading) {
    return (
      <AdaptiveInterface>
        <Box sx={{ p: 3 }}>
          <LinearProgress />
          <Typography variant="h6" sx={{ mt: 2, textAlign: 'center' }}>
            Loading document templates...
          </Typography>
        </Box>
      </AdaptiveInterface>
    );
  }

  return (
    <AdaptiveInterface>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 3 }}>
          <Typography 
            variant={needsSimplification ? 'h5' : 'h4'} 
            component="h1"
            sx={{ fontWeight: isStressed ? 400 : 500, mb: 1 }}
          >
            Document Generator
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Generate professional compliance documents tailored for SMEs
          </Typography>
        </Box>

        {/* Stress level indicator */}
        {stressLevel === 'high' && (
          <Alert 
            severity="info" 
            sx={{ mb: 3 }}
            icon={<PsychologyIcon />}
          >
            We've simplified the document generation process. Each template includes guidance specifically for SMEs.
          </Alert>
        )}

        {/* Stepper for complex flows */}
        {breakIntoSteps && (
          <Stepper activeStep={activeStep} sx={{ mb: 3 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        )}

        {/* Main Content */}
        <Grid container spacing={3}>
          <Grid item xs={12}>
            {renderTemplateSelection()}
          </Grid>

          {selectedTemplate && (
            <>
              <Grid item xs={12} md={6}>
                {renderCustomization()}
              </Grid>
              <Grid item xs={12} md={6}>
                {renderPreview()}
              </Grid>
            </>
          )}
        </Grid>

        {/* Generated Document Actions */}
        {generatedDocument && (
          <Card elevation={getCardElevation()} sx={{ mt: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Box>
                  <Typography variant="h6" color="success.main">
                    ✓ Document Generated Successfully
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {generatedDocument.title}
                  </Typography>
                </Box>
                <Button
                  variant="contained"
                  startIcon={<DownloadIcon />}
                  onClick={handleDownload}
                  size={getButtonSize()}
                >
                  Download
                </Button>
              </Box>
            </CardContent>
          </Card>
        )}

        {/* Preview Dialog */}
        <Dialog
          open={previewOpen}
          onClose={() => setPreviewOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Document Preview</DialogTitle>
          <DialogContent>
            <Typography variant="body2">
              Preview functionality would show the generated document content here.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setPreviewOpen(false)}>Close</Button>
          </DialogActions>
        </Dialog>
      </Box>
    </AdaptiveInterface>
  );
};

export default TemplateGenerator;
