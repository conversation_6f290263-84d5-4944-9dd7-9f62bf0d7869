/**
 * SA Compliance Tools with Behavioral UX
 * Reduces legal anxiety and builds confidence through psychological design
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  LinearProgress,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Stepper,
  Step,
  StepLabel,
  IconButton,
  Tooltip,
  Stack,
  Divider,
  Paper
} from '@mui/material';
import {
  Security as SecurityIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Psychology as PsychologyIcon,
  Lightbulb as TipIcon,
  AutoAwesome as AIIcon,
  School as LearnIcon,
  Support as SupportIcon,
  ExpandMore as ExpandIcon,
  Timer as TimerIcon,
  EmojiEvents as AchievementIcon,
  Verified as VerifiedIcon,
  Assignment as DocumentIcon
} from '@mui/icons-material';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';
import { ComplianceRequirement, DocumentStatus, Priority } from '../../types/tender.types';

interface TenderComplianceIntelligence {
  id: string;
  title: string;
  value: number;
  urgency: 'critical' | 'high' | 'medium' | 'low';
  complianceGaps: string[];
  requiredDocuments: string[];
  bbbeeRequirement: number;
  psychTrigger: string;
  blockedReason: string;
  estimatedCost: number;
  timeToComplete: string;
}

interface ComplianceOverviewProps {
  bidId?: string;
  tenderId?: string;
}

const ComplianceOverview: React.FC<ComplianceOverviewProps> = ({ bidId, tenderId }) => {
  // Behavioral tracking
  const {
    psychologicalState,
    isStressed,
    isHighCognitiveLoad,
    needsSimplification,
    trackEngagement,
    trackStress,
    getPersonalizedRecommendations
  } = useNeuroMarketing();

  // State management
  const [complianceData, setComplianceData] = useState<ComplianceRequirement[]>([]);
  const [overallScore, setOverallScore] = useState(0);
  const [showAnxietyReduction, setShowAnxietyReduction] = useState(false);
  const [confidenceLevel, setConfidenceLevel] = useState<'low' | 'medium' | 'high'>('medium');
  const [showLegalSupport, setShowLegalSupport] = useState(false);
  const [completedRequirements, setCompletedRequirements] = useState(0);

  // Tender Intelligence State
  const [tenderIntelligence, setTenderIntelligence] = useState<TenderComplianceIntelligence[]>([
    {
      id: 'tender-001',
      title: 'Municipal Infrastructure - R15.6M',
      value: ********,
      urgency: 'critical',
      complianceGaps: ['B-BBEE Level 4 Certificate', 'ISO 9001:2015', 'Tax Clearance'],
      requiredDocuments: ['Company Registration', 'B-BBEE Certificate', 'Tax Clearance', 'Bank Details'],
      bbbeeRequirement: 4,
      psychTrigger: 'COMPLIANCE CRISIS: Missing B-BBEE blocks R15.6M tender!',
      blockedReason: 'B-BBEE Level 4 certificate required - currently Level 6',
      estimatedCost: 45000,
      timeToComplete: '6-8 weeks'
    },
    {
      id: 'tender-002',
      title: 'Highway Construction - R22.4M',
      value: ********,
      urgency: 'high',
      complianceGaps: ['CIDB Grade 7+', 'Health & Safety Certificate'],
      requiredDocuments: ['CIDB Certificate', 'Health & Safety Plan', 'Insurance Certificate'],
      bbbeeRequirement: 7,
      psychTrigger: 'EXCLUSIVE ACCESS: You qualify for Grade 7+ tender!',
      blockedReason: 'Health & Safety certification pending',
      estimatedCost: 25000,
      timeToComplete: '3-4 weeks'
    }
  ]);

  // Load compliance data
  useEffect(() => {
    loadComplianceRequirements();
  }, [bidId, tenderId]);

  // Monitor stress levels for legal anxiety
  useEffect(() => {
    if (psychologicalState?.stressLevel > 0.6) {
      setShowAnxietyReduction(true);
      setShowLegalSupport(true);
      trackStress('legal_compliance_anxiety', psychologicalState.stressLevel);
    }
  }, [psychologicalState]);

  const loadComplianceRequirements = () => {
    // Mock compliance data with psychological impact assessment
    const mockRequirements: ComplianceRequirement[] = [
      {
        id: 'req-1',
        name: 'B-BBEE Certificate',
        description: 'Valid Broad-Based Black Economic Empowerment certificate (Level 4 or better required)',
        mandatory: true,
        documentType: 'certificate',
        status: DocumentStatus.COMPLETED,
        psychologicalImpact: 'low',
        dueDate: '2024-02-20T17:00:00Z',
        submittedDate: '2024-01-15T10:30:00Z',
        verifiedDate: '2024-01-16T14:20:00Z'
      },
      {
        id: 'req-2',
        name: 'Tax Compliance Status',
        description: 'Tax clearance certificate from SARS showing good standing',
        mandatory: true,
        documentType: 'certificate',
        status: DocumentStatus.IN_PROGRESS,
        psychologicalImpact: 'medium',
        dueDate: '2024-02-20T17:00:00Z'
      },
      {
        id: 'req-3',
        name: 'Professional Indemnity Insurance',
        description: 'Professional indemnity insurance cover of minimum R5 million',
        mandatory: true,
        documentType: 'insurance',
        status: DocumentStatus.NOT_STARTED,
        psychologicalImpact: 'high',
        dueDate: '2024-02-20T17:00:00Z'
      },
      {
        id: 'req-4',
        name: 'Company Registration',
        description: 'Certificate of incorporation and company registration documents',
        mandatory: true,
        documentType: 'registration',
        status: DocumentStatus.COMPLETED,
        psychologicalImpact: 'low',
        dueDate: '2024-02-20T17:00:00Z',
        submittedDate: '2024-01-10T09:00:00Z',
        verifiedDate: '2024-01-11T11:30:00Z'
      },
      {
        id: 'req-5',
        name: 'Technical Capability Statement',
        description: 'Detailed statement of technical capabilities and past project experience',
        mandatory: false,
        documentType: 'statement',
        status: DocumentStatus.NOT_STARTED,
        psychologicalImpact: 'medium',
        dueDate: '2024-02-20T17:00:00Z'
      }
    ];

    setComplianceData(mockRequirements);
    
    // Calculate metrics
    const completed = mockRequirements.filter(req => req.status === DocumentStatus.COMPLETED).length;
    const total = mockRequirements.filter(req => req.mandatory).length;
    const score = Math.round((completed / total) * 100);
    
    setCompletedRequirements(completed);
    setOverallScore(score);
    
    // Determine confidence level
    if (score >= 80) setConfidenceLevel('high');
    else if (score >= 50) setConfidenceLevel('medium');
    else setConfidenceLevel('low');

    trackEngagement('compliance_overview_viewed', {
      overallScore: score,
      completedRequirements: completed,
      totalRequirements: total,
      psychologicalState
    });
  };

  const getStatusIcon = (status: DocumentStatus) => {
    switch (status) {
      case DocumentStatus.COMPLETED:
      case DocumentStatus.VERIFIED:
        return <CheckIcon color="success" />;
      case DocumentStatus.IN_PROGRESS:
        return <TimerIcon color="warning" />;
      case DocumentStatus.REJECTED:
        return <ErrorIcon color="error" />;
      default:
        return <WarningIcon color="action" />;
    }
  };

  const getStatusColor = (status: DocumentStatus) => {
    switch (status) {
      case DocumentStatus.COMPLETED:
      case DocumentStatus.VERIFIED:
        return 'success';
      case DocumentStatus.IN_PROGRESS:
        return 'warning';
      case DocumentStatus.REJECTED:
        return 'error';
      default:
        return 'default';
    }
  };

  const getConfidenceMessage = () => {
    switch (confidenceLevel) {
      case 'high':
        return "🎉 Excellent! You're well-prepared for this tender.";
      case 'medium':
        return "👍 Good progress! A few more documents and you'll be ready.";
      case 'low':
        return "💪 Don't worry! We'll guide you through each requirement step by step.";
    }
  };

  const getAnxietyReductionTips = () => [
    "📋 Break down complex requirements into smaller, manageable tasks",
    "⏰ Set realistic deadlines with buffer time for each document",
    "🤝 Our legal experts are available to help clarify any requirements",
    "✅ Focus on completing one requirement at a time",
    "📞 Use our compliance hotline for immediate assistance"
  ];

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header with Confidence Building */}
      <Card 
        elevation={2} 
        sx={{ 
          mb: 3, 
          background: confidenceLevel === 'high' ? 'linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%)' :
                     confidenceLevel === 'medium' ? 'linear-gradient(135deg, #fff3e0 0%, #fef7f0 100%)' :
                     'linear-gradient(135deg, #fce4ec 0%, #f8f0f5 100%)'
        }}
      >
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={8}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <SecurityIcon sx={{ fontSize: 40, mr: 2, color: 'primary.main' }} />
                <Box>
                  <Typography variant={isHighCognitiveLoad ? "h4" : "h5"} sx={{ fontWeight: 'bold' }}>
                    SA Legal Compliance Dashboard
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    {getConfidenceMessage()}
                  </Typography>
                </Box>
              </Box>

              {/* Progress Overview */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  Compliance Progress: {completedRequirements} of {complianceData.filter(r => r.mandatory).length} mandatory requirements
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={overallScore}
                  sx={{ height: 10, borderRadius: 5 }}
                  color={confidenceLevel === 'high' ? 'success' : confidenceLevel === 'medium' ? 'warning' : 'error'}
                />
                <Typography variant="h6" sx={{ mt: 1, fontWeight: 'bold' }}>
                  {overallScore}% Complete
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Stack spacing={2}>
                <Button
                  variant="contained"
                  startIcon={<AIIcon />}
                  fullWidth
                  onClick={() => trackEngagement('ai_compliance_assistant', {})}
                >
                  AI Compliance Assistant
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<SupportIcon />}
                  fullWidth
                  onClick={() => setShowLegalSupport(true)}
                >
                  Legal Support
                </Button>
              </Stack>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Anxiety Reduction Alert */}
      {showAnxietyReduction && (
        <Alert 
          severity="info" 
          icon={<PsychologyIcon />}
          sx={{ mb: 3 }}
          action={
            <Button size="small" onClick={() => setShowAnxietyReduction(false)}>
              Got it
            </Button>
          }
        >
          <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
            💆‍♀️ Feeling overwhelmed by legal requirements?
          </Typography>
          <Typography variant="body2">
            That's completely normal! Legal compliance can feel complex, but we're here to guide you through every step.
          </Typography>
        </Alert>
      )}

      {/* Tender Intelligence - Compliance Gaps */}
      <Card sx={{ mb: 3, border: '2px solid', borderColor: 'error.light', bgcolor: 'error.lighter' }}>
        <CardHeader
          title={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Psychology color="error" />
              <Typography variant="h6" fontWeight="bold">
                🚨 Tender Compliance Intelligence
              </Typography>
            </Box>
          }
          subheader="Compliance gaps blocking tender opportunities"
        />
        <CardContent>
          <Grid container spacing={3}>
            {tenderIntelligence.map((tender) => (
              <Grid item xs={12} md={6} key={tender.id}>
                <Card
                  variant="outlined"
                  sx={{
                    border: '2px solid',
                    borderColor: tender.urgency === 'critical' ? 'error.main' : 'warning.main',
                    bgcolor: tender.urgency === 'critical' ? 'error.lighter' : 'warning.lighter'
                  }}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="h6" fontWeight="bold" sx={{ mb: 0.5 }}>
                          {tender.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          💰 {tender.value >= 1000000 ? `R${(tender.value / 1000000).toFixed(1)}M` : `R${(tender.value / 1000).toFixed(0)}k`}
                        </Typography>
                        <Typography variant="body2" color="error.main" fontWeight="bold">
                          {tender.psychTrigger}
                        </Typography>
                      </Box>
                      <Chip
                        label={tender.urgency.toUpperCase()}
                        color={tender.urgency === 'critical' ? 'error' : 'warning'}
                        size="small"
                        sx={{ fontWeight: 'bold' }}
                      />
                    </Box>

                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      <strong>Blocked Reason:</strong> {tender.blockedReason}
                    </Typography>

                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      <strong>Missing Compliance:</strong>
                    </Typography>
                    <Stack direction="row" spacing={1} flexWrap="wrap" gap={1} sx={{ mb: 2 }}>
                      {tender.complianceGaps.map((gap, index) => (
                        <Chip
                          key={index}
                          label={gap}
                          size="small"
                          color="error"
                          variant="outlined"
                        />
                      ))}
                    </Stack>

                    <Grid container spacing={2} sx={{ mb: 2 }}>
                      <Grid item xs={6}>
                        <Typography variant="caption" color="text.secondary">
                          Estimated Cost
                        </Typography>
                        <Typography variant="body2" fontWeight="bold" color="warning.main">
                          R{tender.estimatedCost.toLocaleString()}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="caption" color="text.secondary">
                          Time to Complete
                        </Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {tender.timeToComplete}
                        </Typography>
                      </Grid>
                    </Grid>

                    <Stack direction="row" spacing={1}>
                      <Button
                        variant="outlined"
                        size="small"
                        startIcon={<Description />}
                        sx={{ flex: 1 }}
                      >
                        View Requirements
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        startIcon={<FlashOn />}
                        color={tender.urgency === 'critical' ? 'error' : 'warning'}
                        sx={{ flex: 1 }}
                      >
                        {tender.urgency === 'critical' ? 'FIX NOW!' : 'Get Compliant'}
                      </Button>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          <Alert severity="error" sx={{ mt: 3 }}>
            <Typography variant="body2" fontWeight="bold">
              💀 COMPLIANCE TERROR: Missing compliance requirements block R38M+ in tender opportunities!
            </Typography>
            <Typography variant="caption">
              Immediate action required to maintain competitive advantage and tender eligibility
            </Typography>
          </Alert>
        </CardContent>
      </Card>

      <Grid container spacing={3}>
        {/* Main Compliance List */}
        <Grid item xs={12} md={8}>
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                <DocumentIcon sx={{ mr: 1 }} />
                Compliance Requirements
              </Typography>

              {complianceData.map((requirement, index) => (
                <Accordion key={requirement.id} sx={{ mb: 1 }}>
                  <AccordionSummary expandIcon={<ExpandIcon />}>
                    <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                      <Box sx={{ mr: 2 }}>
                        {getStatusIcon(requirement.status)}
                      </Box>
                      
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                          {requirement.name}
                          {requirement.mandatory && (
                            <Chip label="Required" size="small" color="error" sx={{ ml: 1 }} />
                          )}
                        </Typography>
                        
                        <Stack direction="row" spacing={1} sx={{ mt: 0.5 }}>
                          <Chip 
                            label={requirement.status.replace('_', ' ').toUpperCase()}
                            size="small"
                            color={getStatusColor(requirement.status)}
                          />
                          
                          {requirement.psychologicalImpact === 'high' && (
                            <Chip 
                              label="Complex"
                              size="small"
                              color="warning"
                              icon={<PsychologyIcon />}
                            />
                          )}
                        </Stack>
                      </Box>
                    </Box>
                  </AccordionSummary>
                  
                  <AccordionDetails>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {requirement.description}
                    </Typography>

                    {/* Psychological Support for High-Impact Requirements */}
                    {requirement.psychologicalImpact === 'high' && (
                      <Alert severity="info" sx={{ mb: 2 }}>
                        <Typography variant="body2">
                          💡 <strong>Helpful Tip:</strong> This requirement can seem complex, but break it down into smaller steps. 
                          Our AI assistant can help you understand exactly what's needed.
                        </Typography>
                      </Alert>
                    )}

                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="caption" color="text.secondary">
                          Due Date: {new Date(requirement.dueDate!).toLocaleDateString()}
                        </Typography>
                      </Grid>
                      
                      {requirement.submittedDate && (
                        <Grid item xs={12} sm={6}>
                          <Typography variant="caption" color="success.main">
                            ✅ Submitted: {new Date(requirement.submittedDate).toLocaleDateString()}
                          </Typography>
                        </Grid>
                      )}
                    </Grid>

                    <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                      {requirement.status === DocumentStatus.NOT_STARTED && (
                        <Button 
                          variant="contained" 
                          size="small"
                          onClick={() => trackEngagement('start_requirement', { requirementId: requirement.id })}
                        >
                          Start This Requirement
                        </Button>
                      )}
                      
                      {requirement.status === DocumentStatus.IN_PROGRESS && (
                        <Button 
                          variant="contained" 
                          size="small"
                          onClick={() => trackEngagement('continue_requirement', { requirementId: requirement.id })}
                        >
                          Continue
                        </Button>
                      )}
                      
                      <Button 
                        variant="outlined" 
                        size="small"
                        startIcon={<TipIcon />}
                        onClick={() => trackEngagement('get_help', { requirementId: requirement.id })}
                      >
                        Get Help
                      </Button>
                    </Box>
                  </AccordionDetails>
                </Accordion>
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* Sidebar with Support */}
        <Grid item xs={12} md={4}>
          {/* Confidence Building */}
          <Card elevation={2} sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                <AchievementIcon sx={{ mr: 1, color: 'warning.main' }} />
                Your Progress
              </Typography>
              
              <Box sx={{ textAlign: 'center', mb: 2 }}>
                <Typography variant="h3" color="primary" sx={{ fontWeight: 'bold' }}>
                  {overallScore}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Compliance Complete
                </Typography>
              </Box>

              <Stack spacing={1}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2">Completed:</Typography>
                  <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                    {completedRequirements} requirements
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2">Confidence Level:</Typography>
                  <Chip 
                    label={confidenceLevel.toUpperCase()}
                    size="small"
                    color={confidenceLevel === 'high' ? 'success' : confidenceLevel === 'medium' ? 'warning' : 'error'}
                  />
                </Box>
              </Stack>
            </CardContent>
          </Card>

          {/* Anxiety Reduction Tips */}
          {(isStressed || showLegalSupport) && (
            <Card elevation={2} sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                  <PsychologyIcon sx={{ mr: 1, color: 'info.main' }} />
                  Stress-Free Compliance
                </Typography>
                
                <List dense>
                  {getAnxietyReductionTips().map((tip, index) => (
                    <ListItem key={index} sx={{ px: 0 }}>
                      <ListItemText 
                        primary={tip}
                        primaryTypographyProps={{ variant: 'body2' }}
                      />
                    </ListItem>
                  ))}
                </List>

                <Button 
                  variant="outlined" 
                  fullWidth 
                  startIcon={<SupportIcon />}
                  sx={{ mt: 2 }}
                  onClick={() => trackEngagement('request_legal_support', {})}
                >
                  Talk to Legal Expert
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Quick Actions */}
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Quick Actions
              </Typography>
              
              <Stack spacing={1}>
                <Button 
                  variant="outlined" 
                  fullWidth
                  startIcon={<DocumentIcon />}
                  onClick={() => trackEngagement('upload_documents', {})}
                >
                  Upload Documents
                </Button>
                
                <Button 
                  variant="outlined" 
                  fullWidth
                  startIcon={<VerifiedIcon />}
                  onClick={() => trackEngagement('verify_status', {})}
                >
                  Check Verification Status
                </Button>
                
                <Button 
                  variant="outlined" 
                  fullWidth
                  startIcon={<LearnIcon />}
                  onClick={() => trackEngagement('compliance_guide', {})}
                >
                  Compliance Guide
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ComplianceOverview;
