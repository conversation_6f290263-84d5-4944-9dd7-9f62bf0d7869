/**
 * WhatsApp Setup Page
 * Initial setup and onboarding for WhatsApp integration
 */

import React from 'react';
import { Box, Typography, Alert } from '@mui/material';

const WhatsAppSetup: React.FC = () => {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main', mb: 1 }}>
        🚀 WhatsApp Setup
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Set up your WhatsApp integration for automated bidding
      </Typography>
      
      <Alert severity="info">
        <Typography variant="subtitle2">Coming Soon</Typography>
        <Typography variant="body2">
          WhatsApp setup wizard is under development.
        </Typography>
      </Alert>
    </Box>
  );
};

export default WhatsAppSetup;
