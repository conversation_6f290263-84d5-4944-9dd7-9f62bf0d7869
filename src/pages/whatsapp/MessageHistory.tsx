/**
 * Message History Page
 * View and manage WhatsApp message history
 */

import React from 'react';
import { Box, Typography, Alert } from '@mui/material';

const MessageHistory: React.FC = () => {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main', mb: 1 }}>
        📨 Message History
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        View and manage your WhatsApp message history and processing results
      </Typography>
      
      <Alert severity="info">
        <Typography variant="subtitle2">Coming Soon</Typography>
        <Typography variant="body2">
          Message history interface is under development.
        </Typography>
      </Alert>
    </Box>
  );
};

export default MessageHistory;
