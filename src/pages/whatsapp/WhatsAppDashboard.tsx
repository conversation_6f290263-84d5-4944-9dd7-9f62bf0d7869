/**
 * WhatsApp Dashboard Page
 * Main dashboard for WhatsApp auto-bidding features
 */

import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
  Alert,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Divider,
  Avatar,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  WhatsApp,
  CheckCircle,
  Warning,
  Error,
  AutoAwesome,
  Message,
  TrendingUp,
  Speed,
  Settings,
  Refresh,
  PlayArrow,
  Pause,
  Visibility,
  MoreVert
} from '@mui/icons-material';
import {
  useGetWhatsAppStatusQuery,
  useGetWhatsAppMessagesQuery,
  useGetAutoBidActivitiesQuery,
  useToggleAutoBidMutation
} from '../../services/api/whatsapp.api';

const WhatsAppDashboard: React.FC = () => {
  const [userId] = useState('demo'); // Get from auth context

  // API Queries
  const { 
    data: status, 
    isLoading: statusLoading,
    error: statusError,
    refetch: refetchStatus 
  } = useGetWhatsAppStatusQuery({ userId });

  const { 
    data: messagesData, 
    isLoading: messagesLoading 
  } = useGetWhatsAppMessagesQuery({ 
    userId, 
    page: 1, 
    limit: 5 
  });

  const { 
    data: activitiesData, 
    isLoading: activitiesLoading 
  } = useGetAutoBidActivitiesQuery({ 
    userId, 
    page: 1, 
    limit: 5 
  });

  const [toggleAutoBid] = useToggleAutoBidMutation();

  const handleToggleAutoBid = async () => {
    if (!status) return;
    
    try {
      await toggleAutoBid({
        userId,
        enabled: !status.auto_bid_enabled
      }).unwrap();
    } catch (error) {
      console.error('Failed to toggle auto-bid:', error);
    }
  };

  const getConnectionIcon = () => {
    if (!status) return <Warning color="warning" />;
    
    if (!status.is_connected) return <Error color="error" />;
    if (status.connection_quality === 'excellent') return <CheckCircle color="success" />;
    if (status.connection_quality === 'good') return <CheckCircle color="info" />;
    return <Warning color="warning" />;
  };

  const getConnectionColor = () => {
    if (!status?.is_connected) return 'error';
    if (status.connection_quality === 'excellent') return 'success';
    if (status.connection_quality === 'good') return 'info';
    return 'warning';
  };

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'bid_notification': return '📋';
      case 'tender_alert': return '🚨';
      case 'deadline_reminder': return '⏰';
      case 'document_available': return '📄';
      default: return '💬';
    }
  };

  const getActivityStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'failed': return 'error';
      case 'cancelled': return 'warning';
      default: return 'info';
    }
  };

  if (statusError) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load WhatsApp dashboard. Please try again later.
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
            📱 WhatsApp Auto-Bidding
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Automated bidding intelligence through WhatsApp integration
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<Settings />}
            href="/whatsapp/settings"
          >
            Settings
          </Button>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={() => refetchStatus()}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {/* Loading State */}
      {statusLoading && (
        <Box sx={{ mb: 3 }}>
          <LinearProgress />
          <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
            Loading WhatsApp status...
          </Typography>
        </Box>
      )}

      {status && (
        <>
          {/* Status Overview */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    {getConnectionIcon()}
                    <Typography variant="h6">Connection</Typography>
                  </Box>
                  <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
                    {status.is_connected ? 'Connected' : 'Disconnected'}
                  </Typography>
                  <Chip 
                    label={status.connection_quality.toUpperCase()}
                    color={getConnectionColor() as any}
                    size="small"
                  />
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <Message color="primary" />
                    <Typography variant="h6">Messages</Typography>
                  </Box>
                  <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
                    {status.messages_processed}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Processed today
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <AutoAwesome color="warning" />
                    <Typography variant="h6">Auto-Bids</Typography>
                  </Box>
                  <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
                    {status.auto_bids_triggered}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Triggered today
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <Speed color="info" />
                    <Typography variant="h6">Pending</Typography>
                  </Box>
                  <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
                    {status.pending_messages}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Messages in queue
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Auto-Bid Control */}
          <Paper sx={{ mb: 3 }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">🤖 Auto-Bid Control</Typography>
            </Box>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Box>
                  <Typography variant="h6" sx={{ mb: 1 }}>
                    Auto-Bidding Status
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {status.auto_bid_enabled 
                      ? 'Automatically processing bid opportunities from WhatsApp messages'
                      : 'Auto-bidding is currently disabled'
                    }
                  </Typography>
                </Box>
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={status.auto_bid_enabled}
                      onChange={handleToggleAutoBid}
                      color="primary"
                      size="large"
                    />
                  }
                  label=""
                />
              </Box>

              {status.auto_bid_enabled && (
                <Alert severity="success" sx={{ mt: 2 }}>
                  <Typography variant="subtitle2">✅ Auto-Bidding Active</Typography>
                  <Typography variant="body2">
                    WhatsApp messages are being monitored for bid opportunities. 
                    Last activity: {status.last_activity}
                  </Typography>
                </Alert>
              )}

              {!status.auto_bid_enabled && (
                <Alert severity="warning" sx={{ mt: 2 }}>
                  <Typography variant="subtitle2">⚠️ Auto-Bidding Disabled</Typography>
                  <Typography variant="body2">
                    Enable auto-bidding to automatically process bid opportunities from WhatsApp messages.
                  </Typography>
                </Alert>
              )}
            </CardContent>
          </Paper>

          {/* Recent Messages */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Paper>
                <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
                  <Typography variant="h6">📨 Recent Messages</Typography>
                </Box>
                {messagesLoading ? (
                  <Box sx={{ p: 2 }}>
                    <LinearProgress />
                    <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
                      Loading messages...
                    </Typography>
                  </Box>
                ) : messagesData && messagesData.messages.length > 0 ? (
                  <List>
                    {messagesData.messages.map((message, index) => (
                      <React.Fragment key={message.id}>
                        <ListItem>
                          <ListItemIcon>
                            <Typography variant="h6">
                              {getMessageTypeIcon(message.message_type)}
                            </Typography>
                          </ListItemIcon>
                          <ListItemText
                            primary={message.extracted_data.tender_title || 'Message'}
                            secondary={
                              <Box>
                                <Typography variant="caption" display="block">
                                  From: {message.from_number}
                                </Typography>
                                <Typography variant="caption" display="block">
                                  {new Date(message.received_at).toLocaleString()}
                                </Typography>
                                <Chip 
                                  label={message.processing_status}
                                  size="small"
                                  color={
                                    message.processing_status === 'processed' ? 'success' :
                                    message.processing_status === 'failed' ? 'error' : 'info'
                                  }
                                  sx={{ mt: 0.5 }}
                                />
                              </Box>
                            }
                          />
                          <ListItemSecondaryAction>
                            <IconButton edge="end" size="small">
                              <Visibility />
                            </IconButton>
                          </ListItemSecondaryAction>
                        </ListItem>
                        {index < messagesData.messages.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                ) : (
                  <Box sx={{ p: 3, textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      No recent messages
                    </Typography>
                  </Box>
                )}
              </Paper>
            </Grid>

            {/* Recent Auto-Bid Activities */}
            <Grid item xs={12} md={6}>
              <Paper>
                <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
                  <Typography variant="h6">🤖 Auto-Bid Activities</Typography>
                </Box>
                {activitiesLoading ? (
                  <Box sx={{ p: 2 }}>
                    <LinearProgress />
                    <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
                      Loading activities...
                    </Typography>
                  </Box>
                ) : activitiesData && activitiesData.activities.length > 0 ? (
                  <List>
                    {activitiesData.activities.map((activity, index) => (
                      <React.Fragment key={activity.id}>
                        <ListItem>
                          <ListItemIcon>
                            <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
                              <AutoAwesome fontSize="small" />
                            </Avatar>
                          </ListItemIcon>
                          <ListItemText
                            primary={`Auto-bid ${activity.auto_bid_status}`}
                            secondary={
                              <Box>
                                <Typography variant="caption" display="block">
                                  Source: {activity.trigger_source}
                                </Typography>
                                <Typography variant="caption" display="block">
                                  {new Date(activity.started_at).toLocaleString()}
                                </Typography>
                                <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                                  <Chip 
                                    label={activity.auto_bid_status}
                                    size="small"
                                    color={getActivityStatusColor(activity.auto_bid_status) as any}
                                  />
                                  {activity.jobs_created > 0 && (
                                    <Chip 
                                      label={`${activity.jobs_created} jobs`}
                                      size="small"
                                      color="success"
                                      variant="outlined"
                                    />
                                  )}
                                </Box>
                              </Box>
                            }
                          />
                          <ListItemSecondaryAction>
                            <IconButton edge="end" size="small">
                              <MoreVert />
                            </IconButton>
                          </ListItemSecondaryAction>
                        </ListItem>
                        {index < activitiesData.activities.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                ) : (
                  <Box sx={{ p: 3, textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      No recent auto-bid activities
                    </Typography>
                  </Box>
                )}
              </Paper>
            </Grid>
          </Grid>
        </>
      )}

      {/* No Data State */}
      {!statusLoading && !status && (
        <Alert severity="info" sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" sx={{ mb: 1 }}>
            WhatsApp Integration Not Configured
          </Typography>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Set up WhatsApp auto-bidding to start receiving and processing bid opportunities automatically.
          </Typography>
          <Button variant="contained" href="/whatsapp/setup">
            Set Up WhatsApp Integration
          </Button>
        </Alert>
      )}
    </Box>
  );
};

export default WhatsAppDashboard;
