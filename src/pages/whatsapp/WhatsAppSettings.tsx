/**
 * WhatsApp Settings Page
 * Configuration and preferences for WhatsApp auto-bidding
 */

import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  Paper,
  Divider,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton
} from '@mui/material';
import {
  Save,
  Science as Test,
  Delete,
  Add,
  WhatsApp,
  Security,
  Notifications,
  Schedule,
  MonetizationOn
} from '@mui/icons-material';
import {
  useGetAutoBidSettingsQuery,
  useUpdateAutoBidSettingsMutation,
  useVerifyWhatsAppNumberMutation,
  useTestAutoBidMutation
} from '../../services/api/whatsapp.api';

const WhatsAppSettings: React.FC = () => {
  const [userId] = useState('demo'); // Get from auth context

  const { 
    data: settings, 
    isLoading,
    error 
  } = useGetAutoBidSettingsQuery({ userId });

  const [updateSettings] = useUpdateAutoBidSettingsMutation();
  const [verifyNumber] = useVerifyWhatsAppNumberMutation();
  const [testAutoBid] = useTestAutoBidMutation();

  const [formData, setFormData] = useState({
    whatsapp_number: settings?.whatsapp_number || '',
    auto_bid_preference: settings?.auto_bid_preference || 'ask_first',
    max_bid_value: settings?.max_bid_value || 1000000,
    minimum_confidence_score: settings?.minimum_confidence_score || 0.8,
    require_feasibility_check: settings?.require_feasibility_check || true,
    notify_on_auto_bid: settings?.notify_on_auto_bid || true,
    max_daily_auto_bids: settings?.max_daily_auto_bids || 5,
    max_monthly_auto_bids: settings?.max_monthly_auto_bids || 50,
    working_hours_only: settings?.working_hours_only || true,
    working_hours_start: settings?.working_hours_start || '08:00',
    working_hours_end: settings?.working_hours_end || '17:00',
    weekend_auto_bid: settings?.weekend_auto_bid || false,
    daily_spend_limit: settings?.daily_spend_limit || 100000,
    monthly_spend_limit: settings?.monthly_spend_limit || 1000000,
    require_approval_above: settings?.require_approval_above || 500000
  });

  const [isSaving, setIsSaving] = useState(false);
  const [testMessage, setTestMessage] = useState('New tender available: Municipal IT Services - Closing: 2024-12-20 - Value: R2,500,000');

  React.useEffect(() => {
    if (settings) {
      setFormData({
        whatsapp_number: settings.whatsapp_number || '',
        auto_bid_preference: settings.auto_bid_preference || 'ask_first',
        max_bid_value: settings.max_bid_value || 1000000,
        minimum_confidence_score: settings.minimum_confidence_score || 0.8,
        require_feasibility_check: settings.require_feasibility_check || true,
        notify_on_auto_bid: settings.notify_on_auto_bid || true,
        max_daily_auto_bids: settings.max_daily_auto_bids || 5,
        max_monthly_auto_bids: settings.max_monthly_auto_bids || 50,
        working_hours_only: settings.working_hours_only || true,
        working_hours_start: settings.working_hours_start || '08:00',
        working_hours_end: settings.working_hours_end || '17:00',
        weekend_auto_bid: settings.weekend_auto_bid || false,
        daily_spend_limit: settings.daily_spend_limit || 100000,
        monthly_spend_limit: settings.monthly_spend_limit || 1000000,
        require_approval_above: settings.require_approval_above || 500000
      });
    }
  }, [settings]);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await updateSettings({
        ...formData,
        user_id: userId,
        is_active: true,
        preferred_categories: formData.preferred_categories || [],
        excluded_categories: formData.excluded_categories || [],
        preferred_provinces: formData.preferred_provinces || [],
        excluded_provinces: formData.excluded_provinces || [],
        working_hours_start: formData.working_hours_start || '09:00',
        working_hours_end: formData.working_hours_end || '17:00',
        timezone: formData.timezone || 'Africa/Johannesburg'
      }).unwrap();
      
      // Show success message
      console.log('Settings saved successfully');
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleVerifyNumber = async () => {
    try {
      await verifyNumber({
        userId,
        phoneNumber: formData.whatsapp_number
      }).unwrap();
      
      console.log('Number verification initiated');
    } catch (error) {
      console.error('Failed to verify number:', error);
    }
  };

  const handleTestAutoBid = async () => {
    try {
      const result = await testAutoBid({
        userId,
        testMessage
      }).unwrap();
      
      console.log('Test result:', result);
    } catch (error) {
      console.error('Test failed:', error);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load WhatsApp settings. Please try again later.
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main', mb: 1 }}>
          ⚙️ WhatsApp Settings
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Configure your WhatsApp auto-bidding preferences and security settings
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Main Settings */}
        <Grid item xs={12} md={8}>
          {/* Connection Settings */}
          <Paper sx={{ mb: 3 }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">📱 Connection Settings</Typography>
            </Box>
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={8}>
                  <TextField
                    fullWidth
                    label="WhatsApp Number"
                    value={formData.whatsapp_number}
                    onChange={(e) => setFormData({ ...formData, whatsapp_number: e.target.value })}
                    placeholder="+27 XX XXX XXXX"
                    helperText="Enter your WhatsApp number with country code"
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<WhatsApp />}
                    onClick={handleVerifyNumber}
                    sx={{ height: '56px' }}
                  >
                    Verify Number
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Paper>

          {/* Auto-Bid Preferences */}
          <Paper sx={{ mb: 3 }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">🤖 Auto-Bid Preferences</Typography>
            </Box>
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Auto-Bid Preference</InputLabel>
                    <Select
                      value={formData.auto_bid_preference}
                      label="Auto-Bid Preference"
                      onChange={(e) => setFormData({ ...formData, auto_bid_preference: e.target.value as any })}
                    >
                      <MenuItem value="always">Always Auto-Bid</MenuItem>
                      <MenuItem value="ask_first">Ask First</MenuItem>
                      <MenuItem value="conditions_based">Conditions Based</MenuItem>
                      <MenuItem value="never">Never Auto-Bid</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Maximum Bid Value"
                    value={formData.max_bid_value}
                    onChange={(e) => setFormData({ ...formData, max_bid_value: Number(e.target.value) })}
                    helperText={`Current: ${formatCurrency(formData.max_bid_value)}`}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Minimum Confidence Score"
                    value={formData.minimum_confidence_score}
                    onChange={(e) => setFormData({ ...formData, minimum_confidence_score: Number(e.target.value) })}
                    inputProps={{ min: 0, max: 1, step: 0.1 }}
                    helperText="0.0 to 1.0 (higher = more selective)"
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Box>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={formData.require_feasibility_check}
                          onChange={(e) => setFormData({ ...formData, require_feasibility_check: e.target.checked as true })}
                        />
                      }
                      label="Require Feasibility Check"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={formData.notify_on_auto_bid}
                          onChange={(e) => setFormData({ ...formData, notify_on_auto_bid: e.target.checked as true })}
                        />
                      }
                      label="Notify on Auto-Bid"
                    />
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Paper>

          {/* Limits & Controls */}
          <Paper sx={{ mb: 3 }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">🛡️ Limits & Controls</Typography>
            </Box>
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Max Daily Auto-Bids"
                    value={formData.max_daily_auto_bids}
                    onChange={(e) => setFormData({ ...formData, max_daily_auto_bids: Number(e.target.value) })}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Max Monthly Auto-Bids"
                    value={formData.max_monthly_auto_bids}
                    onChange={(e) => setFormData({ ...formData, max_monthly_auto_bids: Number(e.target.value) })}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Daily Spend Limit"
                    value={formData.daily_spend_limit}
                    onChange={(e) => setFormData({ ...formData, daily_spend_limit: Number(e.target.value) })}
                    helperText={`Current: ${formatCurrency(formData.daily_spend_limit || 0)}`}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Monthly Spend Limit"
                    value={formData.monthly_spend_limit}
                    onChange={(e) => setFormData({ ...formData, monthly_spend_limit: Number(e.target.value) })}
                    helperText={`Current: ${formatCurrency(formData.monthly_spend_limit || 0)}`}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Require Approval Above"
                    value={formData.require_approval_above}
                    onChange={(e) => setFormData({ ...formData, require_approval_above: Number(e.target.value) })}
                    helperText={`Bids above ${formatCurrency(formData.require_approval_above || 0)} will require manual approval`}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Paper>

          {/* Schedule Settings */}
          <Paper sx={{ mb: 3 }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">⏰ Schedule Settings</Typography>
            </Box>
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.working_hours_only}
                        onChange={(e) => setFormData({ ...formData, working_hours_only: e.target.checked as true })}
                      />
                    }
                    label="Auto-bid during working hours only"
                  />
                </Grid>

                {formData.working_hours_only && (
                  <>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        type="time"
                        label="Working Hours Start"
                        value={formData.working_hours_start}
                        onChange={(e) => setFormData({ ...formData, working_hours_start: e.target.value })}
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        type="time"
                        label="Working Hours End"
                        value={formData.working_hours_end}
                        onChange={(e) => setFormData({ ...formData, working_hours_end: e.target.value })}
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>
                  </>
                )}

                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.weekend_auto_bid}
                        onChange={(e) => setFormData({ ...formData, weekend_auto_bid: e.target.checked })}
                      />
                    }
                    label="Enable auto-bidding on weekends"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Paper>

          {/* Save Button */}
          <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
            <Button
              variant="contained"
              startIcon={<Save />}
              onClick={handleSave}
              disabled={isSaving}
              size="large"
            >
              {isSaving ? 'Saving...' : 'Save Settings'}
            </Button>

            <Button
              variant="outlined"
              startIcon={<Test />}
              onClick={handleTestAutoBid}
            >
              Test Auto-Bid
            </Button>
          </Box>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          {/* Test Auto-Bid */}
          <Paper sx={{ mb: 3 }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">🧪 Test Auto-Bid</Typography>
            </Box>
            <CardContent>
              <TextField
                fullWidth
                multiline
                rows={4}
                label="Test Message"
                value={testMessage}
                onChange={(e) => setTestMessage(e.target.value)}
                sx={{ mb: 2 }}
              />
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Test />}
                onClick={handleTestAutoBid}
              >
                Test Processing
              </Button>
            </CardContent>
          </Paper>

          {/* Current Status */}
          <Paper>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">📊 Current Status</Typography>
            </Box>
            <CardContent>
              <List dense>
                <ListItem>
                  <ListItemText primary="Auto-Bid Status" />
                  <ListItemSecondaryAction>
                    <Chip label="Active" color="success" size="small" />
                  </ListItemSecondaryAction>
                </ListItem>
                <ListItem>
                  <ListItemText primary="Daily Bids Used" />
                  <ListItemSecondaryAction>
                    <Typography variant="body2">2 / {formData.max_daily_auto_bids}</Typography>
                  </ListItemSecondaryAction>
                </ListItem>
                <ListItem>
                  <ListItemText primary="Monthly Bids Used" />
                  <ListItemSecondaryAction>
                    <Typography variant="body2">15 / {formData.max_monthly_auto_bids}</Typography>
                  </ListItemSecondaryAction>
                </ListItem>
                <ListItem>
                  <ListItemText primary="Daily Spend" />
                  <ListItemSecondaryAction>
                    <Typography variant="body2">{formatCurrency(25000)}</Typography>
                  </ListItemSecondaryAction>
                </ListItem>
              </List>
            </CardContent>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default WhatsAppSettings;
