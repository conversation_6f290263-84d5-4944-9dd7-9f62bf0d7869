import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  CardHeader,
  Ty<PERSON>graphy,
  Button,
  Grid,
  LinearProgress,
  Chip,
  Stack,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Avatar,
  Divider
} from '@mui/material';
import {
  School,
  Warning,
  CheckCircle,
  Timer,
  TrendingUp,
  FlashOn,
  Psychology,
  EmojiEvents,
  Speed,
  Refresh,
  Add,
  Visibility
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

interface SkillGap {
  id: string;
  skillName: string;
  category: string;
  urgency: 'critical' | 'high' | 'medium' | 'low';
  tendersBlocked: number;
  potentialValue: number;
  certificationRequired: boolean;
  timeToComplete: string;
  costEstimate: number;
  availableProviders: number;
  successRate: number;
}

interface SkillCertification {
  id: string;
  name: string;
  provider: string;
  status: 'enrolled' | 'in_progress' | 'completed' | 'expired';
  progress: number;
  deadline: string;
  cost: number;
  validityPeriod: string;
  tenderRelevance: string[];
}

const SkillSyncDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [skillGaps, setSkillGaps] = useState<SkillGap[]>([
    {
      id: 'gap-1',
      skillName: 'Project Management Professional (PMP)',
      category: 'Management',
      urgency: 'critical',
      tendersBlocked: 12,
      potentialValue: 45000000,
      certificationRequired: true,
      timeToComplete: '6 weeks',
      costEstimate: 18000,
      availableProviders: 3,
      successRate: 89
    },
    {
      id: 'gap-2',
      skillName: 'ISO 9001:2015 Lead Auditor',
      category: 'Quality Management',
      urgency: 'critical',
      tendersBlocked: 15,
      potentialValue: 67000000,
      certificationRequired: true,
      timeToComplete: '4 weeks',
      costEstimate: 42000,
      availableProviders: 1,
      successRate: 92
    },
    {
      id: 'gap-3',
      skillName: 'Advanced Excel & Data Analysis',
      category: 'Technical',
      urgency: 'high',
      tendersBlocked: 8,
      potentialValue: 22000000,
      certificationRequired: false,
      timeToComplete: '2 weeks',
      costEstimate: 8500,
      availableProviders: 5,
      successRate: 78
    }
  ]);

  const [certifications, setCertifications] = useState<SkillCertification[]>([
    {
      id: 'cert-1',
      name: 'Construction Project Management',
      provider: 'SkillSync Elite',
      status: 'in_progress',
      progress: 65,
      deadline: '2024-03-15',
      cost: 15000,
      validityPeriod: '3 years',
      tenderRelevance: ['Construction', 'Infrastructure', 'Engineering']
    },
    {
      id: 'cert-2',
      name: 'Health & Safety Level 5',
      provider: 'SkillSync Pro',
      status: 'completed',
      progress: 100,
      deadline: '2024-01-30',
      cost: 12000,
      validityPeriod: '2 years',
      tenderRelevance: ['Construction', 'Mining', 'Manufacturing']
    }
  ]);

  const [dashboardMetrics, setDashboardMetrics] = useState({
    totalSkillGaps: 3,
    criticalGaps: 2,
    tendersBlocked: 35,
    potentialValueBlocked: 134000000,
    certificationsInProgress: 1,
    completedCertifications: 1,
    complianceScore: 67,
    industryRanking: 'Top 25%'
  });

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadSkillData();
  }, []);

  const loadSkillData = async () => {
    try {
      // In real implementation, fetch from API
      console.log('Loading skill data...');
    } catch (error) {
      console.error('Failed to load skill data:', error);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadSkillData();
    setTimeout(() => setRefreshing(false), 1000);
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return '#f44336';
      case 'high': return '#ff9800';
      case 'medium': return '#2196f3';
      case 'low': return '#4caf50';
      default: return '#757575';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'in_progress': return 'warning';
      case 'enrolled': return 'info';
      case 'expired': return 'error';
      default: return 'default';
    }
  };

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `R${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `R${(value / 1000).toFixed(0)}k`;
    return `R${value}`;
  };

  const handleBookSkill = (skillId: string) => {
    navigate(`/skillsync/book/${skillId}`);
  };

  const handleViewCertification = (certId: string) => {
    navigate(`/skillsync/certification/${certId}`);
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
            🎓 SkillSync Dashboard
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Professional development and compliance management
          </Typography>
        </Box>
        
        <Stack direction="row" spacing={2}>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => navigate('/skillsync/browse')}
            sx={{
              background: 'linear-gradient(45deg, #2196F3, #21CBF3)',
              fontSize: '1.1rem',
              fontWeight: 'bold'
            }}
          >
            🚀 Browse Skills
          </Button>
          <IconButton onClick={handleRefresh} disabled={refreshing}>
            <Refresh />
          </IconButton>
        </Stack>
      </Box>

      {/* Critical Alert */}
      {dashboardMetrics.criticalGaps > 0 && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="body2" fontWeight="bold">
            🚨 CRITICAL SKILL GAPS: {dashboardMetrics.criticalGaps} gaps blocking {formatCurrency(dashboardMetrics.potentialValueBlocked)} in tenders!
          </Typography>
          <Typography variant="caption">
            Immediate action required to maintain competitive advantage
          </Typography>
        </Alert>
      )}

      {/* Dashboard Metrics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="error.main" fontWeight="bold">
                {dashboardMetrics.totalSkillGaps}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Skill Gaps
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main" fontWeight="bold">
                {dashboardMetrics.tendersBlocked}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tenders Blocked
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary.main" fontWeight="bold">
                {dashboardMetrics.complianceScore}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Compliance Score
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="success.main" fontWeight="bold">
                {dashboardMetrics.industryRanking}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Industry Ranking
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Critical Skill Gaps */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardHeader 
              title="🚨 Critical Skill Gaps"
              action={
                <Chip 
                  label={`${dashboardMetrics.criticalGaps} Critical`}
                  color="error"
                  size="small"
                />
              }
            />
            <CardContent>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Skill Required</TableCell>
                      <TableCell>Impact</TableCell>
                      <TableCell>Timeline</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {skillGaps.map((gap) => (
                      <TableRow key={gap.id} hover>
                        <TableCell>
                          <Box>
                            <Typography variant="body1" fontWeight="bold">
                              {gap.skillName}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {gap.category}
                            </Typography>
                            <Chip 
                              label={gap.urgency.toUpperCase()}
                              size="small"
                              sx={{ 
                                mt: 0.5,
                                backgroundColor: getUrgencyColor(gap.urgency),
                                color: 'white',
                                fontWeight: 'bold'
                              }}
                            />
                          </Box>
                        </TableCell>
                        
                        <TableCell>
                          <Typography variant="body2" fontWeight="bold" color="error.main">
                            {gap.tendersBlocked} tenders blocked
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {formatCurrency(gap.potentialValue)} at risk
                          </Typography>
                          <Typography variant="caption" color="success.main">
                            {gap.successRate}% success rate
                          </Typography>
                        </TableCell>
                        
                        <TableCell>
                          <Typography variant="body2" fontWeight="bold">
                            {gap.timeToComplete}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {formatCurrency(gap.costEstimate)}
                          </Typography>
                          <Typography variant="caption">
                            {gap.availableProviders} providers
                          </Typography>
                        </TableCell>
                        
                        <TableCell>
                          <Stack direction="row" spacing={1}>
                            <Button
                              variant="contained"
                              size="small"
                              startIcon={<FlashOn />}
                              onClick={() => handleBookSkill(gap.id)}
                              sx={{
                                backgroundColor: getUrgencyColor(gap.urgency),
                                '&:hover': {
                                  backgroundColor: getUrgencyColor(gap.urgency),
                                  opacity: 0.8
                                }
                              }}
                            >
                              {gap.urgency === 'critical' ? 'URGENT' : 'BOOK'}
                            </Button>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Current Certifications */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader title="📜 Current Certifications" />
            <CardContent>
              <Stack spacing={2}>
                {certifications.map((cert) => (
                  <Card key={cert.id} variant="outlined">
                    <CardContent sx={{ p: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                        <Typography variant="body1" fontWeight="bold">
                          {cert.name}
                        </Typography>
                        <Chip 
                          label={cert.status.replace('_', ' ').toUpperCase()}
                          size="small"
                          color={getStatusColor(cert.status) as any}
                        />
                      </Box>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        {cert.provider}
                      </Typography>
                      
                      {cert.status === 'in_progress' && (
                        <Box sx={{ mb: 1 }}>
                          <LinearProgress 
                            variant="determinate" 
                            value={cert.progress}
                            sx={{ height: 6, borderRadius: 3 }}
                          />
                          <Typography variant="caption" color="text.secondary">
                            {cert.progress}% complete
                          </Typography>
                        </Box>
                      )}
                      
                      <Typography variant="caption" color="text.secondary">
                        Valid: {cert.validityPeriod} • Cost: {formatCurrency(cert.cost)}
                      </Typography>
                      
                      <Button
                        variant="outlined"
                        size="small"
                        fullWidth
                        startIcon={<Visibility />}
                        onClick={() => handleViewCertification(cert.id)}
                        sx={{ mt: 1 }}
                      >
                        View Details
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Success Alert */}
      <Alert severity="success" sx={{ mt: 3 }}>
        <Typography variant="body2" fontWeight="bold">
          🎯 SKILLSYNC SUCCESS: Users with complete skill profiles win 67% more tenders!
        </Typography>
        <Typography variant="caption">
          Professional development directly correlates with bidding success
        </Typography>
      </Alert>
    </Box>
  );
};

export default SkillSyncDashboard;
