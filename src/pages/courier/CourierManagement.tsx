/**
 * Courier Management Interface
 * Comprehensive delivery management system integrated with Queen Bee operations
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Alert,
  LinearProgress,
  Divider,
  Stack,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  LocalShipping as CourierIcon,
  Flight as AirIcon,
  DirectionsCar as BeeIcon,
  LocationOn as LocationIcon,
  Schedule as TimeIcon,
  AttachMoney as CostIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Visibility as ViewIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  Map as MapIcon
} from '@mui/icons-material';
import CourierDispatchEngine, {
  CourierRequest,
  DeliveryMode,
  DeliveryPriority,
  CourierRequestStatus,
  DeliveryAddress
} from '../../services/CourierDispatchEngine';

const CourierManagement: React.FC = () => {
  const [activeRequests, setActiveRequests] = useState<CourierRequest[]>([]);
  const [selectedRequest, setSelectedRequest] = useState<CourierRequest | null>(null);
  const [createRequestDialog, setCreateRequestDialog] = useState(false);
  const [trackingDialog, setTrackingDialog] = useState(false);
  const [newRequest, setNewRequest] = useState<Partial<CourierRequest>>({});

  const courierEngine = CourierDispatchEngine.getInstance();

  useEffect(() => {
    loadActiveRequests();
    
    // Refresh every 30 seconds
    const interval = setInterval(loadActiveRequests, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadActiveRequests = () => {
    const requests = courierEngine.getAllActiveRequests();
    setActiveRequests(requests);
  };

  const handleCreateRequest = async () => {
    if (!newRequest.pickupAddress || !newRequest.deliveryAddress || !newRequest.deadline) {
      return;
    }

    try {
      const request = await courierEngine.createDeliveryRequest(
        newRequest.tenderId || 'TND-001',
        newRequest.userId || 'user-001',
        newRequest.pickupAddress,
        newRequest.deliveryAddress,
        newRequest.documentType || 'Tender Documents',
        newRequest.deadline,
        DeliveryPriority.SPEED
      );

      setCreateRequestDialog(false);
      setNewRequest({});
      loadActiveRequests();
    } catch (error) {
      console.error('Failed to create delivery request:', error);
    }
  };

  const getDeliveryModeIcon = (mode: DeliveryMode) => {
    switch (mode) {
      case DeliveryMode.BEE_DIRECT: return <BeeIcon />;
      case DeliveryMode.COURIER: return <CourierIcon />;
      case DeliveryMode.BEE_AIR_BEE: return <AirIcon />;
      case DeliveryMode.BEE_AIR_BEE_EXTENDED: return <AirIcon />;
      case DeliveryMode.COURIER_PLUS_BEE: return <CourierIcon />;
      default: return <CourierIcon />;
    }
  };

  const getStatusColor = (status: CourierRequestStatus) => {
    switch (status) {
      case CourierRequestStatus.DELIVERED: return 'success';
      case CourierRequestStatus.IN_TRANSIT: return 'primary';
      case CourierRequestStatus.ASSIGNED: return 'info';
      case CourierRequestStatus.FAILED: return 'error';
      case CourierRequestStatus.CANCELLED: return 'default';
      default: return 'warning';
    }
  };

  const getDeliveryModeDescription = (mode: DeliveryMode) => {
    switch (mode) {
      case DeliveryMode.BEE_DIRECT: return 'Direct bee delivery - Fast and personal';
      case DeliveryMode.COURIER: return 'Standard courier service - Reliable';
      case DeliveryMode.BEE_AIR_BEE: return 'Air delivery with bee pickup/dropoff';
      case DeliveryMode.BEE_AIR_BEE_EXTENDED: return 'Extended air delivery network';
      case DeliveryMode.COURIER_PLUS_BEE: return 'Hybrid courier + bee delivery';
      default: return 'Unknown delivery mode';
    }
  };

  const formatTimeEstimate = (hours: number) => {
    if (hours < 1) return `${Math.round(hours * 60)} minutes`;
    if (hours < 24) return `${hours.toFixed(1)} hours`;
    return `${Math.round(hours / 24)} days`;
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
            📦 Courier Management
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Advanced delivery dispatch with Queen Bee integration
          </Typography>
        </Box>
        
        <Stack direction="row" spacing={2}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setCreateRequestDialog(true)}
          >
            New Delivery
          </Button>
          <IconButton onClick={loadActiveRequests}>
            <RefreshIcon />
          </IconButton>
        </Stack>
      </Box>

      {/* Delivery Statistics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CourierIcon color="primary" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h4">
                    {activeRequests.filter(r => r.status === CourierRequestStatus.IN_TRANSIT).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    In Transit
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CheckIcon color="success" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h4">
                    {activeRequests.filter(r => r.status === CourierRequestStatus.DELIVERED).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Delivered Today
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <BeeIcon color="warning" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h4">
                    {activeRequests.filter(r => r.selectedMode === DeliveryMode.BEE_DIRECT).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Bee Deliveries
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <AirIcon color="info" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h4">
                    {activeRequests.filter(r => 
                      r.selectedMode === DeliveryMode.BEE_AIR_BEE || 
                      r.selectedMode === DeliveryMode.BEE_AIR_BEE_EXTENDED
                    ).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Air Deliveries
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Active Delivery Requests */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <CourierIcon sx={{ mr: 1 }} />
            Active Delivery Requests ({activeRequests.length})
          </Typography>

          {activeRequests.length === 0 ? (
            <Alert severity="info">
              No active delivery requests. Create a new delivery to get started.
            </Alert>
          ) : (
            <List>
              {activeRequests.map((request) => (
                <ListItem key={request.id} divider>
                  <ListItemIcon>
                    {getDeliveryModeIcon(request.selectedMode)}
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                          {request.documentType}
                        </Typography>
                        <Chip
                          label={request.status.replace('_', ' ').toUpperCase()}
                          size="small"
                          color={getStatusColor(request.status)}
                        />
                        <Chip
                          label={request.selectedMode.replace('_', ' ').toUpperCase()}
                          size="small"
                          variant="outlined"
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                          <strong>From:</strong> {request.pickupAddress.city}, {request.pickupAddress.province}
                          {' → '}
                          <strong>To:</strong> {request.deliveryAddress.city}, {request.deliveryAddress.province}
                        </Typography>
                        
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <TimeIcon fontSize="small" />
                            <Typography variant="caption">
                              Deadline: {new Date(request.deadline).toLocaleString()}
                            </Typography>
                          </Box>
                          
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <CostIcon fontSize="small" />
                            <Typography variant="caption">
                              {request.currency} {request.estimatedCost.toFixed(2)}
                            </Typography>
                          </Box>
                        </Box>

                        <Typography variant="caption" color="text.secondary">
                          {getDeliveryModeDescription(request.selectedMode)}
                        </Typography>

                        {request.status === CourierRequestStatus.IN_TRANSIT && request.currentLocation && (
                          <Box sx={{ mt: 1 }}>
                            <Typography variant="caption" color="primary">
                              📍 Current location: {request.currentLocation.address}
                            </Typography>
                          </Box>
                        )}
                      </Box>
                    }
                  />
                  
                  <Stack direction="row" spacing={1}>
                    <Tooltip title="View Details">
                      <IconButton 
                        onClick={() => {
                          setSelectedRequest(request);
                          setTrackingDialog(true);
                        }}
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title="Track on Map">
                      <IconButton>
                        <MapIcon />
                      </IconButton>
                    </Tooltip>
                  </Stack>
                </ListItem>
              ))}
            </List>
          )}
        </CardContent>
      </Card>

      {/* Create Delivery Request Dialog */}
      <Dialog open={createRequestDialog} onClose={() => setCreateRequestDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create New Delivery Request</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" sx={{ mb: 2 }}>Pickup Details</Typography>
              
              <TextField
                fullWidth
                label="Pickup Address"
                value={newRequest.pickupAddress?.street || ''}
                onChange={(e) => setNewRequest(prev => ({
                  ...prev,
                  pickupAddress: {
                    ...prev.pickupAddress,
                    street: e.target.value,
                    city: 'Cape Town',
                    province: 'Western Cape',
                    postalCode: '8001',
                    country: 'South Africa',
                    coordinates: { latitude: -33.9249, longitude: 18.4241 },
                    contactPerson: 'John Doe',
                    contactPhone: '+27 21 123 4567'
                  } as DeliveryAddress
                }))}
                sx={{ mb: 2 }}
              />
              
              <TextField
                fullWidth
                label="Contact Person"
                value={newRequest.pickupAddress?.contactPerson || ''}
                onChange={(e) => setNewRequest(prev => ({
                  ...prev,
                  pickupAddress: {
                    ...prev.pickupAddress,
                    contactPerson: e.target.value
                  } as DeliveryAddress
                }))}
                sx={{ mb: 2 }}
              />
              
              <TextField
                fullWidth
                label="Contact Phone"
                value={newRequest.pickupAddress?.contactPhone || ''}
                onChange={(e) => setNewRequest(prev => ({
                  ...prev,
                  pickupAddress: {
                    ...prev.pickupAddress,
                    contactPhone: e.target.value
                  } as DeliveryAddress
                }))}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="h6" sx={{ mb: 2 }}>Delivery Details</Typography>
              
              <TextField
                fullWidth
                label="Delivery Address"
                value={newRequest.deliveryAddress?.street || ''}
                onChange={(e) => setNewRequest(prev => ({
                  ...prev,
                  deliveryAddress: {
                    ...prev.deliveryAddress,
                    street: e.target.value,
                    city: 'Johannesburg',
                    province: 'Gauteng',
                    postalCode: '2000',
                    country: 'South Africa',
                    coordinates: { latitude: -26.2041, longitude: 28.0473 },
                    contactPerson: 'Jane Smith',
                    contactPhone: '+27 11 987 6543'
                  } as DeliveryAddress
                }))}
                sx={{ mb: 2 }}
              />
              
              <TextField
                fullWidth
                label="Contact Person"
                value={newRequest.deliveryAddress?.contactPerson || ''}
                onChange={(e) => setNewRequest(prev => ({
                  ...prev,
                  deliveryAddress: {
                    ...prev.deliveryAddress,
                    contactPerson: e.target.value
                  } as DeliveryAddress
                }))}
                sx={{ mb: 2 }}
              />
              
              <TextField
                fullWidth
                label="Contact Phone"
                value={newRequest.deliveryAddress?.contactPhone || ''}
                onChange={(e) => setNewRequest(prev => ({
                  ...prev,
                  deliveryAddress: {
                    ...prev.deliveryAddress,
                    contactPhone: e.target.value
                  } as DeliveryAddress
                }))}
              />
            </Grid>

            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Document Type"
                value={newRequest.documentType || ''}
                onChange={(e) => setNewRequest(prev => ({ ...prev, documentType: e.target.value }))}
                placeholder="e.g., Tender Documents, Compliance Certificates"
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Tender ID"
                value={newRequest.tenderId || ''}
                onChange={(e) => setNewRequest(prev => ({ ...prev, tenderId: e.target.value }))}
                placeholder="e.g., TND-001"
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                type="datetime-local"
                label="Deadline"
                value={newRequest.deadline || ''}
                onChange={(e) => setNewRequest(prev => ({ ...prev, deadline: e.target.value }))}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateRequestDialog(false)}>Cancel</Button>
          <Button 
            onClick={handleCreateRequest} 
            variant="contained"
            disabled={!newRequest.pickupAddress || !newRequest.deliveryAddress || !newRequest.deadline}
          >
            Create Delivery Request
          </Button>
        </DialogActions>
      </Dialog>

      {/* Tracking Details Dialog */}
      <Dialog open={trackingDialog} onClose={() => setTrackingDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Delivery Tracking: {selectedRequest?.documentType}
        </DialogTitle>
        <DialogContent>
          {selectedRequest && (
            <Box>
              {/* Request Summary */}
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">From</Typography>
                      <Typography variant="body1">
                        {selectedRequest.pickupAddress.street}<br/>
                        {selectedRequest.pickupAddress.city}, {selectedRequest.pickupAddress.province}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">To</Typography>
                      <Typography variant="body1">
                        {selectedRequest.deliveryAddress.street}<br/>
                        {selectedRequest.deliveryAddress.city}, {selectedRequest.deliveryAddress.province}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Tracking Timeline */}
              <Typography variant="h6" sx={{ mb: 2 }}>Tracking History</Typography>
              <List>
                {selectedRequest.trackingHistory.map((event, index) => (
                  <ListItem key={event.id}>
                    <ListItemIcon>
                      <CheckIcon color={index === 0 ? 'primary' : 'disabled'} />
                    </ListItemIcon>
                    <ListItemText
                      primary={event.event.replace('_', ' ').toUpperCase()}
                      secondary={
                        <Box>
                          <Typography variant="body2">{event.description}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            {new Date(event.timestamp).toLocaleString()}
                          </Typography>
                          {event.location && (
                            <Typography variant="caption" color="primary" sx={{ display: 'block' }}>
                              📍 {event.location.address}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTrackingDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CourierManagement;
