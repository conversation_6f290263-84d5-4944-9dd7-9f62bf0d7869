/**
 * Courier Dashboard
 * Overview dashboard for courier dispatch system with Queen Bee integration
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Stack
} from '@mui/material';
import {
  LocalShipping as CourierIcon,
  Flight as AirIcon,
  DirectionsCar as BeeIcon,
  TrendingUp as TrendingUpIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  People as PeopleIcon,
  Map as MapIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import CourierDispatchEngine, { 
  CourierRequest, 
  DeliveryMode, 
  CourierRequestStatus 
} from '../../services/CourierDispatchEngine';
import QueenBeeManagementSystem from '../../services/QueenBeeManagementSystem';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';

const CourierDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const {
    psychologicalState,
    isStressed,
    needsSimplification,
    trackEngagement
  } = useNeuroMarketing();

  const courierEngine = CourierDispatchEngine.getInstance();
  const queenBeeSystem = QueenBeeManagementSystem.getInstance();

  useEffect(() => {
    loadDashboardData();
    
    // Refresh every 30 seconds
    const interval = setInterval(loadDashboardData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      const activeRequests = courierEngine.getAllActiveRequests();
      
      // Calculate metrics
      const metrics = {
        totalDeliveries: activeRequests.length,
        inTransit: activeRequests.filter(r => r.status === CourierRequestStatus.IN_TRANSIT).length,
        delivered: activeRequests.filter(r => r.status === CourierRequestStatus.DELIVERED).length,
        pending: activeRequests.filter(r => r.status === CourierRequestStatus.CREATED).length,
        beeDeliveries: activeRequests.filter(r => r.selectedMode === DeliveryMode.BEE_DIRECT).length,
        airDeliveries: activeRequests.filter(r => 
          r.selectedMode === DeliveryMode.BEE_AIR_BEE || 
          r.selectedMode === DeliveryMode.BEE_AIR_BEE_EXTENDED
        ).length,
        courierDeliveries: activeRequests.filter(r => r.selectedMode === DeliveryMode.COURIER).length,
        urgentDeliveries: activeRequests.filter(r => r.urgency === 'urgent' || r.urgency === 'critical').length
      };

      setDashboardData({
        activeRequests,
        metrics,
        recentActivity: activeRequests.slice(0, 5)
      });
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleQuickAction = (action: string) => {
    trackEngagement('courier_quick_action', { action, psychologicalState });
    
    switch (action) {
      case 'new_delivery':
        navigate('/courier/management');
        break;
      case 'track_deliveries':
        navigate('/courier/tracking');
        break;
      case 'queen_bee':
        navigate('/courier/queen-bee');
        break;
      case 'analytics':
        navigate('/courier/analytics');
        break;
      default:
        break;
    }
  };

  const getDeliveryModeIcon = (mode: DeliveryMode) => {
    switch (mode) {
      case DeliveryMode.BEE_DIRECT: return <BeeIcon color="warning" />;
      case DeliveryMode.COURIER: return <CourierIcon color="primary" />;
      case DeliveryMode.BEE_AIR_BEE: return <AirIcon color="info" />;
      case DeliveryMode.BEE_AIR_BEE_EXTENDED: return <AirIcon color="secondary" />;
      case DeliveryMode.COURIER_PLUS_BEE: return <CourierIcon color="success" />;
      default: return <CourierIcon />;
    }
  };

  const getStatusColor = (status: CourierRequestStatus) => {
    switch (status) {
      case CourierRequestStatus.DELIVERED: return 'success';
      case CourierRequestStatus.IN_TRANSIT: return 'primary';
      case CourierRequestStatus.ASSIGNED: return 'info';
      case CourierRequestStatus.FAILED: return 'error';
      default: return 'warning';
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" sx={{ mb: 3 }}>Loading Courier Dashboard...</Typography>
        <LinearProgress />
      </Box>
    );
  }

  const { metrics, recentActivity } = dashboardData || {};

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          📦 Courier Command Center
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Multi-modal delivery dispatch with Queen Bee coordination
        </Typography>
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CourierIcon color="primary" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h4">{metrics?.totalDeliveries || 0}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Deliveries
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <ScheduleIcon color="warning" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h4">{metrics?.inTransit || 0}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    In Transit
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CheckIcon color="success" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h4">{metrics?.delivered || 0}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Delivered Today
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <WarningIcon color="error" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h4">{metrics?.urgentDeliveries || 0}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Urgent Deliveries
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Quick Actions
              </Typography>
              
              <Stack spacing={2}>
                <Button
                  fullWidth
                  variant="contained"
                  startIcon={<CourierIcon />}
                  onClick={() => handleQuickAction('new_delivery')}
                  size={needsSimplification ? 'large' : 'medium'}
                >
                  Create New Delivery
                </Button>
                
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<MapIcon />}
                  onClick={() => handleQuickAction('track_deliveries')}
                  size={needsSimplification ? 'large' : 'medium'}
                >
                  Track Deliveries
                </Button>
                
                <Button
                  fullWidth
                  variant="outlined"
                  color="warning"
                  startIcon={<PeopleIcon />}
                  onClick={() => handleQuickAction('queen_bee')}
                  size={needsSimplification ? 'large' : 'medium'}
                >
                  Queen Bee Dashboard
                </Button>
                
                <Button
                  fullWidth
                  variant="outlined"
                  color="info"
                  startIcon={<TrendingUpIcon />}
                  onClick={() => handleQuickAction('analytics')}
                  size={needsSimplification ? 'large' : 'medium'}
                >
                  View Analytics
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Delivery Mode Breakdown */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Delivery Modes
              </Typography>
              
              <List>
                <ListItem>
                  <ListItemIcon>
                    <BeeIcon color="warning" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Bee Direct"
                    secondary={`${metrics?.beeDeliveries || 0} active deliveries`}
                  />
                  <Chip 
                    label={`${metrics?.beeDeliveries || 0}`} 
                    color="warning" 
                    size="small" 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <CourierIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Standard Courier"
                    secondary={`${metrics?.courierDeliveries || 0} active deliveries`}
                  />
                  <Chip 
                    label={`${metrics?.courierDeliveries || 0}`} 
                    color="primary" 
                    size="small" 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <AirIcon color="info" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Air Delivery"
                    secondary={`${metrics?.airDeliveries || 0} active deliveries`}
                  />
                  <Chip 
                    label={`${metrics?.airDeliveries || 0}`} 
                    color="info" 
                    size="small" 
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Recent Delivery Activity
              </Typography>
              
              {recentActivity && recentActivity.length > 0 ? (
                <List>
                  {recentActivity.map((request: CourierRequest, index: number) => (
                    <React.Fragment key={request.id}>
                      <ListItem>
                        <ListItemIcon>
                          {getDeliveryModeIcon(request.selectedMode)}
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="subtitle2">
                                {request.documentType}
                              </Typography>
                              <Chip
                                label={request.status.replace('_', ' ').toUpperCase()}
                                size="small"
                                color={getStatusColor(request.status)}
                              />
                            </Box>
                          }
                          secondary={
                            <Typography variant="body2" color="text.secondary">
                              {request.pickupAddress.city} → {request.deliveryAddress.city} | 
                              {request.selectedMode.replace('_', ' ').toUpperCase()} | 
                              R{request.estimatedCost.toFixed(2)}
                            </Typography>
                          }
                        />
                      </ListItem>
                      {index < recentActivity.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <Alert severity="info">
                  No recent delivery activity. Create your first delivery to get started.
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CourierDashboard;
