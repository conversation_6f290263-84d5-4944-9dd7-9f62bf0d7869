/**
 * Psychological Bid Creation Workflow
 * Reduces stress, manages cognitive load, and increases completion rates
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Card,
  CardContent,
  Typo<PERSON>,
  <PERSON>ton,
  TextField,
  Grid,
  Alert,
  LinearProgress,
  Chip,
  IconButton,
  <PERSON><PERSON><PERSON>,
  <PERSON>ade,
  <PERSON><PERSON><PERSON>,
  <PERSON>ack,
  Divider
} from '@mui/material';
import {
  Psychology as PsychologyIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Lightbulb as TipIcon,
  Timer as TimerIcon,
  Save as SaveIcon,
  AutoAwesome as AIIcon,
  EmojiEvents as AchievementIcon,
  Refresh as RefreshIcon,
  Help as HelpIcon
} from '@mui/icons-material';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';
import BehavioralTenderService from '../../services/BehavioralTenderService';
import { Tender, Bid, BidStatus, Priority } from '../../types/tender.types';

interface CreateBidProps {
  tenderId?: string;
  onComplete?: (bidId: string) => void;
  onCancel?: () => void;
}

const CreateBid: React.FC<CreateBidProps> = ({ tenderId, onComplete, onCancel }) => {
  // Behavioral tracking
  const {
    psychologicalState,
    isHighCognitiveLoad,
    isStressed,
    needsSimplification,
    shouldShowProgress,
    trackEngagement,
    trackDecision,
    trackStress,
    getPersonalizedRecommendations
  } = useNeuroMarketing();

  // State management
  const [activeStep, setActiveStep] = useState(0);
  const [tender, setTender] = useState<Tender | null>(null);
  const [bidData, setBidData] = useState<Partial<Bid>>({
    status: BidStatus.DRAFT,
    priority: Priority.MEDIUM,
    completionPercentage: 0,
    psychologicalJourney: [],
    stressPoints: [],
    motivationFactors: [],
    cognitiveLoadPoints: []
  });
  const [loading, setLoading] = useState(false);
  const [showMotivation, setShowMotivation] = useState(false);
  const [sessionStartTime] = useState(Date.now());
  const [stepStartTime, setStepStartTime] = useState(Date.now());
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);

  // Psychological adaptation
  const [simplifiedMode, setSimplifiedMode] = useState(false);
  const [showBreakSuggestion, setShowBreakSuggestion] = useState(false);
  const [motivationalMessage, setMotivationalMessage] = useState('');

  // Load tender data
  useEffect(() => {
    if (tenderId) {
      loadTenderData(tenderId);
    }
  }, [tenderId]);

  // Monitor psychological state
  useEffect(() => {
    if (psychologicalState) {
      // Adapt interface based on stress level
      if (psychologicalState.stressLevel > 0.7 && !simplifiedMode) {
        setSimplifiedMode(true);
        setShowBreakSuggestion(true);
        trackStress('high_stress_detected', psychologicalState.stressLevel);
      }

      // Show motivation when engagement is low
      if (psychologicalState.engagementLevel < 0.4 && !showMotivation) {
        setShowMotivation(true);
        setMotivationalMessage(getMotivationalMessage());
      }

      // Track cognitive load for this step
      const currentLoad = psychologicalState.cognitiveLoad;
      setBidData(prev => ({
        ...prev,
        cognitiveLoadPoints: [...(prev.cognitiveLoadPoints || []), currentLoad]
      }));
    }
  }, [psychologicalState, simplifiedMode, showMotivation]);

  // Auto-save functionality
  useEffect(() => {
    if (autoSaveEnabled && bidData.id) {
      const timer = setTimeout(() => {
        saveBidDraft();
      }, 30000); // Auto-save every 30 seconds

      return () => clearTimeout(timer);
    }
  }, [bidData, autoSaveEnabled]);

  const loadTenderData = async (id: string) => {
    try {
      setLoading(true);
      // Mock tender data - in real app, load from API
      const mockTender: Tender = {
        id,
        title: 'Smart City IoT Infrastructure Development',
        description: 'Design and implement IoT sensors across the city for traffic management, environmental monitoring, and public safety.',
        organization: 'City of Cape Town',
        organizationType: 'government',
        category: 'it_services' as any,
        value: 15000000,
        currency: 'ZAR',
        location: 'Cape Town, Western Cape',
        province: 'Western Cape',
        publishDate: '2024-01-15T08:00:00Z',
        closingDate: '2024-02-28T17:00:00Z',
        status: 'open' as any,
        priority: Priority.HIGH,
        referenceNumber: 'CCT/IOT/2024/001',
        psychologicalTriggers: [],
        behavioralNudges: [],
        emotionalTone: 'innovative' as any,
        socialProofData: {
          viewCount: 245,
          interestedCount: 18,
          recentViewers: 12,
          competitorCount: 8
        },
        difficultyLevel: 'advanced' as any,
        estimatedEffort: 120,
        potentialXP: 150,
        achievementUnlocks: ['Tech Pioneer', 'Government Partner'],
        bookmarked: false,
        engagementScore: 85,
        complianceRequirements: [],
        technicalRequirements: [],
        financialRequirements: [],
        tenderDocuments: [],
        clarifications: []
      };

      setTender(mockTender);
      
      // Initialize bid data
      setBidData(prev => ({
        ...prev,
        tenderId: id,
        tenderTitle: mockTender.title,
        submissionDeadline: mockTender.closingDate,
        currency: mockTender.currency,
        createdDate: new Date().toISOString(),
        lastModified: new Date().toISOString()
      }));

      trackEngagement('bid_creation_started', {
        tenderId: id,
        tenderValue: mockTender.value,
        psychologicalState
      });

    } catch (error) {
      console.error('Failed to load tender:', error);
    } finally {
      setLoading(false);
    }
  };

  const getMotivationalMessage = (): string => {
    const messages = [
      "🎯 You're doing great! This bid could be a game-changer.",
      "💪 Every expert was once a beginner. Keep going!",
      "🌟 This tender aligns perfectly with your expertise.",
      "🚀 You're closer to success than you think!",
      "⭐ Your unique approach makes you stand out."
    ];
    return messages[Math.floor(Math.random() * messages.length)];
  };

  const handleStepChange = (newStep: number) => {
    // Track time spent on current step
    const timeSpent = Date.now() - stepStartTime;
    trackEngagement('step_completed', {
      step: activeStep,
      timeSpent,
      cognitiveLoad: psychologicalState?.cognitiveLoad || 0
    });

    // Update completion percentage
    const completion = Math.round(((newStep + 1) / steps.length) * 100);
    setBidData(prev => ({
      ...prev,
      completionPercentage: completion,
      lastModified: new Date().toISOString()
    }));

    setActiveStep(newStep);
    setStepStartTime(Date.now());

    // Show achievement for major milestones
    if (completion === 50 || completion === 100) {
      setShowMotivation(true);
      setMotivationalMessage(`🎉 ${completion}% Complete! You're making excellent progress!`);
    }
  };

  const saveBidDraft = async () => {
    try {
      // Save bid draft to backend
      trackEngagement('bid_draft_saved', {
        completionPercentage: bidData.completionPercentage,
        autoSave: true
      });
    } catch (error) {
      console.error('Failed to save draft:', error);
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      
      // Calculate total session time
      const totalTime = Date.now() - sessionStartTime;
      
      // Track completion
      trackEngagement('bid_creation_completed', {
        totalTime,
        stressPoints: bidData.stressPoints?.length || 0,
        averageCognitiveLoad: bidData.cognitiveLoadPoints?.length
          ? bidData.cognitiveLoadPoints.reduce((a, b) => a + b, 0) / bidData.cognitiveLoadPoints.length
          : 0,
        psychologicalJourney: bidData.psychologicalJourney
      });

      // Update bid status
      setBidData(prev => ({
        ...prev,
        status: BidStatus.READY_TO_SUBMIT,
        completionPercentage: 100,
        submittedDate: new Date().toISOString()
      }));

      onComplete?.(bidData.id || 'new-bid');
      
    } catch (error) {
      console.error('Failed to submit bid:', error);
    } finally {
      setLoading(false);
    }
  };

  // Simplified steps for high cognitive load
  const steps = simplifiedMode ? [
    { label: 'Basic Info', optional: false },
    { label: 'Pricing', optional: false },
    { label: 'Submit', optional: false }
  ] : [
    { label: 'Bid Overview', optional: false },
    { label: 'Team & Resources', optional: false },
    { label: 'Technical Approach', optional: true },
    { label: 'Pricing & Timeline', optional: false },
    { label: 'Compliance & Documents', optional: false },
    { label: 'Review & Submit', optional: false }
  ];

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <PsychologyIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Optimizing bid creation for your psychology...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header with Psychological Adaptation */}
      <Card elevation={2} sx={{ mb: 3, background: isStressed ? 'linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%)' : 'default' }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={8}>
              <Typography variant={isHighCognitiveLoad ? "h4" : "h5"} sx={{ fontWeight: 'bold', mb: 1 }}>
                {simplifiedMode ? '✨ Simple Bid Creation' : '🎯 Create Your Winning Bid'}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                {tender?.title || 'Loading tender details...'}
              </Typography>
              
              {shouldShowProgress && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Progress: {bidData.completionPercentage}% complete
                  </Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={bidData.completionPercentage || 0}
                    sx={{ height: 8, borderRadius: 4 }}
                    color={bidData.completionPercentage > 80 ? 'success' : 'primary'}
                  />
                </Box>
              )}
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Stack direction="row" spacing={1} justifyContent="flex-end">
                {autoSaveEnabled && (
                  <Chip 
                    icon={<SaveIcon />}
                    label="Auto-saving"
                    size="small"
                    color="success"
                  />
                )}
                
                {simplifiedMode && (
                  <Chip 
                    icon={<PsychologyIcon />}
                    label="Simplified Mode"
                    size="small"
                    color="info"
                  />
                )}
                
                <Tooltip title="Get AI assistance">
                  <IconButton size="small">
                    <AIIcon />
                  </IconButton>
                </Tooltip>
              </Stack>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Break Suggestion */}
      <Collapse in={showBreakSuggestion}>
        <Alert 
          severity="warning" 
          sx={{ mb: 3 }}
          action={
            <Button size="small" onClick={() => setShowBreakSuggestion(false)}>
              Continue
            </Button>
          }
        >
          <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
            💆‍♀️ Take a 5-minute break?
          </Typography>
          High stress detected. A short break could improve your bid quality.
        </Alert>
      </Collapse>

      {/* Motivational Message */}
      <Fade in={showMotivation}>
        <Alert 
          severity="success" 
          sx={{ mb: 3 }}
          icon={<AchievementIcon />}
          action={
            <Button size="small" onClick={() => setShowMotivation(false)}>
              Thanks!
            </Button>
          }
        >
          {motivationalMessage}
        </Alert>
      </Fade>

      {/* Main Stepper */}
      <Card elevation={2}>
        <CardContent>
          <Stepper activeStep={activeStep} orientation="vertical">
            {steps.map((step, index) => (
              <Step key={step.label}>
                <StepLabel 
                  optional={step.optional && (
                    <Typography variant="caption">Optional</Typography>
                  )}
                >
                  <Typography variant={isHighCognitiveLoad ? "h6" : "body1"}>
                    {step.label}
                  </Typography>
                </StepLabel>
                <StepContent>
                  <Box sx={{ mb: 2 }}>
                    {/* Step content would be rendered here */}
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Step {index + 1} content will be implemented based on the step type.
                      This includes form fields, validation, and psychological optimization.
                    </Typography>
                    
                    {/* Step Actions */}
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        variant="contained"
                        onClick={() => handleStepChange(index + 1)}
                        disabled={index === steps.length - 1}
                      >
                        {index === steps.length - 1 ? 'Submit Bid' : 'Continue'}
                      </Button>
                      
                      {index > 0 && (
                        <Button
                          onClick={() => handleStepChange(index - 1)}
                        >
                          Back
                        </Button>
                      )}
                      
                      <Button
                        startIcon={<SaveIcon />}
                        onClick={saveBidDraft}
                      >
                        Save Draft
                      </Button>
                    </Box>
                  </Box>
                </StepContent>
              </Step>
            ))}
          </Stepper>
        </CardContent>
      </Card>
    </Box>
  );
};

export default CreateBid;
