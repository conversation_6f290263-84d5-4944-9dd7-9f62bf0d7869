/**
 * AI Bidding Engine Interface
 * The core interface where <PERSON><PERSON><PERSON><PERSON><PERSON> becomes "the bidder in digital form"
 * <PERSON>les document upload, AI processing, and automated bid generation
 */

import React, { useState, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  <PERSON>ton,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Alert,
  LinearProgress,
  Chip,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Paper,
  Stack,
  CircularProgress
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  AutoAwesome as AIIcon,
  Assignment as DocumentIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Psychology as BrainIcon,
  Speed as FastIcon,
  Security as ComplianceIcon,
  TrendingUp as AnalyticsIcon,
  Build as ProcessIcon,
  Send as SubmitIcon
} from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';
import BidBeezAIEngine, { 
  AutomatedBidResponse, 
  BidClass, 
  TenderDocument 
} from '../../services/BidBeezAIEngine';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';
import ErrorBoundary from '../../components/common/ErrorBoundary';
import { Tender } from '../../types/tender.types';

interface AIBiddingEngineProps {
  tender: Tender;
  onBidGenerated?: (bidResponse: AutomatedBidResponse) => void;
}

const AIBiddingEngine: React.FC<AIBiddingEngineProps> = ({
  tender,
  onBidGenerated
}) => {
  // Behavioral tracking
  const {
    psychologicalState,
    isStressed,
    needsSimplification
  } = useNeuroMarketing();

  // State management
  const [activeStep, setActiveStep] = useState(0);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [processing, setProcessing] = useState(false);
  const [aiResponse, setAiResponse] = useState<AutomatedBidResponse | null>(null);
  const [processingStage, setProcessingStage] = useState('');
  const [progress, setProgress] = useState(0);

  // AI Engine instance
  const aiEngine = BidBeezAIEngine.getInstance();

  // File upload handler
  const onDrop = useCallback((acceptedFiles: File[]) => {
    setUploadedFiles(prev => [...prev, ...acceptedFiles]);
    
    // Track engagement would be implemented here
    console.log('Documents uploaded', {
      fileCount: acceptedFiles.length,
      totalFiles: uploadedFiles.length + acceptedFiles.length,
      psychologicalState
    });

    // Auto-advance to next step if files uploaded
    if (acceptedFiles.length > 0 && activeStep === 0) {
      setActiveStep(1);
    }
  }, [uploadedFiles.length, activeStep, psychologicalState]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'text/plain': ['.txt']
    },
    multiple: true
  });

  // Process documents with AI
  const handleProcessDocuments = async () => {
    if (uploadedFiles.length === 0) return;

    setProcessing(true);
    setProgress(0);
    setActiveStep(2);

    try {
      // Stage 1: Document Analysis
      setProcessingStage('Analyzing tender documents...');
      setProgress(20);
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Stage 2: AI Classification
      setProcessingStage('Classifying bid type...');
      setProgress(40);
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Stage 3: Compliance Check
      setProcessingStage('Checking compliance requirements...');
      setProgress(60);
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Stage 4: Bid Generation
      setProcessingStage('Generating automated bid response...');
      setProgress(80);
      await new Promise(resolve => setTimeout(resolve, 2500));

      // Stage 5: Final Processing
      setProcessingStage('Finalizing bid package...');
      setProgress(95);

      // Call AI Engine
      const response = await aiEngine.processTenderAndGenerateBid(tender, uploadedFiles);
      
      setProgress(100);
      setProcessingStage('Complete!');
      setAiResponse(response);
      setActiveStep(3);

      // Track engagement would be implemented here
      console.log('AI bid generated', {
        classification: response.classification,
        submissionReadiness: response.submissionReadiness,
        complianceScore: response.complianceStatus.overallScore,
        psychologicalState
      });

      onBidGenerated?.(response);

    } catch (error) {
      console.error('AI processing failed:', error);
      setProcessingStage('Processing failed. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const getBidClassIcon = (classification: BidClass) => {
    switch (classification) {
      case BidClass.CONSTRUCTION: return '🏗️';
      case BidClass.IT_SERVICES: return '💻';
      case BidClass.CONSULTING: return '🧠';
      case BidClass.GOODS_SUPPLY: return '📦';
      default: return '📋';
    }
  };

  const getBidClassColor = (classification: BidClass) => {
    switch (classification) {
      case BidClass.CONSTRUCTION: return 'warning';
      case BidClass.IT_SERVICES: return 'info';
      case BidClass.CONSULTING: return 'secondary';
      case BidClass.GOODS_SUPPLY: return 'success';
      default: return 'primary';
    }
  };

  const getReadinessColor = (score: number) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'error';
  };

  const steps = [
    {
      label: 'Upload Documents',
      description: 'Upload tender documents for AI analysis'
    },
    {
      label: 'Review & Process',
      description: 'Review uploaded files and start AI processing'
    },
    {
      label: 'AI Processing',
      description: 'BidBeez AI analyzes and generates your bid'
    },
    {
      label: 'Review Results',
      description: 'Review AI-generated bid and recommendations'
    }
  ];

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('AI Bidding Engine Error:', error, errorInfo);
        // Track error for analytics
        console.log('AI bidding error', {
          error: error.message,
          component: 'AIBiddingEngine'
        });
      }}
    >
      <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Card elevation={2} sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <AIIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                🤖 BidBeez AI Engine
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Your Digital Bidder - Automated Document Processing & Bid Generation
              </Typography>
            </Box>
          </Box>

          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Tender:</strong> {tender.title} | <strong>Value:</strong> {tender.currency} {tender.value.toLocaleString()}
            </Typography>
          </Alert>
        </CardContent>
      </Card>

      {/* AI Processing Stepper */}
      <Card elevation={2}>
        <CardContent>
          <Stepper activeStep={activeStep} orientation="vertical">
            {/* Step 1: Upload Documents */}
            <Step>
              <StepLabel>
                <Typography variant="h6">Upload Tender Documents</Typography>
              </StepLabel>
              <StepContent>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Upload all tender documents (specifications, terms, pricing schedules, etc.) for AI analysis.
                </Typography>

                {/* File Upload Zone */}
                <Paper
                  {...getRootProps()}
                  sx={{
                    p: 4,
                    border: '2px dashed',
                    borderColor: isDragActive ? 'primary.main' : 'grey.300',
                    backgroundColor: isDragActive ? 'primary.50' : 'grey.50',
                    cursor: 'pointer',
                    textAlign: 'center',
                    mb: 2
                  }}
                >
                  <input {...getInputProps()} />
                  <UploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
                  <Typography variant="h6" sx={{ mb: 1 }}>
                    {isDragActive ? 'Drop files here' : 'Drag & drop files or click to browse'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Supports PDF, Word, Excel, and text files
                  </Typography>
                </Paper>

                {/* Uploaded Files List */}
                {uploadedFiles.length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      Uploaded Files ({uploadedFiles.length}):
                    </Typography>
                    <List dense>
                      {uploadedFiles.map((file, index) => (
                        <ListItem key={index} sx={{ py: 0.5 }}>
                          <ListItemIcon>
                            <DocumentIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary={file.name}
                            secondary={`${(file.size / 1024 / 1024).toFixed(2)} MB`}
                          />
                          <Button
                            size="small"
                            color="error"
                            onClick={() => removeFile(index)}
                          >
                            Remove
                          </Button>
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                )}

                <Button
                  variant="contained"
                  onClick={() => setActiveStep(1)}
                  disabled={uploadedFiles.length === 0}
                  startIcon={<CheckIcon />}
                >
                  Continue with {uploadedFiles.length} files
                </Button>
              </StepContent>
            </Step>

            {/* Step 2: Review & Process */}
            <Step>
              <StepLabel>
                <Typography variant="h6">Review & Start Processing</Typography>
              </StepLabel>
              <StepContent>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Review your uploaded documents and start the AI processing.
                </Typography>

                <Alert severity="success" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    ✅ {uploadedFiles.length} documents ready for AI analysis
                  </Typography>
                </Alert>

                <Stack direction="row" spacing={2}>
                  <Button
                    variant="contained"
                    onClick={handleProcessDocuments}
                    disabled={processing}
                    startIcon={<BrainIcon />}
                    size="large"
                  >
                    Start AI Processing
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => setActiveStep(0)}
                  >
                    Back
                  </Button>
                </Stack>
              </StepContent>
            </Step>

            {/* Step 3: AI Processing */}
            <Step>
              <StepLabel>
                <Typography variant="h6">AI Processing</Typography>
              </StepLabel>
              <StepContent>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  BidBeez AI is analyzing your documents and generating an automated bid response.
                </Typography>

                {processing && (
                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <CircularProgress size={20} sx={{ mr: 1 }} />
                      <Typography variant="body2">{processingStage}</Typography>
                    </Box>
                    <LinearProgress variant="determinate" value={progress} sx={{ height: 8, borderRadius: 4 }} />
                    <Typography variant="caption" color="text.secondary">
                      {progress}% complete
                    </Typography>
                  </Box>
                )}

                {/* Processing Stages */}
                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid item xs={6} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <DocumentIcon color="primary" sx={{ fontSize: 32, mb: 1 }} />
                      <Typography variant="caption">Document Analysis</Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <ProcessIcon color="primary" sx={{ fontSize: 32, mb: 1 }} />
                      <Typography variant="caption">AI Classification</Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <ComplianceIcon color="primary" sx={{ fontSize: 32, mb: 1 }} />
                      <Typography variant="caption">Compliance Check</Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <AnalyticsIcon color="primary" sx={{ fontSize: 32, mb: 1 }} />
                      <Typography variant="caption">Bid Generation</Typography>
                    </Paper>
                  </Grid>
                </Grid>
              </StepContent>
            </Step>

            {/* Step 4: Review Results */}
            <Step>
              <StepLabel>
                <Typography variant="h6">AI-Generated Bid Results</Typography>
              </StepLabel>
              <StepContent>
                {aiResponse && (
                  <Box>
                    <Typography variant="body2" sx={{ mb: 3 }}>
                      BidBeez AI has analyzed your tender and generated a comprehensive bid response.
                    </Typography>

                    {/* Bid Classification */}
                    <Card sx={{ mb: 3 }}>
                      <CardContent>
                        <Typography variant="h6" sx={{ mb: 2 }}>
                          🎯 Bid Classification & Analysis
                        </Typography>
                        
                        <Grid container spacing={2}>
                          <Grid item xs={12} md={6}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                              <Typography variant="h4" sx={{ mr: 1 }}>
                                {getBidClassIcon(aiResponse.classification)}
                              </Typography>
                              <Box>
                                <Chip
                                  label={aiResponse.classification.replace('_', ' ').toUpperCase()}
                                  color={getBidClassColor(aiResponse.classification)}
                                  size="small"
                                />
                                <Typography variant="body2" color="text.secondary">
                                  AI-detected bid category
                                </Typography>
                              </Box>
                            </Box>
                          </Grid>
                          
                          <Grid item xs={12} md={6}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h3" color={getReadinessColor(aiResponse.submissionReadiness)}>
                                {aiResponse.submissionReadiness}%
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Submission Readiness
                              </Typography>
                            </Box>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>

                    {/* Compliance Status */}
                    <Card sx={{ mb: 3 }}>
                      <CardContent>
                        <Typography variant="h6" sx={{ mb: 2 }}>
                          ⚖️ Compliance Analysis
                        </Typography>
                        
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Typography variant="h4" sx={{ mr: 2 }}>
                            {aiResponse.complianceStatus.overallScore}%
                          </Typography>
                          <Box>
                            <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                              Overall Compliance Score
                            </Typography>
                            <Chip
                              label={aiResponse.complianceStatus.riskLevel.toUpperCase()}
                              color={aiResponse.complianceStatus.riskLevel === 'low' ? 'success' : 'warning'}
                              size="small"
                            />
                          </Box>
                        </Box>

                        {aiResponse.complianceStatus.missingDocuments.length > 0 && (
                          <Alert severity="warning" sx={{ mb: 2 }}>
                            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                              Missing Documents:
                            </Typography>
                            <List dense>
                              {aiResponse.complianceStatus.missingDocuments.map((doc, index) => (
                                <ListItem key={index} sx={{ py: 0 }}>
                                  <ListItemIcon>
                                    <WarningIcon color="warning" />
                                  </ListItemIcon>
                                  <ListItemText primary={doc} />
                                </ListItem>
                              ))}
                            </List>
                          </Alert>
                        )}
                      </CardContent>
                    </Card>

                    {/* AI Recommendations */}
                    <Card sx={{ mb: 3 }}>
                      <CardContent>
                        <Typography variant="h6" sx={{ mb: 2 }}>
                          💡 AI Recommendations
                        </Typography>
                        
                        <List>
                          {aiResponse.recommendations.map((rec, index) => (
                            <ListItem key={index}>
                              <ListItemIcon>
                                <Chip
                                  label={rec.priority.toUpperCase()}
                                  color={rec.priority === 'high' ? 'error' : rec.priority === 'medium' ? 'warning' : 'info'}
                                  size="small"
                                />
                              </ListItemIcon>
                              <ListItemText
                                primary={rec.recommendation}
                                secondary={`Impact: ${rec.impact} | Effort: ${rec.effort}`}
                              />
                            </ListItem>
                          ))}
                        </List>
                      </CardContent>
                    </Card>

                    {/* Action Buttons */}
                    <Stack direction="row" spacing={2}>
                      <Button
                        variant="contained"
                        size="large"
                        startIcon={<SubmitIcon />}
                        disabled={aiResponse.submissionReadiness < 80}
                      >
                        Submit Bid ({aiResponse.submissionReadiness}% Ready)
                      </Button>
                      <Button
                        variant="outlined"
                        size="large"
                        onClick={() => window.open(`/bids/${aiResponse.bidId}/edit`)}
                      >
                        Review & Edit
                      </Button>
                      <Button
                        variant="outlined"
                        onClick={() => setActiveStep(0)}
                      >
                        Start Over
                      </Button>
                    </Stack>
                  </Box>
                )}
              </StepContent>
            </Step>
          </Stepper>
        </CardContent>
      </Card>
      </Box>
    </ErrorBoundary>
  );
};

export default AIBiddingEngine;
