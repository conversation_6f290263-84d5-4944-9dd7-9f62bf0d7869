/**
 * Main Dashboard - MVP Core with Progressive Feature Activation
 * Central hub that adapts based on enabled features
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
  Alert,
  IconButton,
  Tooltip,
  Badge,
  Skeleton,
  Avatar,
  Divider,
  useTheme,
  alpha
} from '@mui/material';
import {
  Search as SearchIcon,
  Assignment as AssignmentIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  EmojiEvents as EmojiEventsIcon,
  Business as BusinessIcon,
  Psychology as PsychologyIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  Help as HelpIcon,
  LocalShipping as LocalShippingIcon,
  FlashOn as FlashOnIcon,
  AttachMoney as MoneyIcon,
  Star as StarIcon,
  Timer as TimerIcon,
  Leaderboard as LeaderboardIcon,
  Psychology as BrainIcon,
  Whatshot as FireIcon,
  Speed as SpeedIcon,
  GpsFixed as TargetIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';
import usePsychologicalSystems from '../../hooks/usePsychologicalSystems';
import {
  useFeatureFlags,
  useComplianceFeatures,
  useGamificationFeatures,
  useEcosystemFeatures,
  useAnalyticsFeatures,
  FeatureGate
} from '../../hooks/useFeatureFlags';
import AdaptiveInterface from '../../components/adaptive/AdaptiveInterface';
import LiveMarketFeed from '../../components/dashboard/LiveMarketFeed';
import OpportunityCard from '../../components/dashboard/OpportunityCard';
import CompetitiveIntelligence from '../../components/dashboard/CompetitiveIntelligence';
import EnhancedDashboardOverview from '../../components/dashboard/EnhancedDashboardOverview';
import QuestProgressSystem from '../../components/dashboard/QuestProgressSystem';
import ConfidenceCoach from '../../components/dashboard/ConfidenceCoach';
import PredictiveMatches from '../../components/dashboard/PredictiveMatches';
import BeeScarcitySystem from '../../components/dashboard/BeeScarcitySystem';
import TenderScarcitySystem from '../../components/dashboard/TenderScarcitySystem';
import ComplianceScarcitySystem from '../../components/dashboard/ComplianceScarcitySystem';
import TenderIntelligenceEngine from '../../components/intelligence/TenderIntelligenceEngine';
import TenderMapIntelligence from '../../components/maps/TenderMapIntelligence';
import UserTypeAdaptiveDashboard from '../../components/adaptive/UserTypeAdaptiveDashboard';
import BidSummaryWidget from '../../components/analytics/BidSummaryWidget';
import SupplierDashboard from '../../components/supplier/SupplierDashboard';
import WhatsAppStatusWidget from '../../components/whatsapp/WhatsAppStatusWidget';
import {
  useGetPsychologicalDashboardQuery,
  useGetCompetitorDataQuery,
  useGetUserRankingQuery
} from '../../services/api/psychological.api';

// Enhanced mock data with crypto exchange psychology
const mockDashboardData = {
  activeBids: 3,
  pendingTenders: 12,
  successRate: 68,
  totalEarnings: 8750000, // Increased for psychological impact
  recentActivity: [
    { id: 1, type: 'bid_submitted', description: 'Bid submitted for Road Maintenance Project', time: '2 hours ago' },
    { id: 2, type: 'tender_found', description: 'New tender matches your criteria', time: '4 hours ago' },
    { id: 3, type: 'compliance_check', description: 'Compliance documents verified', time: '1 day ago' }
  ],
  // Crypto exchange style data
  profitPotentialToday: 32800000,
  hotOpportunities: 5,
  successPrediction: 78,
  currentRanking: 23,
  totalBidders: 1247,
  streakCount: 4,
  confidenceLevel: 85,
  stressLevel: 23,
  energyLevel: 94,
  competitorActivity: {
    buildCorp: { position: 22, change: 1, threat: 'high' },
    techSolutions: { position: 24, change: -2, threat: 'low' },
    megaBuild: { position: 1, change: 0, threat: 'aspirational' }
  },
  urgentDeadlines: [
    { tender: 'IT Infrastructure Upgrade', value: 8400000, timeLeft: '18h 32m', successRate: 92 },
    { tender: 'Road Maintenance Contract', value: 5200000, timeLeft: '4h 12m', successRate: 87 },
    { tender: 'School Security System', value: 15600000, timeLeft: '2d 14h', successRate: 94 }
  ],
  achievements: [
    { id: 'tech_master', name: 'Tech Master', progress: 75, description: '1 more IT win needed' },
    { id: 'gov_specialist', name: 'Government Specialist', progress: 40, description: '3 more gov wins needed' }
  ]
};

const MainDashboard: React.FC = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState(mockDashboardData);
  const [loading, setLoading] = useState(false);

  // API queries for real-time data
  const {
    data: psychologicalData,
    isLoading: psychLoading,
    error: psychError
  } = useGetPsychologicalDashboardQuery(user?.id || 'demo-user');

  const {
    data: competitorData,
    isLoading: competitorLoading
  } = useGetCompetitorDataQuery({
    userId: user?.id || 'demo-user',
    limit: 5
  });

  const {
    data: rankingData,
    isLoading: rankingLoading
  } = useGetUserRankingQuery(user?.id || 'demo-user');

  // Feature flags
  const { isFeatureEnabled, getMVPReadiness } = useFeatureFlags();
  const { complianceToolsEnabled, protestManagementEnabled } = useComplianceFeatures();
  const { achievementsEnabled, leaderboardsEnabled } = useGamificationFeatures();
  const { skillSyncEnabled, toolSyncEnabled } = useEcosystemFeatures();
  const { advancedAnalyticsEnabled, neuroMarketingEnabled } = useAnalyticsFeatures();

  // NeuroMarketing optimization (only if enabled)
  const neuroMarketing = neuroMarketingEnabled ? useNeuroMarketing() : null;
  const { profile: psychProfile } = usePsychologicalSystems();
  const {
    psychologicalState,
    isStressed,
    needsSimplification,
    shouldShowHelp,
    shouldShowEncouragement
  } = neuroMarketing || {
    psychologicalState: null,
    isStressed: false,
    needsSimplification: false,
    shouldShowHelp: false,
    shouldShowEncouragement: false
  };

  // Get user archetype for personalization
  const userArchetype = psychProfile?.archetype || 'achiever';

  // Merge API data with mock data
  const effectiveDashboardData = psychologicalData?.data ? {
    ...dashboardData,
    ...psychologicalData.data
  } : dashboardData;

  // Check if we're loading any critical data
  const isLoadingData = loading || psychLoading || competitorLoading || rankingLoading;

  // MVP readiness check
  const mvpReadiness = getMVPReadiness();

  // Psychological adaptations (only if NeuroMarketing enabled)
  const getCardElevation = () => {
    return neuroMarketingEnabled && isStressed ? 1 : 3;
  };

  const getButtonSize = () => {
    return neuroMarketingEnabled && psychologicalState?.cognitiveLoad > 0.7 ? 'large' : 'medium';
  };

  // Core MVP Features - Always Available
  const renderCoreMetrics = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} sm={6} md={3}>
        <Card elevation={getCardElevation()}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <AssignmentIcon color="primary" sx={{ mr: 1 }} />
              <Typography variant="h6">Active Bids</Typography>
            </Box>
            <Typography variant="h3" color="primary">
              {dashboardData.activeBids}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Currently tracking
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card elevation={getCardElevation()}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <SearchIcon color="info" sx={{ mr: 1 }} />
              <Typography variant="h6">New Tenders</Typography>
            </Box>
            <Typography variant="h3" color="info.main">
              {dashboardData.pendingTenders}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Matching your criteria
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card elevation={getCardElevation()}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <TrendingUpIcon color="success" sx={{ mr: 1 }} />
              <Typography variant="h6">Success Rate</Typography>
            </Box>
            <Typography variant="h3" color="success.main">
              {dashboardData.successRate}%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Last 12 months
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card elevation={getCardElevation()}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <BusinessIcon color="warning" sx={{ mr: 1 }} />
              <Typography variant="h6">Total Earnings</Typography>
            </Box>
            <Typography variant="h3" color="warning.main">
              R{dashboardData.totalEarnings.toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              This year
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  // Quick Actions - Core + Feature-gated
  const renderQuickActions = () => (
    <Card elevation={getCardElevation()}>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Quick Actions
        </Typography>
        <Grid container spacing={2}>
          {/* Core Actions - Always Available */}
          <Grid item xs={12} sm={6} md={4}>
            <Button
              fullWidth
              variant="contained"
              startIcon={<SearchIcon />}
              onClick={() => navigate('/tenders')}
              size={getButtonSize()}
            >
              Find Tenders
            </Button>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<AssignmentIcon />}
              onClick={() => navigate('/bids/create')}
              size={getButtonSize()}
            >
              Create Bid
            </Button>
          </Grid>

          {/* Feature-gated Actions */}
          <FeatureGate feature="sa_compliance_tools">
            <Grid item xs={12} sm={6} md={4}>
              <Button
                fullWidth
                variant="outlined"
                color="success"
                startIcon={<SecurityIcon />}
                onClick={() => navigate('/compliance')}
                size={getButtonSize()}
              >
                Compliance Tools
              </Button>
            </Grid>
          </FeatureGate>

          <FeatureGate feature="skillsync_integration">
            <Grid item xs={12} sm={6} md={4}>
              <Button
                fullWidth
                variant="outlined"
                color="secondary"
                startIcon={<BusinessIcon />}
                onClick={() => navigate('/ecosystem/skills')}
                size={getButtonSize()}
              >
                Find Skills
              </Button>
            </Grid>
          </FeatureGate>

          <FeatureGate feature="courier_dispatch">
            <Grid item xs={12} sm={6} md={4}>
              <Button
                fullWidth
                variant="outlined"
                color="warning"
                startIcon={<LocalShippingIcon />}
                onClick={() => navigate('/courier')}
                size={getButtonSize()}
              >
                Courier Dispatch
              </Button>
            </Grid>
          </FeatureGate>

          <FeatureGate feature="advanced_analytics">
            <Grid item xs={12} sm={6} md={4}>
              <Button
                fullWidth
                variant="outlined"
                color="info"
                startIcon={<TrendingUpIcon />}
                onClick={() => navigate('/analytics')}
                size={getButtonSize()}
              >
                Analytics
              </Button>
            </Grid>
          </FeatureGate>
        </Grid>
      </CardContent>
    </Card>
  );

  // Feature Status Indicators (for development/admin)
  const renderFeatureStatus = () => {
    if (!user?.role?.includes('admin') && process.env.NODE_ENV === 'production') {
      return null;
    }

    return (
      <Card elevation={getCardElevation()} sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Platform Features Status
          </Typography>
          
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary">
              MVP Readiness: {mvpReadiness.percentage.toFixed(0)}%
            </Typography>
            <LinearProgress 
              variant="determinate" 
              value={mvpReadiness.percentage} 
              sx={{ mt: 1 }}
              color={mvpReadiness.ready ? 'success' : 'warning'}
            />
          </Box>

          <Grid container spacing={1}>
            <Grid item>
              <Chip 
                label="Core Features" 
                color="success" 
                variant="filled" 
                size="small" 
              />
            </Grid>
            <Grid item>
              <Chip 
                label="Compliance" 
                color={complianceToolsEnabled ? 'success' : 'default'} 
                variant={complianceToolsEnabled ? 'filled' : 'outlined'}
                size="small" 
              />
            </Grid>
            <Grid item>
              <Chip 
                label="Gamification" 
                color={achievementsEnabled ? 'success' : 'default'} 
                variant={achievementsEnabled ? 'filled' : 'outlined'}
                size="small" 
              />
            </Grid>
            <Grid item>
              <Chip 
                label="Ecosystem" 
                color={skillSyncEnabled || toolSyncEnabled ? 'success' : 'default'} 
                variant={skillSyncEnabled || toolSyncEnabled ? 'filled' : 'outlined'}
                size="small" 
              />
            </Grid>
            <Grid item>
              <Chip 
                label="Analytics" 
                color={advancedAnalyticsEnabled ? 'success' : 'default'} 
                variant={advancedAnalyticsEnabled ? 'filled' : 'outlined'}
                size="small" 
              />
            </Grid>
            <Grid item>
              <Chip 
                label="NeuroMarketing" 
                color={neuroMarketingEnabled ? 'success' : 'default'} 
                variant={neuroMarketingEnabled ? 'filled' : 'outlined'}
                size="small" 
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    );
  };

  // Gamification Section (feature-gated)
  const renderGamificationSection = () => (
    <FeatureGate feature="achievement_system">
      <Card elevation={getCardElevation()}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <EmojiEventsIcon color="warning" sx={{ mr: 1 }} />
            <Typography variant="h6">Achievements</Typography>
          </Box>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Track your progress and unlock rewards
          </Typography>
          <Button 
            variant="outlined" 
            onClick={() => navigate('/achievements')}
            size={getButtonSize()}
          >
            View Achievements
          </Button>
        </CardContent>
      </Card>
    </FeatureGate>
  );

  // Compliance Section (feature-gated)
  const renderComplianceSection = () => (
    <FeatureGate feature="sa_compliance_tools">
      <Card elevation={getCardElevation()}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <SecurityIcon color="success" sx={{ mr: 1 }} />
            <Typography variant="h6">SA Compliance</Typography>
          </Box>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Professional legal compliance tools
          </Typography>
          <Button 
            variant="contained" 
            color="success"
            onClick={() => navigate('/compliance')}
            size={getButtonSize()}
          >
            Access Tools
          </Button>
        </CardContent>
      </Card>
    </FeatureGate>
  );

  if (isLoadingData) {
    return (
      <AdaptiveInterface>
        <Box sx={{ p: 3 }}>
          {/* Loading Live Feed */}
          <Skeleton variant="rectangular" height={60} sx={{ mb: 3, borderRadius: 1 }} />

          {/* Loading Header */}
          <Skeleton variant="rectangular" height={120} sx={{ mb: 3, borderRadius: 2 }} />

          {/* Loading Cards */}
          <Grid container spacing={3}>
            {[1, 2, 3, 4].map((item) => (
              <Grid item xs={12} sm={6} md={3} key={item}>
                <Skeleton variant="rectangular" height={120} sx={{ borderRadius: 1 }} />
              </Grid>
            ))}
          </Grid>

          {/* Loading Opportunities */}
          <Box sx={{ mt: 3 }}>
            {[1, 2, 3].map((item) => (
              <Skeleton key={item} variant="rectangular" height={200} sx={{ mb: 2, borderRadius: 2 }} />
            ))}
          </Box>
        </Box>
      </AdaptiveInterface>
    );
  }

  // Get psychological header message
  const getPsychologicalHeader = () => {
    if (isStressed) {
      return {
        title: `🌟 Take it easy today, ${user?.username || 'User'}`,
        subtitle: "We've simplified your view • 💚 2 easy wins available",
        color: theme.palette.success.main
      };
    }

    if (psychologicalState?.engagementLevel > 0.8) {
      return {
        title: `🔥 You're on fire, ${user?.username || 'User'}! ${effectiveDashboardData.streakCount} win streak!`,
        subtitle: `Opportunity Hub • ⚡ Peak Performance • 🎯 ${effectiveDashboardData.hotOpportunities} hot opportunities`,
        color: theme.palette.warning.main
      };
    }

    switch (userArchetype) {
      case 'achiever':
        return {
          title: `👑 Welcome back, Achiever! Ready to dominate today's bids?`,
          subtitle: `Opportunity Hub • 🧠 Confidence: ${effectiveDashboardData.confidenceLevel}% • 🎯 ${effectiveDashboardData.hotOpportunities} perfect matches`,
          color: theme.palette.primary.main
        };
      case 'hunter':
        return {
          title: `🎯 Hunt mode activated, ${user?.username || 'User'}!`,
          subtitle: `Opportunity Hub • 🏹 ${effectiveDashboardData.hotOpportunities} targets spotted • ⚡ Strike fast!`,
          color: theme.palette.error.main
        };
      case 'analyst':
        return {
          title: `📊 Data-driven success, ${user?.username || 'User'}`,
          subtitle: `Opportunity Hub • 🧮 ${effectiveDashboardData.successPrediction}% success prediction • 📈 Market trending up`,
          color: theme.palette.info.main
        };
      default:
        return {
          title: `Welcome back, ${user?.username || 'User'}!`,
          subtitle: `Opportunity Hub • 💰 R${(effectiveDashboardData.profitPotentialToday / 1000000).toFixed(1)}M potential today`,
          color: theme.palette.primary.main
        };
    }
  };

  const headerConfig = getPsychologicalHeader();

  return (
    <AdaptiveInterface>
      <Box sx={{ p: 3 }}>
        {/* Live Market Feed with RFQ Force-Feeding */}
        <Box sx={{ mb: 3 }}>
          <LiveMarketFeed
            height={60}
            speed={isStressed ? 'slow' : 'medium'}
            enablePsychologicalFiltering={neuroMarketingEnabled}
          />
        </Box>

        {/* User Type Adaptive Dashboard - Tender Document Driven */}
        <Box sx={{ mb: 3 }}>
          <UserTypeAdaptiveDashboard />
        </Box>

        {/* Enhanced Dashboard Overview - RFQ/Tender Mix */}
        <Box sx={{ mb: 3 }}>
          <EnhancedDashboardOverview />
        </Box>

        {/* Crypto Exchange Style Header */}
        <Box sx={{
          mb: 3,
          p: 3,
          background: `linear-gradient(135deg, ${alpha(headerConfig.color, 0.1)} 0%, ${alpha(headerConfig.color, 0.05)} 100%)`,
          borderRadius: 2,
          border: `1px solid ${alpha(headerConfig.color, 0.2)}`
        }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ flex: 1 }}>
              <Typography
                variant={needsSimplification ? 'h5' : 'h4'}
                component="h1"
                sx={{
                  fontWeight: 600,
                  color: headerConfig.color,
                  mb: 0.5
                }}
              >
                {headerConfig.title}
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  color: 'text.secondary',
                  fontWeight: 500
                }}
              >
                {headerConfig.subtitle}
              </Typography>
            </Box>

            {/* Quick Stats */}
            <Box sx={{ display: 'flex', gap: 3, alignItems: 'center' }}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" color="success.main" fontWeight="bold">
                  R{(effectiveDashboardData.totalEarnings / 1000000).toFixed(1)}M
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Total Earnings
                </Typography>
              </Box>

              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" color="primary.main" fontWeight="bold">
                  #{effectiveDashboardData.currentRanking}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Ranking
                </Typography>
              </Box>

              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" color="warning.main" fontWeight="bold">
                  {effectiveDashboardData.successRate}%
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Success Rate
                </Typography>
              </Box>
            </Box>

            {/* Action Buttons */}
            <Box sx={{ display: 'flex', gap: 1, ml: 3 }}>
              {shouldShowHelp && (
                <Tooltip title="Get help">
                  <IconButton onClick={() => navigate('/help')}>
                    <HelpIcon />
                  </IconButton>
                </Tooltip>
              )}

              <Tooltip title="Notifications">
                <IconButton>
                  <Badge badgeContent={3} color="error">
                    <NotificationsIcon />
                  </Badge>
                </IconButton>
              </Tooltip>

              <Tooltip title="Settings">
                <IconButton onClick={() => navigate('/settings')}>
                  <SettingsIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        </Box>

        {/* NeuroMarketing Stress Indicator */}
        {neuroMarketingEnabled && isStressed && (
          <Alert 
            severity="info" 
            sx={{ mb: 3 }}
            icon={<PsychologyIcon />}
          >
            We've simplified your dashboard to reduce complexity. Take your time!
          </Alert>
        )}

        {/* Encouragement Message */}
        {shouldShowEncouragement && (
          <Alert severity="success" sx={{ mb: 3 }}>
            🎯 You're making great progress! Keep up the excellent work on your tenders.
          </Alert>
        )}

        {/* Feature Status (Development/Admin only) */}
        {renderFeatureStatus()}

        {/* Crypto Exchange Style Trading Floor */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          {/* Profit Potential Today */}
          <Grid item xs={12} md={3}>
            <Card sx={{
              background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.1)} 0%, ${alpha(theme.palette.success.main, 0.05)} 100%)`,
              border: `1px solid ${alpha(theme.palette.success.main, 0.3)}`
            }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <MoneyIcon sx={{ fontSize: 40, color: theme.palette.success.main, mb: 1 }} />
                <Typography variant="h4" fontWeight="bold" color="success.main">
                  R{(effectiveDashboardData.profitPotentialToday / 1000000).toFixed(1)}M
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  💰 Profit Potential Today
                </Typography>
                <Typography variant="caption" color="success.main" fontWeight="bold">
                  🔥 {effectiveDashboardData.hotOpportunities} hot opportunities
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Success Radar */}
          <Grid item xs={12} md={3}>
            <Card sx={{
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.primary.main, 0.05)} 100%)`,
              border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`
            }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <TargetIcon sx={{ fontSize: 40, color: theme.palette.primary.main, mb: 1 }} />
                <Typography variant="h4" fontWeight="bold" color="primary.main">
                  {effectiveDashboardData.successPrediction}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  🎯 Success Prediction
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={effectiveDashboardData.successPrediction}
                  sx={{
                    mt: 1,
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: alpha(theme.palette.primary.main, 0.2),
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: theme.palette.primary.main
                    }
                  }}
                />
              </CardContent>
            </Card>
          </Grid>

          {/* Live Ranking */}
          <Grid item xs={12} md={3}>
            <Card sx={{
              background: `linear-gradient(135deg, ${alpha(theme.palette.warning.main, 0.1)} 0%, ${alpha(theme.palette.warning.main, 0.05)} 100%)`,
              border: `1px solid ${alpha(theme.palette.warning.main, 0.3)}`
            }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <EmojiEventsIcon sx={{ fontSize: 40, color: theme.palette.warning.main, mb: 1 }} />
                <Typography variant="h4" fontWeight="bold" color="warning.main">
                  #{effectiveDashboardData.currentRanking}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  🏆 Live Ranking
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  of {effectiveDashboardData.totalBidders.toLocaleString()} bidders
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Psychological State */}
          <Grid item xs={12} md={3}>
            <Card sx={{
              background: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.1)} 0%, ${alpha(theme.palette.info.main, 0.05)} 100%)`,
              border: `1px solid ${alpha(theme.palette.info.main, 0.3)}`
            }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <BrainIcon sx={{ fontSize: 40, color: theme.palette.info.main, mb: 1 }} />
                <Typography variant="h4" fontWeight="bold" color="info.main">
                  {effectiveDashboardData.confidenceLevel}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  🧠 Confidence Level
                </Typography>
                <Typography variant="caption" color="success.main" fontWeight="bold">
                  ⚡ Energy: {effectiveDashboardData.energyLevel}%
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Hot Opportunities Section */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            🔥 Live Opportunities - Trading Floor View
            <Chip
              label={`${effectiveDashboardData.urgentDeadlines.length} URGENT`}
              size="small"
              color="error"
              sx={{ fontWeight: 'bold' }}
            />
          </Typography>

          <Grid container spacing={3}>
            {effectiveDashboardData.urgentDeadlines.map((opportunity, index) => (
              <Grid item xs={12} key={index}>
                <OpportunityCard
                  opportunity={{
                    id: `opp_${index}`,
                    title: opportunity.tender,
                    client: 'Government Entity',
                    location: 'Gauteng',
                    value: opportunity.value,
                    timeLeft: opportunity.timeLeft,
                    timeLeftHours: parseFloat(opportunity.timeLeft.replace(/[^\d.]/g, '')),
                    successRate: opportunity.successRate,
                    competitorCount: Math.floor(Math.random() * 15) + 5,
                    urgency: opportunity.timeLeft.includes('h') && parseFloat(opportunity.timeLeft) < 12 ? 'critical' : 'high',
                    category: opportunity.tender.includes('IT') ? 'Technology' :
                             opportunity.tender.includes('Road') ? 'Infrastructure' : 'Security',
                    requirements: ['CIDB Grade 7+', 'B-BBEE Level 4', 'Previous Experience'],
                    psychologicalMatch: opportunity.successRate,
                    profitPotential: opportunity.value > 10000000 ? 'excellent' : 'high',
                    difficultyLevel: opportunity.successRate > 90 ? 'easy' : 'moderate',
                    exclusiveAccess: opportunity.value > 15000000,
                    trending: index === 0,
                    hotDeal: opportunity.timeLeft.includes('h'),
                    achievementUnlock: opportunity.tender.includes('IT') ? 'Tech Master' : undefined,
                    rankingImpact: Math.floor(opportunity.value / 1000000)
                  }}
                  onBidNow={(id) => console.log('Bid now:', id)}
                  onSave={(id) => console.log('Save:', id)}
                  onAnalyze={(id) => console.log('Analyze:', id)}
                />
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Core Metrics */}
        <Box sx={{ mb: 3 }}>
          {renderCoreMetrics()}
        </Box>

        {/* Predictive Matches System */}
        <Box sx={{ mb: 3 }}>
          <PredictiveMatches />
        </Box>

        {/* Main Content Grid */}
        <Grid container spacing={3}>
          {/* Left Column - Quest Progress & Confidence Coach */}
          <Grid item xs={12} md={4}>
            <Grid container spacing={3}>
              {/* Quest Progress System */}
              <Grid item xs={12}>
                <QuestProgressSystem />
              </Grid>

              {/* Confidence Coach */}
              <Grid item xs={12}>
                <ConfidenceCoach />
              </Grid>

              {/* Bee Scarcity & Monetization */}
              <Grid item xs={12}>
                <BeeScarcitySystem />
              </Grid>

              {/* SkillSync & ToolSync Compliance Terror */}
              <Grid item xs={12}>
                <ComplianceScarcitySystem />
              </Grid>
            </Grid>
          </Grid>

          {/* Right Column - Quick Actions & Competitive Intelligence */}
          <Grid item xs={12} md={8}>
            <Grid container spacing={3}>
              {/* Quick Actions */}
              <Grid item xs={12}>
                {renderQuickActions()}
              </Grid>

              {/* Tender Scarcity System */}
              <Grid item xs={12}>
                <TenderScarcitySystem />
              </Grid>

              {/* Competitive Intelligence */}
              <Grid item xs={12}>
                <CompetitiveIntelligence
                  userRanking={rankingData?.data || {
                    currentPosition: effectiveDashboardData.currentRanking,
                    previousPosition: effectiveDashboardData.currentRanking + 1,
                    totalCompetitors: effectiveDashboardData.totalBidders,
                    pointsToNext: 250,
                    pointsFromPrevious: 180,
                    category: 'Construction',
                    percentile: Math.round((1 - effectiveDashboardData.currentRanking / effectiveDashboardData.totalBidders) * 100)
                  }}
                  competitors={competitorData?.data || [
                    {
                      id: 'buildcorp',
                      name: 'BuildCorp',
                      currentRanking: 22,
                      previousRanking: 23,
                      totalEarnings: 8900000,
                      successRate: 71,
                      activeBids: 4,
                      recentActivity: 'Viewed IT Infrastructure tender',
                      threatLevel: 'high',
                      specialization: 'Infrastructure',
                      lastSeen: '2 minutes ago',
                      isRival: true
                    },
                    {
                      id: 'techsolutions',
                      name: 'TechSolutions',
                      currentRanking: 24,
                      previousRanking: 22,
                      totalEarnings: 8100000,
                      successRate: 65,
                      activeBids: 2,
                      recentActivity: 'Submitted security bid',
                      threatLevel: 'medium',
                      specialization: 'Technology',
                      lastSeen: '15 minutes ago'
                    },
                    {
                      id: 'megabuild',
                      name: 'MegaBuild',
                      currentRanking: 1,
                      previousRanking: 1,
                      totalEarnings: 45200000,
                      successRate: 94,
                      activeBids: 8,
                      recentActivity: 'Won R12.8M contract',
                      threatLevel: 'critical',
                      specialization: 'Large Infrastructure',
                      lastSeen: '1 hour ago',
                      isTarget: true
                    }
                  ]}
                  onViewCompetitor={(id) => console.log('View competitor:', id)}
                  onRefresh={() => console.log('Refresh competitors')}
                />
              </Grid>

              {/* Bid Analytics Summary */}
              <FeatureGate feature="bid_analytics">
                <Grid item xs={12}>
                  <BidSummaryWidget
                    userId={user?.id || 'demo'}
                    compact={true}
                    showEconomicImpact={true}
                    showPsychologicalInsights={true}
                    onViewFullAnalytics={() => navigate('/analytics')}
                  />
                </Grid>
              </FeatureGate>

              {/* WhatsApp Auto-Bidding Status */}
              <FeatureGate feature="whatsapp_autobid">
                <Grid item xs={12}>
                  <WhatsAppStatusWidget
                    userId={user?.id || 'demo'}
                    onConfigureSettings={() => navigate('/settings/whatsapp')}
                  />
                </Grid>
              </FeatureGate>

              {/* Supplier Dashboard Widget */}
              <FeatureGate feature="supplier_revenue">
                <Grid item xs={12}>
                  <SupplierDashboard
                    userId={user?.id || 'demo'}
                    compact={true}
                    onViewFullDashboard={() => navigate('/supplier')}
                  />
                </Grid>
              </FeatureGate>

              {/* Compliance Section */}
              <Grid item xs={12}>
                {renderComplianceSection()}
              </Grid>

              {/* Gamification Section */}
              <Grid item xs={12}>
                {renderGamificationSection()}
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Box>
    </AdaptiveInterface>
  );
};

export default MainDashboard;
