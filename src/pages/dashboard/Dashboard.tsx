/**
 * Enhanced Dashboard with Behavioral Psychology Integration
 * Combines existing BidBeez functionality with new tender management
 */

import React, { useEffect, useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  LinearProgress,
  Avatar,
  Chip,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Alert,
  Fade,
  Stack,
  Divider,
  Paper
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Assignment as AssignmentIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingUpIcon,
  Notifications as NotificationsIcon,
  Add as AddIcon,
  Visibility as VisibilityIcon,
  Psychology as PsychologyIcon,
  EmojiEvents as AchievementIcon,
  Search as SearchIcon,
  SwipeRight as SwipeIcon,
  Security as ComplianceIcon,
  AutoAwesome as AIIcon,
  Timer as TimerIcon,
  Star as StarIcon
} from '@mui/icons-material';
import { useGetDashboardStatsQuery } from '../../services/api/dashboard.api';
import TaskAnalyticsChart from '../../components/charts/TaskAnalyticsChart';
import PaymentChart from '../../components/charts/PaymentChart';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';
import BehavioralTenderService from '../../services/BehavioralTenderService';
import { DashboardSummary, Tender } from '../../types/tender.types';

const Dashboard: React.FC = () => {
  // Existing BidBeez data
  const { data: dashboardStats, isLoading, error } = useGetDashboardStatsQuery();
  
  // New tender management state
  const [tenderSummary, setTenderSummary] = useState<DashboardSummary | null>(null);
  const [personalizedTenders, setPersonalizedTenders] = useState<Tender[]>([]);
  const [showPsychologicalInsights, setShowPsychologicalInsights] = useState(false);

  // Behavioral tracking
  const {
    psychologicalState,
    adaptiveSettings,
    isHighCognitiveLoad,
    isStressed,
    needsSimplification,
    shouldUseCalming
  } = useNeuroMarketing();

  const behavioralService = BehavioralTenderService.getInstance();

  // Initialize behavioral tracking
  useEffect(() => {
    // Track engagement would be implemented here
    console.log('Dashboard visit', {
      timestamp: new Date().toISOString(),
      psychologicalState: psychologicalState
    });
  }, [psychologicalState]);

  // Load tender data
  useEffect(() => {
    loadTenderData();
  }, [psychologicalState]);

  const loadTenderData = async () => {
    try {
      // Mock tender summary data
      const mockSummary: DashboardSummary = {
        activeBids: 3,
        pendingTenders: 12,
        successRate: 68,
        totalEarnings: 2450000,
        bidsThisMonth: 5,
        bidsLastMonth: 3,
        successRateChange: 5.2,
        earningsChange: 12.8,
        urgentDeadlines: 2,
        missingDocuments: 3,
        complianceIssues: 1,
        psychologicalInsights: {
          currentMood: psychologicalState?.stressLevel > 0.7 ? 'stressed' : 
                      psychologicalState?.engagementLevel > 0.7 ? 'motivated' : 'focused',
          engagementTrend: 'increasing',
          optimalWorkingHours: ['09:00-11:00', '14:00-16:00'],
          stressFactors: ['Tight deadlines', 'Complex compliance requirements'],
          motivationFactors: ['Achievement recognition', 'Financial rewards'],
          recommendedActions: [
            'Take a 10-minute break to reduce stress',
            'Focus on high-value tenders first',
            'Complete compliance documents early'
          ]
        },
        currentLevel: 7,
        xpToNextLevel: 250,
        recentAchievements: ['Bid Master', 'Compliance Expert'],
        streakCount: 5,
        leaderboardPosition: 12,
        recentActivity: [],
        notifications: [],
        totalBidsSubmitted: 24,
        totalTendersViewed: 156,
        averageBidValue: 3200000,
        winRate: 68,
        averageResponseTime: 2.5
      };

      setTenderSummary(mockSummary);

      // Load personalized tenders
      const mockTenders: Tender[] = []; // Would be loaded from API
      const personalized = await behavioralService.getPersonalizedTenders(mockTenders, 3);
      setPersonalizedTenders(personalized);

    } catch (error) {
      console.error('Failed to load tender data:', error);
    }
  };

  // Psychological state adaptation
  useEffect(() => {
    if (psychologicalState?.stressLevel > 0.7) {
      setShowPsychologicalInsights(true);
    }
  }, [psychologicalState]);

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <LinearProgress sx={{ width: 200 }} />
      </Box>
    );
  }

  return (
    <Box sx={{ 
      p: 3, 
      minHeight: '100vh',
      backgroundColor: shouldUseCalming ? '#f8fafc' : 'background.default'
    }}>
      {/* Welcome Header with Psychological Adaptation */}
      <Box sx={{ mb: 3 }}>
        <Typography 
          variant={isHighCognitiveLoad ? "h3" : "h4"} 
          component="h1" 
          sx={{ 
            fontWeight: 500,
            color: isStressed ? 'success.main' : 'text.primary'
          }}
        >
          Welcome back! 👋
        </Typography>
        <Typography variant="body1" color="text.secondary">
          {tenderSummary?.psychologicalInsights.currentMood === 'stressed' 
            ? "Take it easy today - we've simplified your dashboard"
            : "Ready to discover new opportunities?"
          }
        </Typography>
      </Box>

      {/* Psychological Insights Alert */}
      <Fade in={showPsychologicalInsights}>
        <Alert 
          severity="info" 
          icon={<PsychologyIcon />}
          sx={{ mb: 3 }}
          action={
            <Button size="small" onClick={() => setShowPsychologicalInsights(false)}>
              Got it
            </Button>
          }
        >
          <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
            💡 Personalized Insight
          </Typography>
          {tenderSummary?.psychologicalInsights.recommendedActions[0]}
        </Alert>
      </Fade>

      <Grid container spacing={3}>
        {/* Core Metrics Row */}
        <Grid item xs={12}>
          <Grid container spacing={2}>
            {/* Existing BidBeez Metrics */}
            <Grid item xs={12} sm={6} md={3}>
              <Card elevation={2}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <PeopleIcon color="primary" sx={{ mr: 1 }} />
                    <Typography variant="h6">Active Bees</Typography>
                  </Box>
                  <Typography variant="h3" color="primary">
                    {dashboardStats?.activeBees || 8}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Currently working
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card elevation={2}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <AssignmentIcon color="info" sx={{ mr: 1 }} />
                    <Typography variant="h6">Active Tasks</Typography>
                  </Box>
                  <Typography variant="h3" color="info.main">
                    {dashboardStats?.activeTasks || 15}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    In progress
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* New Tender Metrics */}
            <Grid item xs={12} sm={6} md={3}>
              <Card elevation={2}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <SearchIcon color="success" sx={{ mr: 1 }} />
                    <Typography variant="h6">New Tenders</Typography>
                  </Box>
                  <Typography variant="h3" color="success.main">
                    {tenderSummary?.pendingTenders || 12}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Matching your profile
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card elevation={2}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <TrendingUpIcon color="warning" sx={{ mr: 1 }} />
                    <Typography variant="h6">Success Rate</Typography>
                  </Box>
                  <Typography variant="h3" color="warning.main">
                    {tenderSummary?.successRate || 68}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Last 12 months
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>

        {/* Main Content */}
        <Grid item xs={12} md={8}>
          {/* Quick Actions with Behavioral Optimization */}
          <Card elevation={2} sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                🎯 Recommended Actions
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<SwipeIcon />}
                    onClick={() => window.location.href = '/tenders/swipe'}
                    sx={{ 
                      py: 2,
                      background: 'linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%)'
                    }}
                  >
                    Discover Tenders
                  </Button>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<ComplianceIcon />}
                    onClick={() => window.location.href = '/compliance'}
                    sx={{ py: 2 }}
                  >
                    SA Compliance Tools
                  </Button>
                </Grid>

                {!needsSimplification && (
                  <>
                    <Grid item xs={12} sm={6}>
                      <Button
                        fullWidth
                        variant="outlined"
                        startIcon={<AssignmentIcon />}
                        onClick={() => window.location.href = '/bids/create'}
                        sx={{ py: 2 }}
                      >
                        Create New Bid
                      </Button>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Button
                        fullWidth
                        variant="outlined"
                        startIcon={<AIIcon />}
                        onClick={() => window.location.href = '/analytics'}
                        sx={{ py: 2 }}
                      >
                        AI Insights
                      </Button>
                    </Grid>
                  </>
                )}
              </Grid>
            </CardContent>
          </Card>

          {/* Existing Charts */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card elevation={2}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>Task Analytics</Typography>
                  <TaskAnalyticsChart data={[]} />
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card elevation={2}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>Payment Overview</Typography>
                  <PaymentChart data={[]} />
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          {/* Gamification Panel */}
          {tenderSummary && (
            <Card elevation={2} sx={{ mb: 3 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <AchievementIcon color="warning" sx={{ mr: 1 }} />
                  <Typography variant="h6">Your Progress</Typography>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Level {tenderSummary.currentLevel} • {tenderSummary.xpToNextLevel} XP to next level
                  </Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={75} 
                    sx={{ mt: 1, height: 8, borderRadius: 4 }}
                  />
                </Box>

                <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
                  <Chip 
                    icon={<StarIcon />}
                    label={`${tenderSummary.streakCount} day streak`}
                    size="small"
                    color="success"
                  />
                  <Chip 
                    label={`#${tenderSummary.leaderboardPosition} rank`}
                    size="small"
                    color="info"
                  />
                </Stack>

                <Typography variant="body2" sx={{ mb: 1 }}>Recent Achievements:</Typography>
                {tenderSummary.recentAchievements.map((achievement, index) => (
                  <Chip 
                    key={index}
                    label={achievement}
                    size="small"
                    sx={{ mr: 0.5, mb: 0.5 }}
                  />
                ))}
              </CardContent>
            </Card>
          )}

          {/* Notifications */}
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Recent Activity
              </Typography>
              
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <TimerIcon color="warning" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Tender deadline approaching"
                    secondary="IT Infrastructure bid due in 2 days"
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <AchievementIcon color="success" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Achievement unlocked!"
                    secondary="Compliance Expert badge earned"
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <NotificationsIcon color="info" />
                  </ListItemIcon>
                  <ListItemText
                    primary="New tender matches"
                    secondary="3 new opportunities in your area"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
