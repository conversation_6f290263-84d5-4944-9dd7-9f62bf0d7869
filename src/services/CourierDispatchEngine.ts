/**
 * Courier Dispatch Engine
 * Advanced delivery optimization system that integrates with Queen Bee Management
 * Handles document pickup/delivery for tender submissions with multiple delivery modes
 */

import QueenBeeManagementSystem, { BeeSpecialty, BeeType } from './QueenBeeManagementSystem';

export enum DeliveryMode {
  BEE_DIRECT = 'bee_direct',
  COURIER = 'courier',
  BEE_AIR_BEE = 'bee_air_bee',
  BEE_AIR_BEE_EXTENDED = 'bee_air_bee_extended',
  COURIER_PLUS_BEE = 'courier_plus_bee'
}

export enum DeliveryPriority {
  SPEED = 'speed',
  COST = 'cost',
  RELIABILITY = 'reliability'
}

export enum CourierRequestStatus {
  CREATED = 'created',
  ASSIGNED = 'assigned',
  PICKUP_SCHEDULED = 'pickup_scheduled',
  IN_TRANSIT = 'in_transit',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface CourierRequest {
  id: string;
  tenderId: string;
  userId: string;

  // Delivery Details
  pickupAddress: DeliveryAddress;
  deliveryAddress: DeliveryAddress;
  documentType: string;
  documentDescription: string;
  urgency: 'standard' | 'urgent' | 'critical';

  // Timing
  requestedPickupTime?: string;
  deadline: string;
  estimatedDeliveryTime?: string;
  actualPickupTime?: string;
  actualDeliveryTime?: string;

  // Delivery Mode Selection
  selectedMode: DeliveryMode;
  alternativeModes: DeliveryModeOption[];

  // Assignment
  assignedQueenBeeId?: string;
  assignedWorkerBeeId?: string;
  courierServiceId?: string;

  // Status and Tracking
  status: CourierRequestStatus;
  trackingHistory: TrackingEvent[];
  currentLocation?: LocationData;

  // Cost and Payment
  estimatedCost: number;
  actualCost?: number;
  currency: string;
  paymentStatus: 'pending' | 'paid' | 'failed';

  // Special Requirements
  specialInstructions?: string;
  requiresSignature: boolean;
  requiresPhoto: boolean;
  fragile: boolean;
  confidential: boolean;

  // Metadata
  createdAt: string;
  updatedAt: string;
}

export interface DeliveryAddress {
  street: string;
  city: string;
  province: string;
  postalCode: string;
  country: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  contactPerson: string;
  contactPhone: string;
  accessInstructions?: string;
  businessHours?: string;
}

export interface DeliveryModeOption {
  mode: DeliveryMode;
  estimatedTime: number; // hours
  estimatedCost: number;
  reliability: number; // 0-100
  description: string;
  requirements: string[];
  limitations: string[];
}

export interface TrackingEvent {
  id: string;
  timestamp: string;
  event: string;
  location?: LocationData;
  description: string;
  performedBy: string; // Bee ID or Courier Service
  attachments?: string[];
}

export interface LocationData {
  latitude: number;
  longitude: number;
  address: string;
  accuracy: number; // meters
  timestamp: string;
}

export interface BeeCapabilities {
  carryingCapacityKg: number;
  speedKmh: number;
  rangeKm: number;
  transportModes: string[];
  specialties: BeeSpecialty[];
  currentLocation: LocationData;
  availability: 'available' | 'busy' | 'offline';
}

export interface CourierService {
  id: string;
  name: string;
  serviceType: 'standard' | 'express' | 'overnight' | 'same_day';
  coverage: string[];
  baseCost: number;
  costPerKm: number;
  estimatedDeliveryTime: number; // hours
  reliability: number; // 0-100
  trackingSupported: boolean;
  signatureRequired: boolean;
}

export interface DeliveryEstimate {
  mode: DeliveryMode;
  estimatedTime: number; // hours
  estimatedCost: number;
  reliability: number;
  reason: string;
  requirements: string[];
  limitations: string[];
}

class CourierDispatchEngine {
  private static instance: CourierDispatchEngine;
  private queenBeeSystem: QueenBeeManagementSystem;
  private activeRequests: Map<string, CourierRequest> = new Map();
  private courierServices: Map<string, CourierService> = new Map();
  private beeCapabilities: Map<string, BeeCapabilities> = new Map();

  private constructor() {
    this.queenBeeSystem = QueenBeeManagementSystem.getInstance();
    this.initializeCourierServices();
    this.initializeBeeCapabilities();
  }

  public static getInstance(): CourierDispatchEngine {
    if (!CourierDispatchEngine.instance) {
      CourierDispatchEngine.instance = new CourierDispatchEngine();
    }
    return CourierDispatchEngine.instance;
  }

  /**
   * Main entry point: Create delivery request and find optimal delivery mode
   */
  public async createDeliveryRequest(
    tenderId: string,
    userId: string,
    pickupAddress: DeliveryAddress,
    deliveryAddress: DeliveryAddress,
    documentType: string,
    deadline: string,
    priority: DeliveryPriority = DeliveryPriority.SPEED,
    specialRequirements?: Partial<CourierRequest>
  ): Promise<CourierRequest> {
    console.log(`📦 Courier Engine: Creating delivery request for tender ${tenderId}`);

    // Calculate delivery estimates for all modes
    const deliveryEstimates = await this.calculateDeliveryEstimates(
      pickupAddress,
      deliveryAddress,
      deadline,
      documentType
    );

    // Select optimal delivery mode based on priority
    const selectedMode = this.selectOptimalDeliveryMode(deliveryEstimates, priority);

    // Create courier request
    const courierRequest: CourierRequest = {
      id: `COURIER-${Date.now()}`,
      tenderId,
      userId,
      pickupAddress,
      deliveryAddress,
      documentType,
      documentDescription: specialRequirements?.documentDescription || `Tender documents for ${tenderId}`,
      urgency: this.determineUrgency(deadline),
      deadline,
      selectedMode: selectedMode.mode,
      alternativeModes: deliveryEstimates.filter(est => est.mode !== selectedMode.mode),
      status: CourierRequestStatus.CREATED,
      trackingHistory: [{
        id: `TRACK-${Date.now()}`,
        timestamp: new Date().toISOString(),
        event: 'REQUEST_CREATED',
        description: `Delivery request created for ${documentType}`,
        performedBy: 'system'
      }],
      estimatedCost: selectedMode.estimatedCost,
      currency: 'ZAR',
      paymentStatus: 'pending',
      requiresSignature: specialRequirements?.requiresSignature ?? true,
      requiresPhoto: specialRequirements?.requiresPhoto ?? false,
      fragile: specialRequirements?.fragile ?? false,
      confidential: specialRequirements?.confidential ?? true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...specialRequirements
    };

    // Store request
    this.activeRequests.set(courierRequest.id, courierRequest);

    // Assign delivery based on selected mode
    await this.assignDelivery(courierRequest);

    console.log(`✅ Courier Request Created: ${courierRequest.id} using ${selectedMode.mode}`);
    return courierRequest;
  }

  /**
   * Calculate delivery estimates for all available modes
   */
  private async calculateDeliveryEstimates(
    pickupAddress: DeliveryAddress,
    deliveryAddress: DeliveryAddress,
    deadline: string,
    documentType: string
  ): Promise<DeliveryEstimate[]> {
    const estimates: DeliveryEstimate[] = [];
    const distance = this.calculateDistance(pickupAddress.coordinates, deliveryAddress.coordinates);
    const timeRemaining = this.getTimeRemaining(deadline);
    const conditions = this.getRealTimeConditions(pickupAddress, deliveryAddress);

    // Bee Direct Delivery
    const beeDirectEstimate = await this.estimateBeeDirectDelivery(
      pickupAddress, deliveryAddress, distance, conditions
    );
    if (beeDirectEstimate && beeDirectEstimate.estimatedTime <= timeRemaining) {
      estimates.push(beeDirectEstimate);
    }

    // Standard Courier
    const courierEstimate = this.estimateCourierDelivery(
      pickupAddress, deliveryAddress, distance, conditions
    );
    if (courierEstimate.estimatedTime <= timeRemaining) {
      estimates.push(courierEstimate);
    }

    // Bee-Air-Bee (if airports available)
    const beeAirBeeEstimate = await this.estimateBeeAirBeeDelivery(
      pickupAddress, deliveryAddress, distance, conditions
    );
    if (beeAirBeeEstimate && beeAirBeeEstimate.estimatedTime <= timeRemaining) {
      estimates.push(beeAirBeeEstimate);
    }

    // Courier + Bee (urban to rural)
    const courierPlusBeeEstimate = await this.estimateCourierPlusBeeDelivery(
      pickupAddress, deliveryAddress, distance, conditions
    );
    if (courierPlusBeeEstimate && courierPlusBeeEstimate.estimatedTime <= timeRemaining) {
      estimates.push(courierPlusBeeEstimate);
    }

    if (estimates.length === 0) {
      throw new Error('No delivery mode can meet the deadline');
    }

    return estimates;
  }

  /**
   * Select optimal delivery mode based on priority
   */
  private selectOptimalDeliveryMode(
    estimates: DeliveryEstimate[],
    priority: DeliveryPriority
  ): DeliveryEstimate {
    switch (priority) {
      case DeliveryPriority.SPEED:
        return estimates.reduce((fastest, current) =>
          current.estimatedTime < fastest.estimatedTime ? current : fastest
        );

      case DeliveryPriority.COST:
        return estimates.reduce((cheapest, current) =>
          current.estimatedCost < cheapest.estimatedCost ? current : cheapest
        );

      case DeliveryPriority.RELIABILITY:
        return estimates.reduce((mostReliable, current) =>
          current.reliability > mostReliable.reliability ? current : mostReliable
        );

      default:
        return estimates[0];
    }
  }

  /**
   * Assign delivery based on selected mode
   */
  private async assignDelivery(request: CourierRequest): Promise<void> {
    switch (request.selectedMode) {
      case DeliveryMode.BEE_DIRECT:
        await this.assignBeeDirectDelivery(request);
        break;

      case DeliveryMode.COURIER:
        await this.assignCourierDelivery(request);
        break;

      case DeliveryMode.BEE_AIR_BEE:
        await this.assignBeeAirBeeDelivery(request);
        break;

      case DeliveryMode.COURIER_PLUS_BEE:
        await this.assignCourierPlusBeeDelivery(request);
        break;

      default:
        throw new Error(`Unsupported delivery mode: ${request.selectedMode}`);
    }
  }

  /**
   * Assign bee direct delivery
   */
  private async assignBeeDirectDelivery(request: CourierRequest): Promise<void> {
    // Find suitable Queen Bee for pickup location
    const queenBeeTask = {
      tenderId: request.tenderId,
      clientId: request.userId,
      title: `Document Delivery: ${request.documentType}`,
      description: `Pickup from ${request.pickupAddress.city} and deliver to ${request.deliveryAddress.city}`,
      taskType: 'document_delivery' as any,
      priority: request.urgency as any,
      complexity: 'moderate' as any,
      deadline: request.deadline,
      location: {
        latitude: request.pickupAddress.coordinates.latitude,
        longitude: request.pickupAddress.coordinates.longitude,
        address: `${request.pickupAddress.street}, ${request.pickupAddress.city}`,
        city: request.pickupAddress.city,
        province: request.pickupAddress.province,
        lastUpdated: new Date().toISOString()
      },
      requirements: [],
      documentsRequired: [],
      skillsRequired: [BeeSpecialty.DOCUMENT_PROCESSING],
      payment: request.estimatedCost
    };

    try {
      const assignedTask = await this.queenBeeSystem.assignTaskToQueenBee(queenBeeTask);
      request.assignedQueenBeeId = assignedTask.assignedQueenBeeId;
      request.status = CourierRequestStatus.ASSIGNED;

      this.addTrackingEvent(request, 'ASSIGNED_TO_QUEEN_BEE',
        `Assigned to Queen Bee for direct delivery`);
    } catch (error) {
      request.status = CourierRequestStatus.FAILED;
      this.addTrackingEvent(request, 'ASSIGNMENT_FAILED',
        `Failed to assign to Queen Bee: ${error}`);
    }
  }

  /**
   * Assign standard courier delivery
   */
  private async assignCourierDelivery(request: CourierRequest): Promise<void> {
    // Find best courier service
    const suitableCourier = this.findBestCourierService(request);

    if (suitableCourier) {
      request.courierServiceId = suitableCourier.id;
      request.status = CourierRequestStatus.ASSIGNED;

      this.addTrackingEvent(request, 'ASSIGNED_TO_COURIER',
        `Assigned to ${suitableCourier.name} courier service`);
    } else {
      request.status = CourierRequestStatus.FAILED;
      this.addTrackingEvent(request, 'ASSIGNMENT_FAILED',
        'No suitable courier service available');
    }
  }

  /**
   * Add tracking event to request
   */
  private addTrackingEvent(
    request: CourierRequest,
    event: string,
    description: string,
    location?: LocationData
  ): void {
    const trackingEvent: TrackingEvent = {
      id: `TRACK-${Date.now()}`,
      timestamp: new Date().toISOString(),
      event,
      description,
      performedBy: 'system',
      location
    };

    request.trackingHistory.push(trackingEvent);
    request.updatedAt = new Date().toISOString();
  }

  // Helper methods for calculations and estimations
  private calculateDistance(coord1: {latitude: number, longitude: number}, coord2: {latitude: number, longitude: number}): number {
    const R = 6371; // Earth's radius in km
    const dLat = this.toRad(coord2.latitude - coord1.latitude);
    const dLon = this.toRad(coord2.longitude - coord1.longitude);
    const lat1 = this.toRad(coord1.latitude);
    const lat2 = this.toRad(coord2.latitude);

    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.sin(dLon/2) * Math.sin(dLon/2) * Math.cos(lat1) * Math.cos(lat2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  private toRad(value: number): number {
    return value * Math.PI / 180;
  }

  private getTimeRemaining(deadline: string): number {
    const deadlineTime = new Date(deadline).getTime();
    const currentTime = new Date().getTime();
    return (deadlineTime - currentTime) / (1000 * 60 * 60); // hours
  }

  private determineUrgency(deadline: string): 'standard' | 'urgent' | 'critical' {
    const hoursRemaining = this.getTimeRemaining(deadline);
    if (hoursRemaining <= 4) return 'critical';
    if (hoursRemaining <= 24) return 'urgent';
    return 'standard';
  }

  private getRealTimeConditions(pickup: DeliveryAddress, delivery: DeliveryAddress): any {
    // Simulate real-time conditions
    const hour = new Date().getHours();
    return {
      traffic: hour >= 7 && hour <= 9 || hour >= 17 && hour <= 19 ? 'heavy' : 'light',
      weather: 'clear',
      roadConditions: 'good'
    };
  }

  private async estimateBeeDirectDelivery(
    pickup: DeliveryAddress,
    delivery: DeliveryAddress,
    distance: number,
    conditions: any
  ): Promise<DeliveryEstimate | null> {
    // Check if bees are available in pickup location
    const availableBees = this.getAvailableBeesInLocation(pickup.city);
    if (availableBees.length === 0) return null;

    const estimatedTime = (distance / 45) + 2; // 45 km/h average + 2h buffer
    const estimatedCost = 50 + (distance * 2.5); // Base cost + per km

    return {
      mode: DeliveryMode.BEE_DIRECT,
      estimatedTime,
      estimatedCost,
      reliability: 85,
      reason: 'Direct bee delivery - fast and reliable',
      requirements: ['Available bee in pickup location'],
      limitations: ['Weather dependent', 'Limited to bee range']
    };
  }

  private estimateCourierDelivery(
    pickup: DeliveryAddress,
    delivery: DeliveryAddress,
    distance: number,
    conditions: any
  ): DeliveryEstimate {
    const estimatedTime = 24 + (distance / 100); // Overnight + travel time
    const estimatedCost = 150 + (distance * 0.8);

    return {
      mode: DeliveryMode.COURIER,
      estimatedTime,
      estimatedCost,
      reliability: 95,
      reason: 'Standard courier service - most reliable',
      requirements: ['Courier service coverage'],
      limitations: ['Slower than bee delivery']
    };
  }

  private async estimateBeeAirBeeDelivery(
    pickup: DeliveryAddress,
    delivery: DeliveryAddress,
    distance: number,
    conditions: any
  ): Promise<DeliveryEstimate | null> {
    // Check if airports are available
    if (!this.hasAirport(pickup.city) || !this.hasAirport(delivery.city)) {
      return null;
    }

    const estimatedTime = 6 + (distance / 500); // Airport processing + flight time
    const estimatedCost = 300 + (distance * 0.5);

    return {
      mode: DeliveryMode.BEE_AIR_BEE,
      estimatedTime,
      estimatedCost,
      reliability: 90,
      reason: 'Air delivery - fastest for long distances',
      requirements: ['Airports in both cities', 'Available bees'],
      limitations: ['Weather dependent', 'Higher cost']
    };
  }

  private async estimateCourierPlusBeeDelivery(
    pickup: DeliveryAddress,
    delivery: DeliveryAddress,
    distance: number,
    conditions: any
  ): Promise<DeliveryEstimate | null> {
    // Check if this is urban to rural delivery
    if (!this.isUrban(pickup.city) || !this.isRural(delivery.city)) {
      return null;
    }

    const estimatedTime = 30 + (distance / 80); // Courier to urban center + bee to rural
    const estimatedCost = 200 + (distance * 1.2);

    return {
      mode: DeliveryMode.COURIER_PLUS_BEE,
      estimatedTime,
      estimatedCost,
      reliability: 88,
      reason: 'Hybrid delivery - optimal for urban to rural',
      requirements: ['Urban pickup', 'Rural delivery', 'Available bees'],
      limitations: ['Longer delivery time', 'Multiple handoffs']
    };
  }

  private getAvailableBeesInLocation(city: string): string[] {
    // Mock implementation - would integrate with actual bee tracking
    const beesByCity: {[key: string]: string[]} = {
      'Cape Town': ['bee-001', 'bee-002'],
      'Johannesburg': ['bee-003', 'bee-004'],
      'Durban': ['bee-005'],
      'Port Elizabeth': ['bee-006']
    };
    return beesByCity[city] || [];
  }

  private hasAirport(city: string): boolean {
    const airportCities = ['Cape Town', 'Johannesburg', 'Durban', 'Port Elizabeth', 'East London'];
    return airportCities.includes(city);
  }

  private isUrban(city: string): boolean {
    const urbanCities = ['Cape Town', 'Johannesburg', 'Durban', 'Port Elizabeth', 'East London', 'Pretoria'];
    return urbanCities.includes(city);
  }

  private isRural(city: string): boolean {
    return !this.isUrban(city);
  }

  private findBestCourierService(request: CourierRequest): CourierService | null {
    const availableServices = Array.from(this.courierServices.values()).filter(service =>
      service.coverage.includes(request.pickupAddress.province) &&
      service.coverage.includes(request.deliveryAddress.province)
    );

    if (availableServices.length === 0) return null;

    // Return service with best reliability
    return availableServices.reduce((best, current) =>
      current.reliability > best.reliability ? current : best
    );
  }

  private initializeCourierServices(): void {
    const services: CourierService[] = [
      {
        id: 'courier-001',
        name: 'FastTrack Express',
        serviceType: 'express',
        coverage: ['Western Cape', 'Gauteng', 'KwaZulu-Natal'],
        baseCost: 120,
        costPerKm: 0.8,
        estimatedDeliveryTime: 24,
        reliability: 95,
        trackingSupported: true,
        signatureRequired: true
      },
      {
        id: 'courier-002',
        name: 'SA Courier Network',
        serviceType: 'standard',
        coverage: ['All Provinces'],
        baseCost: 80,
        costPerKm: 0.6,
        estimatedDeliveryTime: 48,
        reliability: 90,
        trackingSupported: true,
        signatureRequired: true
      }
    ];

    services.forEach(service => {
      this.courierServices.set(service.id, service);
    });
  }

  private initializeBeeCapabilities(): void {
    // Initialize with sample bee capabilities
    const capabilities: BeeCapabilities[] = [
      {
        carryingCapacityKg: 2,
        speedKmh: 45,
        rangeKm: 100,
        transportModes: ['own_vehicle', 'ehailing'],
        specialties: [BeeSpecialty.DOCUMENT_PROCESSING],
        currentLocation: {
          latitude: -33.9249,
          longitude: 18.4241,
          address: 'Cape Town',
          accuracy: 10,
          timestamp: new Date().toISOString()
        },
        availability: 'available'
      }
    ];

    capabilities.forEach((cap, index) => {
      this.beeCapabilities.set(`bee-${String(index + 1).padStart(3, '0')}`, cap);
    });
  }

  // Public methods for tracking and management
  public getDeliveryRequest(requestId: string): CourierRequest | undefined {
    return this.activeRequests.get(requestId);
  }

  public getAllActiveRequests(): CourierRequest[] {
    return Array.from(this.activeRequests.values());
  }

  public updateRequestStatus(requestId: string, status: CourierRequestStatus, description: string): boolean {
    const request = this.activeRequests.get(requestId);
    if (!request) return false;

    request.status = status;
    this.addTrackingEvent(request, status.toUpperCase(), description);
    return true;
  }

  public updateRequestLocation(requestId: string, location: LocationData): boolean {
    const request = this.activeRequests.get(requestId);
    if (!request) return false;

    request.currentLocation = location;
    this.addTrackingEvent(request, 'LOCATION_UPDATE',
      `Location updated: ${location.address}`, location);
    return true;
  }
}

export default CourierDispatchEngine;