/**
 * Psychological Dashboard API Service
 * Handles all psychological profiling, archetype detection, and behavioral data
 */

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { 
  BidderArchetype, 
  ArchetypeProfile, 
  UserBehaviorData 
} from '../ArchetypeDetectionService';

// Types for psychological dashboard data
export interface PsychologicalDashboardData {
  // Core metrics
  profitPotentialToday: number;
  hotOpportunities: number;
  successPrediction: number;
  currentRanking: number;
  totalBidders: number;
  streakCount: number;
  confidenceLevel: number;
  stressLevel: number;
  energyLevel: number;
  
  // Competitive intelligence
  competitorActivity: {
    [key: string]: {
      position: number;
      change: number;
      threat: 'low' | 'medium' | 'high' | 'critical';
    };
  };
  
  // Urgent opportunities
  urgentDeadlines: Array<{
    tender: string;
    value: number;
    timeLeft: string;
    successRate: number;
  }>;
  
  // Achievements
  achievements: Array<{
    id: string;
    name: string;
    progress: number;
    description: string;
  }>;
  
  // Live feed data
  marketFeedItems: Array<{
    id: string;
    type: 'win' | 'new_tender' | 'deadline' | 'competitor' | 'achievement' | 'market_move' | 'insight';
    title: string;
    subtitle?: string;
    value?: string;
    urgency: 'low' | 'medium' | 'high' | 'critical';
    timestamp: string;
    psychologicalTrigger: 'greed' | 'fomo' | 'competition' | 'achievement' | 'social_proof' | 'scarcity';
  }>;
}

export interface CompetitorData {
  id: string;
  name: string;
  avatar?: string;
  currentRanking: number;
  previousRanking: number;
  totalEarnings: number;
  successRate: number;
  activeBids: number;
  recentActivity: string;
  threatLevel: 'low' | 'medium' | 'high' | 'critical';
  specialization: string;
  lastSeen: string;
  isRival?: boolean;
  isTarget?: boolean;
}

export interface UserRankingData {
  currentPosition: number;
  previousPosition: number;
  totalCompetitors: number;
  pointsToNext: number;
  pointsFromPrevious: number;
  category: string;
  percentile: number;
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
}

// Base query configuration
const baseQuery = fetchBaseQuery({
  baseUrl: process.env.REACT_APP_API_URL || 'http://localhost:3000/api',
  prepareHeaders: (headers, { getState }) => {
    const token = localStorage.getItem('token');
    if (token) {
      headers.set('authorization', `Bearer ${token}`);
    }
    headers.set('Content-Type', 'application/json');
    return headers;
  },
});

export const psychologicalApi = createApi({
  reducerPath: 'psychologicalApi',
  baseQuery,
  tagTypes: [
    'PsychologicalDashboard',
    'ArchetypeProfile',
    'BehaviorData',
    'CompetitorData',
    'UserRanking',
    'MarketFeed'
  ],
  endpoints: (builder) => ({
    
    // ===== PSYCHOLOGICAL DASHBOARD =====
    
    getPsychologicalDashboard: builder.query<APIResponse<PsychologicalDashboardData>, string>({
      query: (userId) => `/psychological/dashboard/${userId}`,
      providesTags: ['PsychologicalDashboard'],
      // Mock data for development
      queryFn: async (userId) => {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        return {
          data: {
            success: true,
            data: {
              profitPotentialToday: 32800000,
              hotOpportunities: 5,
              successPrediction: 78,
              currentRanking: 23,
              totalBidders: 1247,
              streakCount: 4,
              confidenceLevel: 85,
              stressLevel: 23,
              energyLevel: 94,
              competitorActivity: {
                buildCorp: { position: 22, change: 1, threat: 'high' },
                techSolutions: { position: 24, change: -2, threat: 'low' },
                megaBuild: { position: 1, change: 0, threat: 'critical' }
              },
              urgentDeadlines: [
                { tender: 'IT Infrastructure Upgrade', value: 8400000, timeLeft: '18h 32m', successRate: 92 },
                { tender: 'Road Maintenance Contract', value: 5200000, timeLeft: '4h 12m', successRate: 87 },
                { tender: 'School Security System', value: 15600000, timeLeft: '2d 14h', successRate: 94 }
              ],
              achievements: [
                { id: 'tech_master', name: 'Tech Master', progress: 75, description: '1 more IT win needed' },
                { id: 'gov_specialist', name: 'Government Specialist', progress: 40, description: '3 more gov wins needed' }
              ],
              marketFeedItems: [
                {
                  id: '1',
                  type: 'win',
                  title: 'MegaCorp just SNATCHED R12.8M infrastructure deal',
                  subtitle: 'Could have been yours!',
                  value: 'R12.8M',
                  urgency: 'high',
                  timestamp: new Date().toISOString(),
                  psychologicalTrigger: 'fomo'
                },
                {
                  id: '2',
                  type: 'new_tender',
                  title: 'R8.4M road tender LIVE NOW',
                  subtitle: '73% success rate for your profile',
                  value: 'R8.4M',
                  urgency: 'critical',
                  timestamp: new Date().toISOString(),
                  psychologicalTrigger: 'greed'
                }
              ]
            },
            timestamp: new Date().toISOString()
          }
        };
      }
    }),
    
    // ===== ARCHETYPE DETECTION =====
    
    getArchetypeProfile: builder.query<APIResponse<ArchetypeProfile>, string>({
      query: (userId) => `/psychological/archetype/${userId}`,
      providesTags: ['ArchetypeProfile']
    }),
    
    updateBehaviorData: builder.mutation<APIResponse<ArchetypeProfile>, {
      userId: string;
      behaviorData: Partial<UserBehaviorData>;
    }>({
      query: ({ userId, behaviorData }) => ({
        url: `/psychological/behavior/${userId}`,
        method: 'POST',
        body: behaviorData
      }),
      invalidatesTags: ['ArchetypeProfile', 'BehaviorData']
    }),
    
    detectArchetype: builder.mutation<APIResponse<ArchetypeProfile>, {
      userId: string;
      behaviorData: UserBehaviorData;
    }>({
      query: ({ userId, behaviorData }) => ({
        url: `/psychological/archetype/detect/${userId}`,
        method: 'POST',
        body: behaviorData
      }),
      invalidatesTags: ['ArchetypeProfile']
    }),
    
    // ===== COMPETITIVE INTELLIGENCE =====
    
    getCompetitorData: builder.query<APIResponse<CompetitorData[]>, {
      userId: string;
      category?: string;
      limit?: number;
    }>({
      query: ({ userId, category, limit = 10 }) => ({
        url: `/psychological/competitors/${userId}`,
        params: { category, limit }
      }),
      providesTags: ['CompetitorData'],
      // Mock data for development
      queryFn: async ({ userId, category, limit = 10 }) => {
        await new Promise(resolve => setTimeout(resolve, 300));
        
        return {
          data: {
            success: true,
            data: [
              {
                id: 'buildcorp',
                name: 'BuildCorp',
                currentRanking: 22,
                previousRanking: 23,
                totalEarnings: 8900000,
                successRate: 71,
                activeBids: 4,
                recentActivity: 'Viewed IT Infrastructure tender',
                threatLevel: 'high',
                specialization: 'Infrastructure',
                lastSeen: '2 minutes ago',
                isRival: true
              },
              {
                id: 'techsolutions',
                name: 'TechSolutions',
                currentRanking: 24,
                previousRanking: 22,
                totalEarnings: 8100000,
                successRate: 65,
                activeBids: 2,
                recentActivity: 'Submitted security bid',
                threatLevel: 'medium',
                specialization: 'Technology',
                lastSeen: '15 minutes ago'
              },
              {
                id: 'megabuild',
                name: 'MegaBuild',
                currentRanking: 1,
                previousRanking: 1,
                totalEarnings: 45200000,
                successRate: 94,
                activeBids: 8,
                recentActivity: 'Won R12.8M contract',
                threatLevel: 'critical',
                specialization: 'Large Infrastructure',
                lastSeen: '1 hour ago',
                isTarget: true
              }
            ],
            timestamp: new Date().toISOString()
          }
        };
      }
    }),
    
    getUserRanking: builder.query<APIResponse<UserRankingData>, string>({
      query: (userId) => `/psychological/ranking/${userId}`,
      providesTags: ['UserRanking'],
      // Mock data for development
      queryFn: async (userId) => {
        await new Promise(resolve => setTimeout(resolve, 200));
        
        return {
          data: {
            success: true,
            data: {
              currentPosition: 23,
              previousPosition: 24,
              totalCompetitors: 1247,
              pointsToNext: 250,
              pointsFromPrevious: 180,
              category: 'Construction',
              percentile: 98
            },
            timestamp: new Date().toISOString()
          }
        };
      }
    }),
    
    // ===== MARKET FEED =====
    
    getMarketFeed: builder.query<APIResponse<any[]>, {
      userId: string;
      archetype?: BidderArchetype;
      limit?: number;
    }>({
      query: ({ userId, archetype, limit = 20 }) => ({
        url: `/psychological/market-feed/${userId}`,
        params: { archetype, limit }
      }),
      providesTags: ['MarketFeed']
    }),
    
    // ===== PERSONALIZED RECOMMENDATIONS =====
    
    getPersonalizedRecommendations: builder.query<APIResponse<string[]>, {
      userId: string;
      archetype: BidderArchetype;
    }>({
      query: ({ userId, archetype }) => ({
        url: `/psychological/recommendations/${userId}`,
        params: { archetype }
      })
    }),
    
    // ===== PSYCHOLOGICAL INSIGHTS =====
    
    getPsychologicalInsights: builder.query<APIResponse<{
      currentMood: string;
      stressLevel: number;
      motivationFactors: string[];
      optimalConditions: string[];
      recommendations: string[];
    }>, string>({
      query: (userId) => `/psychological/insights/${userId}`
    }),
    
    // ===== REAL-TIME UPDATES =====
    
    subscribeToUpdates: builder.mutation<void, {
      userId: string;
      subscriptionTypes: string[];
    }>({
      query: ({ userId, subscriptionTypes }) => ({
        url: `/psychological/subscribe/${userId}`,
        method: 'POST',
        body: { subscriptionTypes }
      })
    })
  })
});

// Export hooks for use in components
export const {
  useGetPsychologicalDashboardQuery,
  useGetArchetypeProfileQuery,
  useUpdateBehaviorDataMutation,
  useDetectArchetypeMutation,
  useGetCompetitorDataQuery,
  useGetUserRankingQuery,
  useGetMarketFeedQuery,
  useGetPersonalizedRecommendationsQuery,
  useGetPsychologicalInsightsQuery,
  useSubscribeToUpdatesMutation
} = psychologicalApi;

export default psychologicalApi;
