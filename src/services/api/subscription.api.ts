/**
 * Subscription and Billing API Service
 * Handles all subscription management, payment processing, and billing operations
 */

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import {
  SubscriptionPlan,
  UserSubscription,
  PaymentMethod,
  Invoice,
  UsageBilling,
  RevenueMetrics,
  CreateSubscriptionRequest,
  UpdateSubscriptionRequest,
  CreatePaymentMethodRequest,
  ProcessPaymentRequest,
  FeatureGate,
  FeatureUsage,
  SubscriptionTier,
  UsageMetrics,
  PricingConfig,
  APIResponse,
  PaginatedResponse
} from '../../types/subscription';

// Base query configuration
const baseQuery = fetchBaseQuery({
  baseUrl: process.env.REACT_APP_API_URL || 'http://localhost:3000/api',
  prepareHeaders: (headers, { getState }) => {
    const token = localStorage.getItem('token');
    if (token) {
      headers.set('authorization', `Bearer ${token}`);
    }
    headers.set('Content-Type', 'application/json');
    return headers;
  },
});

export const subscriptionApi = createApi({
  reducerPath: 'subscriptionApi',
  baseQuery,
  tagTypes: [
    'Subscription',
    'PaymentMethod',
    'Invoice',
    'Usage',
    'Plan',
    'Revenue'
  ],
  endpoints: (builder) => ({
    
    // ===== SUBSCRIPTION PLANS =====
    
    getSubscriptionPlans: builder.query<SubscriptionPlan[], void>({
      query: () => '/billing/plans',
      providesTags: ['Plan']
    }),
    
    getSubscriptionPlan: builder.query<SubscriptionPlan, string>({
      query: (planId) => `/billing/plans/${planId}`,
      providesTags: (result, error, planId) => [{ type: 'Plan', id: planId }]
    }),
    
    getPricingConfig: builder.query<PricingConfig, void>({
      query: () => '/billing/pricing',
      providesTags: ['Plan']
    }),
    
    // ===== USER SUBSCRIPTION MANAGEMENT =====
    
    getUserSubscription: builder.query<UserSubscription, string>({
      query: (userId) => `/billing/subscriptions/user/${userId}`,
      providesTags: (result, error, userId) => [{ type: 'Subscription', id: userId }]
    }),
    
    createSubscription: builder.mutation<APIResponse<UserSubscription>, CreateSubscriptionRequest>({
      query: (subscriptionData) => ({
        url: '/billing/subscriptions',
        method: 'POST',
        body: subscriptionData
      }),
      invalidatesTags: ['Subscription', 'Usage']
    }),
    
    updateSubscription: builder.mutation<APIResponse<UserSubscription>, {
      subscriptionId: string;
      updates: UpdateSubscriptionRequest;
    }>({
      query: ({ subscriptionId, updates }) => ({
        url: `/billing/subscriptions/${subscriptionId}`,
        method: 'PUT',
        body: updates
      }),
      invalidatesTags: (result, error, { subscriptionId }) => [
        { type: 'Subscription', id: subscriptionId },
        'Usage'
      ]
    }),
    
    cancelSubscription: builder.mutation<APIResponse<UserSubscription>, {
      subscriptionId: string;
      cancelAtPeriodEnd?: boolean;
      reason?: string;
    }>({
      query: ({ subscriptionId, cancelAtPeriodEnd = true, reason }) => ({
        url: `/billing/subscriptions/${subscriptionId}/cancel`,
        method: 'POST',
        body: { cancelAtPeriodEnd, reason }
      }),
      invalidatesTags: (result, error, { subscriptionId }) => [
        { type: 'Subscription', id: subscriptionId }
      ]
    }),
    
    reactivateSubscription: builder.mutation<APIResponse<UserSubscription>, string>({
      query: (subscriptionId) => ({
        url: `/billing/subscriptions/${subscriptionId}/reactivate`,
        method: 'POST'
      }),
      invalidatesTags: (result, error, subscriptionId) => [
        { type: 'Subscription', id: subscriptionId }
      ]
    }),
    
    // ===== PAYMENT METHODS =====
    
    getPaymentMethods: builder.query<PaymentMethod[], string>({
      query: (userId) => `/billing/payment-methods/user/${userId}`,
      providesTags: ['PaymentMethod']
    }),
    
    createPaymentMethod: builder.mutation<APIResponse<PaymentMethod>, CreatePaymentMethodRequest>({
      query: (paymentMethodData) => ({
        url: '/billing/payment-methods',
        method: 'POST',
        body: paymentMethodData
      }),
      invalidatesTags: ['PaymentMethod']
    }),
    
    updatePaymentMethod: builder.mutation<APIResponse<PaymentMethod>, {
      paymentMethodId: string;
      updates: Partial<PaymentMethod>;
    }>({
      query: ({ paymentMethodId, updates }) => ({
        url: `/billing/payment-methods/${paymentMethodId}`,
        method: 'PUT',
        body: updates
      }),
      invalidatesTags: ['PaymentMethod']
    }),
    
    deletePaymentMethod: builder.mutation<APIResponse<void>, string>({
      query: (paymentMethodId) => ({
        url: `/billing/payment-methods/${paymentMethodId}`,
        method: 'DELETE'
      }),
      invalidatesTags: ['PaymentMethod']
    }),
    
    setDefaultPaymentMethod: builder.mutation<APIResponse<PaymentMethod>, string>({
      query: (paymentMethodId) => ({
        url: `/billing/payment-methods/${paymentMethodId}/set-default`,
        method: 'POST'
      }),
      invalidatesTags: ['PaymentMethod']
    }),
    
    // ===== BILLING AND INVOICES =====
    
    getInvoices: builder.query<PaginatedResponse<Invoice>, {
      userId: string;
      page?: number;
      pageSize?: number;
      status?: string;
    }>({
      query: ({ userId, page = 1, pageSize = 10, status }) => ({
        url: `/billing/invoices/user/${userId}`,
        params: { page, pageSize, status }
      }),
      providesTags: ['Invoice']
    }),
    
    getInvoice: builder.query<Invoice, string>({
      query: (invoiceId) => `/billing/invoices/${invoiceId}`,
      providesTags: (result, error, invoiceId) => [{ type: 'Invoice', id: invoiceId }]
    }),
    
    downloadInvoice: builder.mutation<Blob, string>({
      query: (invoiceId) => ({
        url: `/billing/invoices/${invoiceId}/download`,
        method: 'GET',
        responseHandler: (response) => response.blob()
      })
    }),
    
    payInvoice: builder.mutation<APIResponse<Invoice>, {
      invoiceId: string;
      paymentMethodId: string;
    }>({
      query: ({ invoiceId, paymentMethodId }) => ({
        url: `/billing/invoices/${invoiceId}/pay`,
        method: 'POST',
        body: { paymentMethodId }
      }),
      invalidatesTags: (result, error, { invoiceId }) => [
        { type: 'Invoice', id: invoiceId },
        'Subscription'
      ]
    }),
    
    // ===== USAGE TRACKING =====
    
    getUsageMetrics: builder.query<UsageMetrics, {
      userId: string;
      period?: 'current' | 'last_month' | 'last_year';
    }>({
      query: ({ userId, period = 'current' }) => ({
        url: `/billing/usage/${userId}`,
        params: { period }
      }),
      providesTags: ['Usage']
    }),
    
    getUsageBilling: builder.query<UsageBilling, {
      userId: string;
      month: string;
    }>({
      query: ({ userId, month }) => ({
        url: `/billing/usage/${userId}/billing`,
        params: { month }
      }),
      providesTags: ['Usage']
    }),
    
    recordUsage: builder.mutation<APIResponse<void>, {
      userId: string;
      usageType: string;
      quantity: number;
      metadata?: any;
    }>({
      query: (usageData) => ({
        url: '/billing/usage/record',
        method: 'POST',
        body: usageData
      }),
      invalidatesTags: ['Usage']
    }),
    
    // ===== FEATURE GATES =====
    
    checkFeatureAccess: builder.query<FeatureGate, {
      userId: string;
      feature: string;
    }>({
      query: ({ userId, feature }) => ({
        url: `/billing/features/check/${userId}/${feature}`
      })
    }),
    
    getFeatureUsage: builder.query<FeatureUsage[], string>({
      query: (userId) => `/billing/features/usage/${userId}`,
      providesTags: ['Usage']
    }),
    
    // ===== PAYMENT PROCESSING =====
    
    processPayment: builder.mutation<APIResponse<any>, ProcessPaymentRequest>({
      query: (paymentData) => ({
        url: '/billing/payments/process',
        method: 'POST',
        body: paymentData
      }),
      invalidatesTags: ['Invoice', 'Subscription']
    }),
    
    createPaymentIntent: builder.mutation<APIResponse<any>, {
      amount: number;
      currency: string;
      paymentMethodId: string;
      description: string;
    }>({
      query: (intentData) => ({
        url: '/billing/payments/intent',
        method: 'POST',
        body: intentData
      })
    }),
    
    confirmPaymentIntent: builder.mutation<APIResponse<any>, {
      paymentIntentId: string;
      paymentMethodId: string;
    }>({
      query: (confirmData) => ({
        url: '/billing/payments/confirm',
        method: 'POST',
        body: confirmData
      }),
      invalidatesTags: ['Invoice', 'Subscription']
    }),
    
    // ===== PROMOTIONS AND DISCOUNTS =====
    
    validatePromotionCode: builder.mutation<APIResponse<{
      valid: boolean;
      promotion: any;
      discountAmount: number;
    }>, {
      code: string;
      planId: string;
    }>({
      query: (promoData) => ({
        url: '/billing/promotions/validate',
        method: 'POST',
        body: promoData
      })
    }),
    
    applyPromotionCode: builder.mutation<APIResponse<UserSubscription>, {
      subscriptionId: string;
      code: string;
    }>({
      query: ({ subscriptionId, code }) => ({
        url: `/billing/subscriptions/${subscriptionId}/apply-promotion`,
        method: 'POST',
        body: { code }
      }),
      invalidatesTags: (result, error, { subscriptionId }) => [
        { type: 'Subscription', id: subscriptionId }
      ]
    }),
    
    // ===== REVENUE ANALYTICS (Admin) =====
    
    getRevenueMetrics: builder.query<RevenueMetrics, {
      period: 'month' | 'quarter' | 'year';
      startDate?: string;
      endDate?: string;
    }>({
      query: (params) => ({
        url: '/billing/analytics/revenue',
        params
      }),
      providesTags: ['Revenue']
    }),
    
    getSubscriptionAnalytics: builder.query<any, {
      period: 'month' | 'quarter' | 'year';
    }>({
      query: (params) => ({
        url: '/billing/analytics/subscriptions',
        params
      }),
      providesTags: ['Revenue']
    }),
    
    // ===== COMPLIANCE BILLING INTEGRATION =====
    
    getComplianceUsage: builder.query<any, {
      userId: string;
      period?: string;
    }>({
      query: ({ userId, period = 'current' }) => ({
        url: `/billing/compliance/usage/${userId}`,
        params: { period }
      }),
      providesTags: ['Usage']
    }),
    
    upgradeForCompliance: builder.mutation<APIResponse<UserSubscription>, {
      userId: string;
      complianceFeatures: string[];
    }>({
      query: (upgradeData) => ({
        url: '/billing/compliance/upgrade',
        method: 'POST',
        body: upgradeData
      }),
      invalidatesTags: ['Subscription', 'Usage']
    }),
    
    // ===== ECOSYSTEM BILLING =====
    
    getEcosystemUsage: builder.query<any, {
      userId: string;
      service: 'skillsync' | 'toolsync' | 'contractorsync' | 'suppliersync';
      period?: string;
    }>({
      query: ({ userId, service, period = 'current' }) => ({
        url: `/billing/ecosystem/${service}/usage/${userId}`,
        params: { period }
      }),
      providesTags: ['Usage']
    }),
    
    processEcosystemPayment: builder.mutation<APIResponse<any>, {
      userId: string;
      service: string;
      amount: number;
      description: string;
      metadata?: any;
    }>({
      query: (paymentData) => ({
        url: '/billing/ecosystem/payment',
        method: 'POST',
        body: paymentData
      }),
      invalidatesTags: ['Usage', 'Invoice']
    })
  })
});

// Export hooks for use in components
export const {
  // Subscription Plans
  useGetSubscriptionPlansQuery,
  useGetSubscriptionPlanQuery,
  useGetPricingConfigQuery,
  
  // User Subscriptions
  useGetUserSubscriptionQuery,
  useCreateSubscriptionMutation,
  useUpdateSubscriptionMutation,
  useCancelSubscriptionMutation,
  useReactivateSubscriptionMutation,
  
  // Payment Methods
  useGetPaymentMethodsQuery,
  useCreatePaymentMethodMutation,
  useUpdatePaymentMethodMutation,
  useDeletePaymentMethodMutation,
  useSetDefaultPaymentMethodMutation,
  
  // Billing and Invoices
  useGetInvoicesQuery,
  useGetInvoiceQuery,
  useDownloadInvoiceMutation,
  usePayInvoiceMutation,
  
  // Usage Tracking
  useGetUsageMetricsQuery,
  useGetUsageBillingQuery,
  useRecordUsageMutation,
  
  // Feature Gates
  useCheckFeatureAccessQuery,
  useGetFeatureUsageQuery,
  
  // Payment Processing
  useProcessPaymentMutation,
  useCreatePaymentIntentMutation,
  useConfirmPaymentIntentMutation,
  
  // Promotions
  useValidatePromotionCodeMutation,
  useApplyPromotionCodeMutation,
  
  // Analytics
  useGetRevenueMetricsQuery,
  useGetSubscriptionAnalyticsQuery,
  
  // Compliance Billing
  useGetComplianceUsageQuery,
  useUpgradeForComplianceMutation,
  
  // Ecosystem Billing
  useGetEcosystemUsageQuery,
  useProcessEcosystemPaymentMutation
} = subscriptionApi;
