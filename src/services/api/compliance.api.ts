/**
 * SA Compliance Tool API Service
 * Handles all compliance-related API calls including bid protests, template generation, and SME analysis
 */

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import {
  BidProtest,
  CreateProtestRequest,
  ProtestViabilityRequest,
  ProtestViabilityAnalysis,
  GenerateDocumentRequest,
  ComplianceDocument,
  IrregularityDetectionRequest,
  ComplianceIssue,
  ProtestDeadline,
  SMEComplianceProfile,
  TenderLifecycleData,
  DocumentTemplate,
  ComplianceDashboardData,
  ProtestFilter,
  SMEAnalysisFilter,
  APIResponse,
  PaginatedResponse,
  DocumentType,
  ProtestGround,
  Jurisdiction
} from '../../types/compliance';

// Base query configuration
const baseQuery = fetchBaseQuery({
  baseUrl: process.env.REACT_APP_API_URL || 'http://localhost:3000/api',
  prepareHeaders: (headers, { getState }) => {
    const token = localStorage.getItem('token');
    if (token) {
      headers.set('authorization', `Bearer ${token}`);
    }
    headers.set('Content-Type', 'application/json');
    return headers;
  },
});

export const complianceApi = createApi({
  reducerPath: 'complianceApi',
  baseQuery,
  tagTypes: [
    'BidProtest',
    'ComplianceDocument', 
    'SMEProfile',
    'TenderLifecycle',
    'DocumentTemplate',
    'ComplianceDashboard'
  ],
  endpoints: (builder) => ({
    
    // ===== BID PROTEST MANAGEMENT =====
    
    getBidProtests: builder.query<PaginatedResponse<BidProtest>, {
      page?: number;
      pageSize?: number;
      filter?: ProtestFilter;
    }>({
      query: ({ page = 1, pageSize = 10, filter = {} }) => ({
        url: '/compliance/protests',
        params: { page, pageSize, ...filter }
      }),
      providesTags: ['BidProtest']
    }),
    
    getBidProtestById: builder.query<BidProtest, string>({
      query: (id) => `/compliance/protests/${id}`,
      providesTags: (result, error, id) => [{ type: 'BidProtest', id }]
    }),
    
    createBidProtest: builder.mutation<APIResponse<BidProtest>, CreateProtestRequest>({
      query: (protestData) => ({
        url: '/compliance/protests',
        method: 'POST',
        body: protestData
      }),
      invalidatesTags: ['BidProtest', 'ComplianceDashboard']
    }),
    
    updateBidProtest: builder.mutation<APIResponse<BidProtest>, {
      id: string;
      updates: Partial<BidProtest>;
    }>({
      query: ({ id, updates }) => ({
        url: `/compliance/protests/${id}`,
        method: 'PUT',
        body: updates
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'BidProtest', id },
        'ComplianceDashboard'
      ]
    }),
    
    deleteBidProtest: builder.mutation<APIResponse<void>, string>({
      query: (id) => ({
        url: `/compliance/protests/${id}`,
        method: 'DELETE'
      }),
      invalidatesTags: ['BidProtest', 'ComplianceDashboard']
    }),
    
    // ===== PROTEST VIABILITY ANALYSIS =====
    
    analyzeProtestViability: builder.mutation<APIResponse<ProtestViabilityAnalysis>, ProtestViabilityRequest>({
      query: (analysisData) => ({
        url: '/compliance/protests/analyze-viability',
        method: 'POST',
        body: analysisData
      })
    }),
    
    detectIrregularities: builder.mutation<APIResponse<ComplianceIssue[]>, IrregularityDetectionRequest>({
      query: (tenderData) => ({
        url: '/compliance/protests/detect-irregularities',
        method: 'POST',
        body: tenderData
      })
    }),
    
    // ===== DOCUMENT GENERATION =====
    
    generateProtestDocument: builder.mutation<APIResponse<ComplianceDocument>, GenerateDocumentRequest>({
      query: (documentRequest) => ({
        url: '/compliance/documents/generate',
        method: 'POST',
        body: documentRequest
      }),
      invalidatesTags: ['ComplianceDocument', 'BidProtest']
    }),
    
    getDocumentTemplates: builder.query<DocumentTemplate[], {
      templateType?: DocumentType;
      isSMEFocused?: boolean;
    }>({
      query: (params) => ({
        url: '/compliance/templates',
        params
      }),
      providesTags: ['DocumentTemplate']
    }),
    
    getDocumentTemplate: builder.query<DocumentTemplate, string>({
      query: (id) => `/compliance/templates/${id}`,
      providesTags: (result, error, id) => [{ type: 'DocumentTemplate', id }]
    }),
    
    // ===== DOCUMENT MANAGEMENT =====
    
    getComplianceDocuments: builder.query<PaginatedResponse<ComplianceDocument>, {
      protestId?: string;
      documentType?: DocumentType;
      page?: number;
      pageSize?: number;
    }>({
      query: (params) => ({
        url: '/compliance/documents',
        params
      }),
      providesTags: ['ComplianceDocument']
    }),
    
    getComplianceDocument: builder.query<ComplianceDocument, string>({
      query: (id) => `/compliance/documents/${id}`,
      providesTags: (result, error, id) => [{ type: 'ComplianceDocument', id }]
    }),
    
    downloadDocument: builder.mutation<Blob, string>({
      query: (documentId) => ({
        url: `/compliance/documents/${documentId}/download`,
        method: 'GET',
        responseHandler: (response) => response.blob()
      })
    }),
    
    uploadComplianceDocument: builder.mutation<APIResponse<ComplianceDocument>, {
      file: File;
      protestId: string;
      documentType: DocumentType;
      title: string;
    }>({
      query: ({ file, protestId, documentType, title }) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('protestId', protestId);
        formData.append('documentType', documentType);
        formData.append('title', title);
        
        return {
          url: '/compliance/documents/upload',
          method: 'POST',
          body: formData,
          formData: true
        };
      },
      invalidatesTags: ['ComplianceDocument', 'BidProtest']
    }),
    
    // ===== DEADLINE MANAGEMENT =====
    
    getProtestDeadlines: builder.query<ProtestDeadline[], {
      protestId?: string;
      upcoming?: boolean;
      jurisdiction?: Jurisdiction;
    }>({
      query: (params) => ({
        url: '/compliance/deadlines',
        params
      })
    }),
    
    calculateDeadlines: builder.mutation<APIResponse<ProtestDeadline[]>, {
      awardDate: string;
      jurisdiction: Jurisdiction;
      protestGrounds: ProtestGround[];
    }>({
      query: (deadlineData) => ({
        url: '/compliance/deadlines/calculate',
        method: 'POST',
        body: deadlineData
      })
    }),
    
    // ===== SME COMPLIANCE MANAGEMENT =====
    
    getSMEProfile: builder.query<SMEComplianceProfile, string>({
      query: (tenantId) => `/compliance/sme/profile/${tenantId}`,
      providesTags: (result, error, tenantId) => [{ type: 'SMEProfile', id: tenantId }]
    }),
    
    updateSMEProfile: builder.mutation<APIResponse<SMEComplianceProfile>, {
      tenantId: string;
      updates: Partial<SMEComplianceProfile>;
    }>({
      query: ({ tenantId, updates }) => ({
        url: `/compliance/sme/profile/${tenantId}`,
        method: 'PUT',
        body: updates
      }),
      invalidatesTags: (result, error, { tenantId }) => [
        { type: 'SMEProfile', id: tenantId },
        'ComplianceDashboard'
      ]
    }),
    
    analyzeSMECompliance: builder.mutation<APIResponse<{
      complianceScore: number;
      weakAreas: string[];
      recommendations: string[];
      improvementPlan: any[];
    }>, string>({
      query: (tenantId) => ({
        url: `/compliance/sme/analyze/${tenantId}`,
        method: 'POST'
      }),
      invalidatesTags: (result, error, tenantId) => [{ type: 'SMEProfile', id: tenantId }]
    }),
    
    getSMEBenchmarks: builder.query<{
      industryAverage: number;
      peerComparison: any[];
      rankingPercentile: number;
    }, {
      tenantId: string;
      filter?: SMEAnalysisFilter;
    }>({
      query: ({ tenantId, filter = {} }) => ({
        url: `/compliance/sme/benchmarks/${tenantId}`,
        params: filter
      })
    }),
    
    // ===== TENDER LIFECYCLE MANAGEMENT =====
    
    getTenderLifecycleData: builder.query<TenderLifecycleData, string>({
      query: (tenderReference) => `/compliance/lifecycle/${tenderReference}`,
      providesTags: (result, error, tenderReference) => [
        { type: 'TenderLifecycle', id: tenderReference }
      ]
    }),
    
    createTenderLifecycleData: builder.mutation<APIResponse<TenderLifecycleData>, Partial<TenderLifecycleData>>({
      query: (lifecycleData) => ({
        url: '/compliance/lifecycle',
        method: 'POST',
        body: lifecycleData
      }),
      invalidatesTags: ['TenderLifecycle']
    }),
    
    updateTenderLifecycleData: builder.mutation<APIResponse<TenderLifecycleData>, {
      tenderReference: string;
      updates: Partial<TenderLifecycleData>;
    }>({
      query: ({ tenderReference, updates }) => ({
        url: `/compliance/lifecycle/${tenderReference}`,
        method: 'PUT',
        body: updates
      }),
      invalidatesTags: (result, error, { tenderReference }) => [
        { type: 'TenderLifecycle', id: tenderReference }
      ]
    }),
    
    // ===== COMPLIANCE DASHBOARD =====
    
    getComplianceDashboard: builder.query<ComplianceDashboardData, string>({
      query: (tenantId) => `/compliance/dashboard/${tenantId}`,
      providesTags: ['ComplianceDashboard']
    }),
    
    getComplianceActivity: builder.query<any[], {
      tenantId: string;
      limit?: number;
      activityType?: string;
    }>({
      query: ({ tenantId, limit = 10, activityType }) => ({
        url: `/compliance/activity/${tenantId}`,
        params: { limit, activityType }
      })
    }),
    
    // ===== COMPLIANCE REPORTING =====
    
    generateComplianceReport: builder.mutation<APIResponse<ComplianceDocument>, {
      tenantId: string;
      reportType: 'protest_summary' | 'compliance_audit' | 'sme_analysis';
      dateFrom: string;
      dateTo: string;
      includeRecommendations: boolean;
    }>({
      query: (reportRequest) => ({
        url: '/compliance/reports/generate',
        method: 'POST',
        body: reportRequest
      }),
      invalidatesTags: ['ComplianceDocument']
    }),
    
    // ===== LEGAL FRAMEWORK INTEGRATION =====
    
    getLegalFramework: builder.query<{
      regulations: any[];
      deadlineRules: any[];
      protestGrounds: any[];
      jurisdictionRules: any[];
    }, Jurisdiction>({
      query: (jurisdiction) => `/compliance/legal-framework/${jurisdiction}`
    }),
    
    validateLegalCompliance: builder.mutation<APIResponse<{
      isCompliant: boolean;
      violations: any[];
      recommendations: string[];
    }>, {
      protestData: Partial<BidProtest>;
      jurisdiction: Jurisdiction;
    }>({
      query: (validationRequest) => ({
        url: '/compliance/legal-framework/validate',
        method: 'POST',
        body: validationRequest
      })
    }),
    
    // ===== INTEGRATION WITH EXISTING BIDBEEZ SYSTEMS =====
    
    linkTenderToCompliance: builder.mutation<APIResponse<void>, {
      tenderId: string;
      complianceData: Partial<TenderLifecycleData>;
    }>({
      query: (linkData) => ({
        url: '/compliance/integration/link-tender',
        method: 'POST',
        body: linkData
      }),
      invalidatesTags: ['TenderLifecycle', 'BidProtest']
    }),
    
    syncEcosystemCompliance: builder.mutation<APIResponse<{
      beeData: any;
      taxData: any;
      cipcData: any;
      skillsData: any;
    }>, string>({
      query: (tenantId) => ({
        url: `/compliance/integration/sync-ecosystem/${tenantId}`,
        method: 'POST'
      }),
      invalidatesTags: ['SMEProfile', 'ComplianceDashboard']
    })
  })
});

// Export hooks for use in components
export const {
  // Bid Protest Management
  useGetBidProtestsQuery,
  useGetBidProtestByIdQuery,
  useCreateBidProtestMutation,
  useUpdateBidProtestMutation,
  useDeleteBidProtestMutation,
  
  // Protest Analysis
  useAnalyzeProtestViabilityMutation,
  useDetectIrregularitiesMutation,
  
  // Document Management
  useGenerateProtestDocumentMutation,
  useGetDocumentTemplatesQuery,
  useGetDocumentTemplateQuery,
  useGetComplianceDocumentsQuery,
  useGetComplianceDocumentQuery,
  useDownloadDocumentMutation,
  useUploadComplianceDocumentMutation,
  
  // Deadline Management
  useGetProtestDeadlinesQuery,
  useCalculateDeadlinesMutation,
  
  // SME Management
  useGetSMEProfileQuery,
  useUpdateSMEProfileMutation,
  useAnalyzeSMEComplianceMutation,
  useGetSMEBenchmarksQuery,
  
  // Tender Lifecycle
  useGetTenderLifecycleDataQuery,
  useCreateTenderLifecycleDataMutation,
  useUpdateTenderLifecycleDataMutation,
  
  // Dashboard
  useGetComplianceDashboardQuery,
  useGetComplianceActivityQuery,
  
  // Reporting
  useGenerateComplianceReportMutation,
  
  // Legal Framework
  useGetLegalFrameworkQuery,
  useValidateLegalComplianceMutation,
  
  // Integration
  useLinkTenderToComplianceMutation,
  useSyncEcosystemComplianceMutation
} = complianceApi;
