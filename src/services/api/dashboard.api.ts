import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

export const dashboardApi = createApi({
  reducerPath: 'dashboardApi',
  baseQuery: fetchBaseQuery({ baseUrl: process.env.NEXT_PUBLIC_API_URL }),
  endpoints: (builder) => ({
    getDashboardData: builder.query<any, void>({
      query: () => 'dashboard',
    }),
    getDashboardStats: builder.query<any, void>({
      query: () => 'dashboard/stats',
    }),
  }),
});

export const { useGetDashboardDataQuery, useGetDashboardStatsQuery } = dashboardApi;