/**
 * WhatsApp Auto-Bidding API Service
 * Connects frontend to WhatsApp auto-bidding backend
 */

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// Base URL for WhatsApp API
const WHATSAPP_API_BASE_URL = process.env.NEXT_PUBLIC_WHATSAPP_API_URL || 'http://localhost:8007';

// Types
export interface AutoBidSettings {
  id?: string;
  user_id: string;
  whatsapp_number: string;
  auto_bid_preference: 'always' | 'ask_first' | 'never' | 'conditions_based';
  max_bid_value?: number;
  preferred_categories: string[];
  excluded_categories: string[];
  minimum_confidence_score: number;
  require_feasibility_check: boolean;
  notify_on_auto_bid: boolean;
  
  // Advanced settings
  max_daily_auto_bids: number;
  max_monthly_auto_bids: number;
  working_hours_only: boolean;
  working_hours_start: string;
  working_hours_end: string;
  weekend_auto_bid: boolean;
  
  // Geographic preferences
  preferred_provinces: string[];
  excluded_provinces: string[];
  max_distance_km: number;
  
  // Financial controls
  daily_spend_limit?: number;
  monthly_spend_limit?: number;
  require_approval_above?: number;
  
  // Status
  is_active: boolean;
  last_auto_bid_at?: string;
  total_auto_bids: number;
  successful_auto_bids: number;
}

export interface WhatsAppStatus {
  is_connected: boolean;
  is_verified: boolean;
  auto_bid_enabled: boolean;
  messages_processed: number;
  auto_bids_triggered: number;
  last_activity: string;
  connection_quality: 'excellent' | 'good' | 'poor' | 'disconnected';
  pending_messages: number;
  settings: AutoBidSettings;
}

export interface WhatsAppMessage {
  id: string;
  message_id: string;
  from_number: string;
  to_number: string;
  message_body: string;
  message_type: 'bid_notification' | 'tender_alert' | 'deadline_reminder' | 'document_available' | 'unknown';
  processing_status: 'pending' | 'processing' | 'processed' | 'failed' | 'ignored';
  confidence_score: number;
  extracted_data: {
    tender_id?: string;
    tender_title?: string;
    organization?: string;
    estimated_value?: number;
    closing_date?: string;
    location?: string;
    category?: string;
  };
  auto_bid_triggered: boolean;
  auto_bid_reason?: string;
  auto_bid_result?: any;
  whatsapp_timestamp: string;
  received_at: string;
  processed_at?: string;
}

export interface AutoBidActivity {
  id: string;
  user_id: string;
  tender_id?: string;
  whatsapp_message_id?: string;
  trigger_source: 'whatsapp' | 'email' | 'sms' | 'api' | 'manual';
  auto_bid_status: 'initiated' | 'feasibility_check' | 'ai_processing' | 'completed' | 'failed' | 'cancelled';
  feasibility_result?: any;
  ai_result?: any;
  bid_id?: string;
  submission_status?: string;
  success_probability?: number;
  estimated_cost?: number;
  actual_cost?: number;
  jobs_created: number;
  economic_value: number;
  processing_time_seconds?: number;
  started_at: string;
  completed_at?: string;
  user_notified: boolean;
  error_details?: string;
}

export interface WhatsAppAnalytics {
  date: string;
  total_messages_received: number;
  bid_notifications_received: number;
  messages_processed: number;
  processing_errors: number;
  auto_bids_triggered: number;
  auto_bids_successful: number;
  auto_bids_failed: number;
  permission_requests_sent: number;
  user_approvals_received: number;
  avg_processing_time_seconds: number;
  avg_confidence_score: number;
  total_jobs_created: number;
  total_economic_value: number;
  active_users: number;
}

// API Definition
export const whatsappApi = createApi({
  reducerPath: 'whatsappApi',
  baseQuery: fetchBaseQuery({
    baseUrl: WHATSAPP_API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      // Add auth token if available
      const token = (getState() as any)?.auth?.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['AutoBidSettings', 'WhatsAppStatus', 'Messages', 'Activities', 'Analytics'],
  endpoints: (builder) => ({
    // Get WhatsApp status
    getWhatsAppStatus: builder.query<WhatsAppStatus, { userId: string }>({
      query: ({ userId }) => ({
        url: `/status/${userId}`,
      }),
      providesTags: ['WhatsAppStatus'],
    }),

    // Get auto-bid settings
    getAutoBidSettings: builder.query<AutoBidSettings, { userId: string }>({
      query: ({ userId }) => ({
        url: `/settings/auto-bid/${userId}`,
      }),
      providesTags: ['AutoBidSettings'],
    }),

    // Update auto-bid settings
    updateAutoBidSettings: builder.mutation<AutoBidSettings, AutoBidSettings>({
      query: (settings) => ({
        url: '/settings/auto-bid',
        method: 'POST',
        body: settings,
      }),
      invalidatesTags: ['AutoBidSettings', 'WhatsAppStatus'],
    }),

    // Get WhatsApp messages
    getWhatsAppMessages: builder.query<{
      messages: WhatsAppMessage[];
      total: number;
      page: number;
      limit: number;
    }, {
      userId: string;
      page?: number;
      limit?: number;
      status?: string;
      messageType?: string;
    }>({
      query: ({ userId, page = 1, limit = 20, status, messageType }) => ({
        url: `/messages/${userId}`,
        params: { page, limit, status, message_type: messageType },
      }),
      providesTags: ['Messages'],
    }),

    // Get auto-bid activities
    getAutoBidActivities: builder.query<{
      activities: AutoBidActivity[];
      total: number;
      page: number;
      limit: number;
    }, {
      userId: string;
      page?: number;
      limit?: number;
      status?: string;
    }>({
      query: ({ userId, page = 1, limit = 20, status }) => ({
        url: `/activities/${userId}`,
        params: { page, limit, status },
      }),
      providesTags: ['Activities'],
    }),

    // Get WhatsApp analytics
    getWhatsAppAnalytics: builder.query<WhatsAppAnalytics[], {
      userId: string;
      startDate?: string;
      endDate?: string;
    }>({
      query: ({ userId, startDate, endDate }) => ({
        url: `/analytics/${userId}`,
        params: { start_date: startDate, end_date: endDate },
      }),
      providesTags: ['Analytics'],
    }),

    // Verify WhatsApp number
    verifyWhatsAppNumber: builder.mutation<{ success: boolean; message: string }, {
      userId: string;
      phoneNumber: string;
    }>({
      query: ({ userId, phoneNumber }) => ({
        url: '/verify-number',
        method: 'POST',
        body: { user_id: userId, phone_number: phoneNumber },
      }),
      invalidatesTags: ['WhatsAppStatus'],
    }),

    // Test auto-bid functionality
    testAutoBid: builder.mutation<{ success: boolean; result: any }, {
      userId: string;
      testMessage: string;
    }>({
      query: ({ userId, testMessage }) => ({
        url: '/test-auto-bid',
        method: 'POST',
        body: { user_id: userId, test_message: testMessage },
      }),
    }),

    // Send test WhatsApp message
    sendTestMessage: builder.mutation<{ success: boolean; message_id: string }, {
      userId: string;
      phoneNumber: string;
      message: string;
    }>({
      query: ({ userId, phoneNumber, message }) => ({
        url: '/send-test-message',
        method: 'POST',
        body: { user_id: userId, phone_number: phoneNumber, message },
      }),
    }),

    // Toggle auto-bid status
    toggleAutoBid: builder.mutation<{ success: boolean; enabled: boolean }, {
      userId: string;
      enabled: boolean;
    }>({
      query: ({ userId, enabled }) => ({
        url: '/toggle-auto-bid',
        method: 'POST',
        body: { user_id: userId, enabled },
      }),
      invalidatesTags: ['AutoBidSettings', 'WhatsAppStatus'],
    }),

    // Process pending messages manually
    processPendingMessages: builder.mutation<{ success: boolean; processed: number }, {
      userId: string;
    }>({
      query: ({ userId }) => ({
        url: '/process-pending',
        method: 'POST',
        body: { user_id: userId },
      }),
      invalidatesTags: ['Messages', 'WhatsAppStatus'],
    }),

    // Get webhook status
    getWebhookStatus: builder.query<{
      webhook_url: string;
      is_verified: boolean;
      last_ping: string;
      status: 'active' | 'inactive' | 'error';
    }, void>({
      query: () => ({
        url: '/webhook/status',
      }),
    }),

    // Update notification preferences
    updateNotificationPreferences: builder.mutation<void, {
      userId: string;
      preferences: {
        receive_bid_notifications?: boolean;
        receive_deadline_reminders?: boolean;
        receive_document_alerts?: boolean;
        receive_auto_bid_confirmations?: boolean;
        receive_daily_summaries?: boolean;
        quiet_hours_start?: string;
        quiet_hours_end?: string;
        preferred_language?: string;
      };
    }>({
      query: ({ userId, preferences }) => ({
        url: `/preferences/${userId}`,
        method: 'PUT',
        body: preferences,
      }),
      invalidatesTags: ['AutoBidSettings'],
    }),
  }),
});

// Export hooks for use in components
export const {
  useGetWhatsAppStatusQuery,
  useGetAutoBidSettingsQuery,
  useUpdateAutoBidSettingsMutation,
  useGetWhatsAppMessagesQuery,
  useGetAutoBidActivitiesQuery,
  useGetWhatsAppAnalyticsQuery,
  useVerifyWhatsAppNumberMutation,
  useTestAutoBidMutation,
  useSendTestMessageMutation,
  useToggleAutoBidMutation,
  useProcessPendingMessagesMutation,
  useGetWebhookStatusQuery,
  useUpdateNotificationPreferencesMutation,
} = whatsappApi;

// Export API reducer
export default whatsappApi;
