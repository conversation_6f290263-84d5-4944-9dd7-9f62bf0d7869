/**
 * Analytics API Service
 * Connects frontend to bid analytics backend
 */

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// Base URL for analytics API
const ANALYTICS_API_BASE_URL = process.env.NEXT_PUBLIC_ANALYTICS_API_URL || 'http://localhost:8008';

// Types
export interface BidPerformanceMetrics {
  // Core Performance
  total_bids: number;
  successful_bids: number;
  success_rate: number;
  total_value: number;
  won_value: number;
  average_bid_value: number;
  
  // Financial Analytics
  revenue: number;
  profit: number;
  profit_margin: number;
  roi: number;
  cost_per_bid: number;
  revenue_per_bid: number;
  
  // Competitive Analytics
  market_position: number;
  competitive_win_rate: number;
  average_competitors: number;
  win_rate_vs_competitors: number;
  
  // Efficiency Metrics
  average_response_time: number;
  average_preparation_time: number;
  document_completion_rate: number;
  compliance_score: number;
  
  // Economic Impact
  jobs_created: number;
  economic_value: number;
  community_impact: string;
}

export interface BidSummary {
  user_id: string;
  time_range: 'week' | 'month' | 'quarter' | 'year';
  
  // Quick Performance
  recent_performance: {
    last_30_days: {
      bids: number;
      wins: number;
      success_rate: number;
      value: number;
    };
    trend: 'up' | 'down' | 'stable';
    trend_percentage: number;
  };
  
  // Top Insights
  top_insights: Array<{
    type: 'success' | 'warning' | 'info' | 'achievement';
    title: string;
    description: string;
    value?: string;
    trend?: 'up' | 'down' | 'stable';
  }>;
  
  // Quick Stats
  quick_stats: {
    avg_response_time: number;
    compliance_score: number;
    competitive_position: number;
    next_deadline: string;
  };
  
  // Economic Impact
  economic_impact: {
    jobs_created: number;
    economic_value: number;
    community_rank: string;
  };
  
  // Psychological State
  psychological_state: {
    current_mood: 'motivated' | 'stressed' | 'confident' | 'overwhelmed';
    stress_level: number;
    optimal_time: string;
    recommendation: string;
  };
  
  // Alerts & Recommendations
  alerts: Array<{
    severity: 'error' | 'warning' | 'info' | 'success';
    title: string;
    message: string;
    action_required: boolean;
    deadline?: string;
  }>;
  
  recommendations: Array<{
    priority: 'high' | 'medium' | 'low';
    category: 'performance' | 'financial' | 'competitive' | 'psychological';
    title: string;
    description: string;
    expected_impact: string;
  }>;
}

export interface CompetitorAnalysis {
  competitor_name: string;
  heads_up_wins: number;
  heads_up_losses: number;
  win_rate: number;
  avg_bid_difference: number;
  last_encounter: string;
  threat_level: 'low' | 'medium' | 'high' | 'critical';
}

export interface CategoryPerformance {
  category: string;
  bids: number;
  wins: number;
  success_rate: number;
  total_value: number;
  avg_value: number;
  profitability: number;
}

export interface PsychologicalInsights {
  behavioral_patterns: Array<{
    pattern: string;
    impact: 'positive' | 'negative' | 'neutral';
    frequency: number;
    recommendation: string;
  }>;
  stress_impact_on_success: number;
  optimal_bidding_times: string[];
  current_psychological_state: {
    mood: string;
    confidence_level: number;
    stress_level: number;
    energy_level: number;
  };
  recommendations: Array<{
    type: 'timing' | 'preparation' | 'mindset' | 'strategy';
    title: string;
    description: string;
    expected_improvement: string;
  }>;
}

// API Definition
export const analyticsApi = createApi({
  reducerPath: 'analyticsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: `${ANALYTICS_API_BASE_URL}/analytics`,
    prepareHeaders: (headers, { getState }) => {
      // Add auth token if available
      const token = (getState() as any)?.auth?.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['BidPerformance', 'BidSummary', 'Competitors', 'Categories', 'Psychology'],
  endpoints: (builder) => ({
    // Get comprehensive bid performance metrics
    getBidPerformance: builder.query<BidPerformanceMetrics, {
      userId: string;
      timeRange?: 'week' | 'month' | 'quarter' | 'year';
    }>({
      query: ({ userId, timeRange = 'month' }) => ({
        url: `/performance/${userId}`,
        params: { time_range: timeRange },
      }),
      providesTags: ['BidPerformance'],
    }),

    // Get quick bid summary
    getBidSummary: builder.query<BidSummary, {
      userId: string;
      compact?: boolean;
    }>({
      query: ({ userId, compact = false }) => ({
        url: `/summary/${userId}`,
        params: { compact },
      }),
      providesTags: ['BidSummary'],
    }),

    // Get competitive analysis
    getCompetitiveAnalysis: builder.query<CompetitorAnalysis[], {
      userId: string;
      timeRange?: 'week' | 'month' | 'quarter' | 'year';
    }>({
      query: ({ userId, timeRange = 'quarter' }) => ({
        url: `/competitive/${userId}`,
        params: { time_range: timeRange },
      }),
      providesTags: ['Competitors'],
    }),

    // Get category performance
    getCategoryPerformance: builder.query<CategoryPerformance[], {
      userId: string;
      timeRange?: 'week' | 'month' | 'quarter' | 'year';
    }>({
      query: ({ userId, timeRange = 'year' }) => ({
        url: `/categories/${userId}`,
        params: { time_range: timeRange },
      }),
      providesTags: ['Categories'],
    }),

    // Get psychological insights
    getPsychologicalInsights: builder.query<PsychologicalInsights, {
      userId: string;
    }>({
      query: ({ userId }) => ({
        url: `/psychological/${userId}`,
      }),
      providesTags: ['Psychology'],
    }),

    // Export analytics data
    exportAnalytics: builder.mutation<Blob, {
      userId: string;
      format: 'pdf' | 'excel' | 'csv';
      timeRange?: 'week' | 'month' | 'quarter' | 'year';
    }>({
      query: ({ userId, format, timeRange = 'month' }) => ({
        url: `/export/${userId}`,
        method: 'POST',
        body: { format, time_range: timeRange },
        responseHandler: (response) => response.blob(),
      }),
    }),

    // Update analytics preferences
    updateAnalyticsPreferences: builder.mutation<void, {
      userId: string;
      preferences: {
        default_time_range?: 'week' | 'month' | 'quarter' | 'year';
        show_economic_impact?: boolean;
        show_psychological_insights?: boolean;
        email_reports?: boolean;
        report_frequency?: 'daily' | 'weekly' | 'monthly';
      };
    }>({
      query: ({ userId, preferences }) => ({
        url: `/preferences/${userId}`,
        method: 'PUT',
        body: preferences,
      }),
      invalidatesTags: ['BidSummary', 'BidPerformance'],
    }),
  }),
});

// Export hooks for use in components
export const {
  useGetBidPerformanceQuery,
  useGetBidSummaryQuery,
  useGetCompetitiveAnalysisQuery,
  useGetCategoryPerformanceQuery,
  useGetPsychologicalInsightsQuery,
  useExportAnalyticsMutation,
  useUpdateAnalyticsPreferencesMutation,
} = analyticsApi;

// Export API reducer
export default analyticsApi;
