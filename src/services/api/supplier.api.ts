/**
 * Supplier API Service
 * Connects frontend to supplier revenue and management backend
 */

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// Base URL for supplier API
const SUPPLIER_API_BASE_URL = process.env.NEXT_PUBLIC_SUPPLIER_API_URL || 'http://localhost:8006';

// Types
export interface SupplierMetrics {
  total_revenue: number;
  monthly_revenue: number;
  quarterly_revenue: number;
  yearly_revenue: number;
  active_quotes: number;
  pending_quotes: number;
  won_quotes: number;
  success_rate: number;
  average_quote_value: number;
  total_jobs_created: number;
  economic_impact: number;
}

export interface QuoteRequest {
  id: string;
  tender_id: string;
  tender_title: string;
  organization: string;
  category: string;
  estimated_value: number;
  closing_date: string;
  requirements: string[];
  status: 'pending' | 'submitted' | 'won' | 'lost' | 'expired';
  confidence_score: number;
  created_at: string;
  updated_at: string;
}

export interface SalesRep {
  id: string;
  name: string;
  email: string;
  phone: string;
  company_id: string;
  monthly_target: number;
  yearly_target: number;
  current_performance: number;
  success_rate: number;
  total_sales: number;
  rank: number;
  badges: string[];
  psychological_profile: {
    motivation_level: number;
    stress_level: number;
    optimal_times: string[];
  };
}

// API Definition
export const supplierApi = createApi({
  reducerPath: 'supplierApi',
  baseQuery: fetchBaseQuery({
    baseUrl: SUPPLIER_API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      // Add auth token if available
      const token = (getState() as any)?.auth?.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['SupplierMetrics', 'Quotes', 'SalesReps'],
  endpoints: (builder) => ({
    // Get supplier metrics
    getSupplierMetrics: builder.query<SupplierMetrics, {
      userId: string;
      timeRange?: 'week' | 'month' | 'quarter' | 'year';
    }>({
      query: ({ userId, timeRange = 'month' }) => ({
        url: `/metrics/${userId}`,
        params: { time_range: timeRange },
      }),
      providesTags: ['SupplierMetrics'],
    }),

    // Get quote requests
    getQuoteRequests: builder.query<{
      quotes: QuoteRequest[];
      total: number;
      page: number;
      limit: number;
    }, {
      userId: string;
      page?: number;
      limit?: number;
      status?: string;
    }>({
      query: ({ userId, page = 1, limit = 20, status }) => ({
        url: `/quotes/${userId}`,
        params: { page, limit, status },
      }),
      providesTags: ['Quotes'],
    }),

    // Submit quote response
    submitQuote: builder.mutation<{ success: boolean; quote_id: string }, {
      userId: string;
      quoteId: string;
      response: {
        price: number;
        delivery_time: number;
        notes?: string;
        attachments?: string[];
      };
    }>({
      query: ({ userId, quoteId, response }) => ({
        url: `/quotes/${quoteId}/submit`,
        method: 'POST',
        body: { user_id: userId, ...response },
      }),
      invalidatesTags: ['Quotes', 'SupplierMetrics'],
    }),

    // Get sales reps
    getSalesReps: builder.query<{
      reps: SalesRep[];
      total: number;
    }, {
      companyId: string;
    }>({
      query: ({ companyId }) => ({
        url: `/reps/${companyId}`,
      }),
      providesTags: ['SalesReps'],
    }),

    // Create sales rep
    createSalesRep: builder.mutation<SalesRep, {
      name: string;
      email: string;
      phone: string;
      company_id: string;
      monthly_target: number;
      yearly_target: number;
    }>({
      query: (repData) => ({
        url: '/reps',
        method: 'POST',
        body: repData,
      }),
      invalidatesTags: ['SalesReps'],
    }),

    // Update sales rep targets
    updateSalesRepTargets: builder.mutation<void, {
      repId: string;
      monthly_target: number;
      yearly_target: number;
    }>({
      query: ({ repId, ...targets }) => ({
        url: `/reps/${repId}/targets`,
        method: 'PUT',
        body: targets,
      }),
      invalidatesTags: ['SalesReps'],
    }),
  }),
});

// Export hooks for use in components
export const {
  useGetSupplierMetricsQuery,
  useGetQuoteRequestsQuery,
  useSubmitQuoteMutation,
  useGetSalesRepsQuery,
  useCreateSalesRepMutation,
  useUpdateSalesRepTargetsMutation,
} = supplierApi;

// Export API reducer
export default supplierApi;
