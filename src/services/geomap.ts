/**
 * Geomap Module - Geographic Services and Utilities
 * Provides unified interface for Google Maps and Mapbox services
 */

import { env } from '../config/environment';

export interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface Location extends Coordinates {
  address?: string;
  city?: string;
  province?: string;
  country?: string;
  postalCode?: string;
}

export interface DistanceResult {
  distance: number; // in kilometers
  duration: number; // in minutes
  route?: any; // Route geometry
}

export interface GeocodeResult {
  coordinates: Coordinates;
  formattedAddress: string;
  components: {
    streetNumber?: string;
    route?: string;
    locality?: string;
    administrativeAreaLevel1?: string;
    country?: string;
    postalCode?: string;
  };
}

export interface MapBounds {
  northeast: Coordinates;
  southwest: Coordinates;
}

export interface MapProvider {
  name: 'google' | 'mapbox';
  apiKey: string;
  isAvailable: boolean;
}

class GeomapService {
  private static instance: GeomapService;
  private providers: MapProvider[] = [];
  private primaryProvider: 'google' | 'mapbox' = 'mapbox';

  private constructor() {
    this.initializeProviders();
  }

  public static getInstance(): GeomapService {
    if (!GeomapService.instance) {
      GeomapService.instance = new GeomapService();
    }
    return GeomapService.instance;
  }

  private initializeProviders(): void {
    // Initialize Mapbox
    if (env.mapboxToken) {
      this.providers.push({
        name: 'mapbox',
        apiKey: env.mapboxToken,
        isAvailable: true
      });
    }

    // Initialize Google Maps (if available)
    if (process.env.REACT_APP_GOOGLE_MAPS_API_KEY) {
      this.providers.push({
        name: 'google',
        apiKey: process.env.REACT_APP_GOOGLE_MAPS_API_KEY,
        isAvailable: true
      });
    }

    // Set primary provider based on availability
    if (this.providers.find(p => p.name === 'mapbox')) {
      this.primaryProvider = 'mapbox';
    } else if (this.providers.find(p => p.name === 'google')) {
      this.primaryProvider = 'google';
    }
  }

  public getAvailableProviders(): MapProvider[] {
    return this.providers.filter(p => p.isAvailable);
  }

  public getPrimaryProvider(): 'google' | 'mapbox' {
    return this.primaryProvider;
  }

  public setPrimaryProvider(provider: 'google' | 'mapbox'): void {
    if (this.providers.find(p => p.name === provider && p.isAvailable)) {
      this.primaryProvider = provider;
    }
  }

  /**
   * Calculate distance between two points
   */
  public async calculateDistance(
    origin: Coordinates,
    destination: Coordinates
  ): Promise<DistanceResult> {
    try {
      if (this.primaryProvider === 'mapbox') {
        return await this.calculateDistanceMapbox(origin, destination);
      } else {
        return await this.calculateDistanceGoogle(origin, destination);
      }
    } catch (error) {
      console.error('Distance calculation failed:', error);
      // Fallback to straight-line distance
      return this.calculateStraightLineDistance(origin, destination);
    }
  }

  private async calculateDistanceMapbox(
    origin: Coordinates,
    destination: Coordinates
  ): Promise<DistanceResult> {
    const mapboxToken = this.providers.find(p => p.name === 'mapbox')?.apiKey;
    if (!mapboxToken) throw new Error('Mapbox token not available');

    const url = `https://api.mapbox.com/directions/v5/mapbox/driving/${origin.longitude},${origin.latitude};${destination.longitude},${destination.latitude}?access_token=${mapboxToken}&geometries=geojson`;

    const response = await fetch(url);
    const data = await response.json();

    if (data.routes && data.routes.length > 0) {
      const route = data.routes[0];
      return {
        distance: route.distance / 1000, // Convert to kilometers
        duration: route.duration / 60, // Convert to minutes
        route: route.geometry
      };
    }

    throw new Error('No route found');
  }

  private async calculateDistanceGoogle(
    origin: Coordinates,
    destination: Coordinates
  ): Promise<DistanceResult> {
    // Google Maps Distance Matrix API implementation
    const googleKey = this.providers.find(p => p.name === 'google')?.apiKey;
    if (!googleKey) throw new Error('Google Maps API key not available');

    const url = `https://maps.googleapis.com/maps/api/distancematrix/json?origins=${origin.latitude},${origin.longitude}&destinations=${destination.latitude},${destination.longitude}&key=${googleKey}`;

    const response = await fetch(url);
    const data = await response.json();

    if (data.rows && data.rows[0].elements && data.rows[0].elements[0].status === 'OK') {
      const element = data.rows[0].elements[0];
      return {
        distance: element.distance.value / 1000, // Convert to kilometers
        duration: element.duration.value / 60 // Convert to minutes
      };
    }

    throw new Error('No route found');
  }

  private calculateStraightLineDistance(
    origin: Coordinates,
    destination: Coordinates
  ): DistanceResult {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(destination.latitude - origin.latitude);
    const dLon = this.toRadians(destination.longitude - origin.longitude);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(origin.latitude)) * Math.cos(this.toRadians(destination.latitude)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;

    return {
      distance,
      duration: distance * 1.5 // Rough estimate: 1.5 minutes per km
    };
  }

  /**
   * Geocode an address to coordinates
   */
  public async geocodeAddress(address: string): Promise<GeocodeResult> {
    try {
      if (this.primaryProvider === 'mapbox') {
        return await this.geocodeAddressMapbox(address);
      } else {
        return await this.geocodeAddressGoogle(address);
      }
    } catch (error) {
      console.error('Geocoding failed:', error);
      throw error;
    }
  }

  private async geocodeAddressMapbox(address: string): Promise<GeocodeResult> {
    const mapboxToken = this.providers.find(p => p.name === 'mapbox')?.apiKey;
    if (!mapboxToken) throw new Error('Mapbox token not available');

    const encodedAddress = encodeURIComponent(address);
    const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodedAddress}.json?access_token=${mapboxToken}&country=za&limit=1`;

    const response = await fetch(url);
    const data = await response.json();

    if (data.features && data.features.length > 0) {
      const feature = data.features[0];
      const [longitude, latitude] = feature.center;

      return {
        coordinates: { latitude, longitude },
        formattedAddress: feature.place_name,
        components: this.parseMapboxComponents(feature.context || [])
      };
    }

    throw new Error('Address not found');
  }

  private async geocodeAddressGoogle(address: string): Promise<GeocodeResult> {
    const googleKey = this.providers.find(p => p.name === 'google')?.apiKey;
    if (!googleKey) throw new Error('Google Maps API key not available');

    const encodedAddress = encodeURIComponent(address);
    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodedAddress}&key=${googleKey}`;

    const response = await fetch(url);
    const data = await response.json();

    if (data.results && data.results.length > 0) {
      const result = data.results[0];
      const location = result.geometry.location;

      return {
        coordinates: { latitude: location.lat, longitude: location.lng },
        formattedAddress: result.formatted_address,
        components: this.parseGoogleComponents(result.address_components)
      };
    }

    throw new Error('Address not found');
  }

  /**
   * Reverse geocode coordinates to address
   */
  public async reverseGeocode(coordinates: Coordinates): Promise<GeocodeResult> {
    try {
      if (this.primaryProvider === 'mapbox') {
        return await this.reverseGeocodeMapbox(coordinates);
      } else {
        return await this.reverseGeocodeGoogle(coordinates);
      }
    } catch (error) {
      console.error('Reverse geocoding failed:', error);
      throw error;
    }
  }

  private async reverseGeocodeMapbox(coordinates: Coordinates): Promise<GeocodeResult> {
    const mapboxToken = this.providers.find(p => p.name === 'mapbox')?.apiKey;
    if (!mapboxToken) throw new Error('Mapbox token not available');

    const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${coordinates.longitude},${coordinates.latitude}.json?access_token=${mapboxToken}&limit=1`;

    const response = await fetch(url);
    const data = await response.json();

    if (data.features && data.features.length > 0) {
      const feature = data.features[0];

      return {
        coordinates,
        formattedAddress: feature.place_name,
        components: this.parseMapboxComponents(feature.context || [])
      };
    }

    throw new Error('Location not found');
  }

  private async reverseGeocodeGoogle(coordinates: Coordinates): Promise<GeocodeResult> {
    const googleKey = this.providers.find(p => p.name === 'google')?.apiKey;
    if (!googleKey) throw new Error('Google Maps API key not available');

    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${coordinates.latitude},${coordinates.longitude}&key=${googleKey}`;

    const response = await fetch(url);
    const data = await response.json();

    if (data.results && data.results.length > 0) {
      const result = data.results[0];

      return {
        coordinates,
        formattedAddress: result.formatted_address,
        components: this.parseGoogleComponents(result.address_components)
      };
    }

    throw new Error('Location not found');
  }

  /**
   * Get current user location
   */
  public async getCurrentLocation(): Promise<Coordinates> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          });
        },
        (error) => {
          reject(new Error(`Geolocation error: ${error.message}`));
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      );
    });
  }

  /**
   * Calculate bounds for multiple points
   */
  public calculateBounds(points: Coordinates[]): MapBounds {
    if (points.length === 0) {
      throw new Error('No points provided');
    }

    let minLat = points[0].latitude;
    let maxLat = points[0].latitude;
    let minLng = points[0].longitude;
    let maxLng = points[0].longitude;

    points.forEach(point => {
      minLat = Math.min(minLat, point.latitude);
      maxLat = Math.max(maxLat, point.latitude);
      minLng = Math.min(minLng, point.longitude);
      maxLng = Math.max(maxLng, point.longitude);
    });

    return {
      southwest: { latitude: minLat, longitude: minLng },
      northeast: { latitude: maxLat, longitude: maxLng }
    };
  }

  // Helper methods
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  private parseMapboxComponents(context: any[]): any {
    const components: any = {};
    
    context.forEach(item => {
      if (item.id.includes('postcode')) {
        components.postalCode = item.text;
      } else if (item.id.includes('place')) {
        components.locality = item.text;
      } else if (item.id.includes('region')) {
        components.administrativeAreaLevel1 = item.text;
      } else if (item.id.includes('country')) {
        components.country = item.text;
      }
    });

    return components;
  }

  private parseGoogleComponents(components: any[]): any {
    const parsed: any = {};

    components.forEach(component => {
      const types = component.types;
      
      if (types.includes('street_number')) {
        parsed.streetNumber = component.long_name;
      } else if (types.includes('route')) {
        parsed.route = component.long_name;
      } else if (types.includes('locality')) {
        parsed.locality = component.long_name;
      } else if (types.includes('administrative_area_level_1')) {
        parsed.administrativeAreaLevel1 = component.long_name;
      } else if (types.includes('country')) {
        parsed.country = component.long_name;
      } else if (types.includes('postal_code')) {
        parsed.postalCode = component.long_name;
      }
    });

    return parsed;
  }
}

export default GeomapService;
