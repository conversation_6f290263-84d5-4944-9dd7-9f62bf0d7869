/**
 * Mock Data Service
 * Realistic business data for functional MVP demo
 */

import {
  Tender,
  Bid,
  ActivityLog,
  Notification,
  DashboardSummary,
  CalendarEvent,
  TenderStatus,
  BidStatus,
  DocumentStatus
  Priority,
  TenderCategory
} from '../types/tender';

// Mock Tenders Data
export const mockTenders: Tender[] = [
  {
    id: 'TND-001',
    title: 'Road Maintenance and Repair Services - N1 Highway',
    description: 'Comprehensive road maintenance services including pothole repairs, line marking, and general upkeep for the N1 highway between Johannesburg and Pretoria.',
    organization: 'South African National Roads Agency (SANRAL)',
    organizationType: 'parastatal',
    category: TenderCategory.CONSTRUCTION,
    value: 2500000,
    currency: 'ZAR',
    location: 'Johannesburg to Pretoria',
    province: 'Gauteng',
    publishDate: '2024-01-15T08:00:00Z',
    closingDate: '2024-02-15T17:00:00Z',
    briefingDate: '2024-01-25T10:00:00Z',
    briefingLocation: 'SANRAL Offices, Centurion',
    status: TenderStatus.OPEN,
    priority: Priority.HIGH,
    referenceNumber: 'SANRAL/2024/001',
    contactPerson: '<PERSON>',
    contactEmail: '<EMAIL>',
    contactPhone: '+27 12 426 7600',
    viewedDate: '2024-01-16T09:30:00Z',
    bookmarked: true,
    matchScore: 95,
    estimatedCost: 180000,
    estimatedDuration: 90,
    complianceRequirements: [
      {
        id: 'CR-001',
        name: 'CIDB Registration',
        description: 'Valid CIDB registration Grade 7 or higher',
        mandatory: true,
        documentType: 'certificate',
        status: DocumentStatus.COMPLETED
      },
      {
        id: 'CR-002',
        name: 'B-BBEE Certificate',
        description: 'Valid B-BBEE certificate Level 4 or better',
        mandatory: true,
        documentType: 'certificate',
        status: DocumentStatus.IN_PROGRESS
      },
      {
        id: 'CR-003',
        name: 'Tax Compliance',
        description: 'Tax clearance certificate from SARS',
        mandatory: true,
        documentType: 'certificate',
        status: DocumentStatus.NOT_STARTED
      }
    ],
    technicalRequirements: [
      'Minimum 5 years experience in highway maintenance',
      'Own equipment for road marking',
      'Emergency response capability 24/7'
    ],
    financialRequirements: [
      'Annual turnover of R10 million minimum',
      'Bank guarantee capability',
      'Professional indemnity insurance R5 million'
    ],
    tenderDocuments: [],
    clarifications: []
  },
  {
    id: 'TND-002',
    title: 'IT Infrastructure Upgrade - Department of Health',
    description: 'Complete IT infrastructure overhaul including servers, networking equipment, and cybersecurity systems for 15 health facilities across Western Cape.',
    organization: 'Western Cape Department of Health',
    organizationType: 'government',
    category: TenderCategory.IT_SERVICES,
    value: 8500000,
    currency: 'ZAR',
    location: 'Cape Town and surrounding areas',
    province: 'Western Cape',
    publishDate: '2024-01-10T08:00:00Z',
    closingDate: '2024-02-20T17:00:00Z',
    status: TenderStatus.CLOSING_SOON,
    priority: Priority.CRITICAL,
    referenceNumber: 'WCDOH/IT/2024/002',
    contactPerson: 'Sarah van der Merwe',
    contactEmail: '<EMAIL>',
    contactPhone: '+27 21 483 3000',
    viewedDate: '2024-01-12T14:20:00Z',
    bookmarked: true,
    matchScore: 78,
    estimatedCost: 250000,
    estimatedDuration: 120,
    complianceRequirements: [
      {
        id: 'CR-004',
        name: 'SITA Registration',
        description: 'Valid SITA registration for IT services',
        mandatory: true,
        documentType: 'certificate',
        status: DocumentStatus.COMPLETED
      },
      {
        id: 'CR-005',
        name: 'ISO 27001 Certification',
        description: 'Information security management certification',
        mandatory: true,
        documentType: 'certificate',
        status: DocumentStatus.REJECTED,
        rejectionReason: 'Certificate expired'
      }
    ],
    technicalRequirements: [
      'Microsoft Gold Partner status',
      'Minimum 10 years IT infrastructure experience',
      'Certified cybersecurity specialists on team'
    ],
    financialRequirements: [
      'Annual turnover R50 million minimum',
      'Performance guarantee 10% of contract value'
    ],
    tenderDocuments: [],
    clarifications: []
  },
  {
    id: 'TND-003',
    title: 'Cleaning Services - Government Buildings Complex',
    description: 'Professional cleaning services for government office buildings including daily cleaning, window cleaning, and specialized sanitization services.',
    organization: 'Department of Public Works and Infrastructure',
    organizationType: 'government',
    category: TenderCategory.CLEANING,
    value: 1200000,
    currency: 'ZAR',
    location: 'Pretoria CBD',
    province: 'Gauteng',
    publishDate: '2024-01-20T08:00:00Z',
    closingDate: '2024-02-10T17:00:00Z',
    status: TenderStatus.CLOSING_SOON,
    priority: Priority.MEDIUM,
    referenceNumber: 'DPWI/CLEAN/2024/003',
    viewedDate: '2024-01-21T11:15:00Z',
    bookmarked: false,
    matchScore: 88,
    estimatedCost: 85000,
    estimatedDuration: 60,
    complianceRequirements: [
      {
        id: 'CR-006',
        name: 'Tax Compliance',
        description: 'Valid tax clearance certificate',
        mandatory: true,
        documentType: 'certificate',
        status: DocumentStatus.COMPLETED
      },
      {
        id: 'CR-007',
        name: 'B-BBEE Certificate',
        description: 'B-BBEE Level 2 or better required',
        mandatory: true,
        documentType: 'certificate',
        status: DocumentStatus.COMPLETED
      }
    ],
    technicalRequirements: [
      'Minimum 3 years cleaning services experience',
      'Own cleaning equipment and supplies',
      'Staff security clearance for government buildings'
    ],
    financialRequirements: [
      'Annual turnover R5 million minimum',
      'Public liability insurance R2 million'
    ],
    tenderDocuments: [],
    clarifications: []
  }
];

// Mock Bids Data
export const mockBids: Bid[] = [
  {
    id: 'BID-001',
    tenderId: 'TND-001',
    tenderTitle: 'Road Maintenance and Repair Services - N1 Highway',
    status: BidStatus.IN_PROGRESS,
    priority: Priority.HIGH,
    bidAmount: 2350000,
    currency: 'ZAR',
    validityPeriod: 90,
    deliveryPeriod: 90,
    createdDate: '2024-01-16T10:00:00Z',
    lastModified: '2024-01-18T15:30:00Z',
    submissionDeadline: '2024-02-15T17:00:00Z',
    leadPerson: 'Mike Johnson',
    teamMembers: ['Sarah Smith', 'David Brown', 'Lisa Wilson'],
    documents: [],
    requiredDocuments: [],
    completionPercentage: 65,
    estimatedHoursRemaining: 24,
    estimatedCost: 180000,
    profitMargin: 8.5,
    submissionMethod: 'online',
    notes: [
      'Need to finalize equipment rental costs',
      'Waiting for subcontractor quotes',
      'Review environmental compliance requirements'
    ],
    riskAssessment: 'Medium risk due to weather dependency and equipment availability'
  },
  {
    id: 'BID-002',
    tenderId: 'TND-002',
    tenderTitle: 'IT Infrastructure Upgrade - Department of Health',
    status: BidStatus.READY_TO_SUBMIT,
    priority: Priority.CRITICAL,
    bidAmount: 8200000,
    currency: 'ZAR',
    validityPeriod: 120,
    deliveryPeriod: 180,
    createdDate: '2024-01-12T16:00:00Z',
    lastModified: '2024-01-19T09:45:00Z',
    submissionDeadline: '2024-02-20T17:00:00Z',
    leadPerson: 'Jennifer Adams',
    teamMembers: ['Robert Taylor', 'Amanda Clark', 'Kevin Martinez'],
    documents: [],
    requiredDocuments: [],
    completionPercentage: 95,
    estimatedHoursRemaining: 4,
    estimatedCost: 250000,
    profitMargin: 12.2,
    submissionMethod: 'online',
    notes: [
      'Final review completed',
      'All documents uploaded',
      'Ready for submission'
    ],
    competitorAnalysis: 'Main competitors: TechCorp, InfoSystems Ltd. Our advantage: local presence and healthcare experience'
  },
  {
    id: 'BID-003',
    tenderId: 'TND-003',
    tenderTitle: 'Cleaning Services - Government Buildings Complex',
    status: BidStatus.SUBMITTED,
    priority: Priority.MEDIUM,
    bidAmount: 1150000,
    currency: 'ZAR',
    validityPeriod: 60,
    deliveryPeriod: 365,
    createdDate: '2024-01-21T12:00:00Z',
    lastModified: '2024-01-22T14:20:00Z',
    submissionDeadline: '2024-02-10T17:00:00Z',
    submittedDate: '2024-01-22T14:30:00Z',
    leadPerson: 'Patricia Williams',
    teamMembers: ['Mark Davis', 'Helen Garcia'],
    documents: [],
    requiredDocuments: [],
    completionPercentage: 100,
    estimatedHoursRemaining: 0,
    estimatedCost: 85000,
    actualCost: 87500,
    profitMargin: 7.8,
    submissionMethod: 'online',
    submissionReference: 'SUB-2024-003-789',
    submissionConfirmation: 'Confirmed received by DPWI at 14:32 on 22/01/2024',
    notes: [
      'Submitted successfully',
      'Confirmation received',
      'Awaiting evaluation results'
    ]
  }
];

// Mock Activity Log
export const mockActivityLog: ActivityLog[] = [
  {
    id: 'ACT-001',
    type: 'bid_submitted',
    title: 'Bid Submitted Successfully',
    description: 'Cleaning Services bid submitted to Department of Public Works',
    timestamp: '2024-01-22T14:30:00Z',
    userId: 'user-123',
    relatedId: 'BID-003',
    priority: Priority.HIGH,
    read: false
  },
  {
    id: 'ACT-002',
    type: 'document_uploaded',
    title: 'Document Uploaded',
    description: 'B-BBEE certificate uploaded for Road Maintenance tender',
    timestamp: '2024-01-22T11:15:00Z',
    userId: 'user-123',
    relatedId: 'TND-001',
    priority: Priority.MEDIUM,
    read: true
  },
  {
    id: 'ACT-003',
    type: 'tender_viewed',
    title: 'New Tender Viewed',
    description: 'Viewed IT Infrastructure Upgrade tender details',
    timestamp: '2024-01-22T09:30:00Z',
    userId: 'user-123',
    relatedId: 'TND-002',
    priority: Priority.LOW,
    read: true
  },
  {
    id: 'ACT-004',
    type: 'deadline_reminder',
    title: 'Deadline Approaching',
    description: 'IT Infrastructure bid deadline in 3 days',
    timestamp: '2024-01-22T08:00:00Z',
    userId: 'user-123',
    relatedId: 'BID-002',
    priority: Priority.CRITICAL,
    read: false
  }
];

// Mock Notifications
export const mockNotifications: Notification[] = [
  {
    id: 'NOT-001',
    type: 'deadline',
    title: 'Urgent: Bid Deadline Tomorrow',
    message: 'IT Infrastructure Upgrade bid must be submitted by 17:00 tomorrow',
    timestamp: '2024-01-22T08:00:00Z',
    read: false,
    actionRequired: true,
    actionUrl: '/bids/BID-002',
    priority: Priority.CRITICAL,
    relatedId: 'BID-002'
  },
  {
    id: 'NOT-002',
    type: 'document_required',
    title: 'Missing Document',
    message: 'Tax clearance certificate required for Road Maintenance bid',
    timestamp: '2024-01-22T10:30:00Z',
    read: false,
    actionRequired: true,
    actionUrl: '/bids/BID-001/documents',
    priority: Priority.HIGH,
    relatedId: 'BID-001'
  },
  {
    id: 'NOT-003',
    type: 'status_update',
    title: 'Bid Submitted Successfully',
    message: 'Your Cleaning Services bid has been received and is under review',
    timestamp: '2024-01-22T14:32:00Z',
    read: true,
    actionRequired: false,
    priority: Priority.MEDIUM,
    relatedId: 'BID-003'
  }
];

// Mock Calendar Events
export const mockCalendarEvents: CalendarEvent[] = [
  {
    id: 'CAL-001',
    title: 'IT Infrastructure Bid Deadline',
    type: 'deadline',
    date: '2024-02-20',
    time: '17:00',
    description: 'Final submission deadline for Department of Health IT tender',
    priority: Priority.CRITICAL,
    relatedId: 'BID-002',
    completed: false
  },
  {
    id: 'CAL-002',
    title: 'Road Maintenance Briefing',
    type: 'briefing',
    date: '2024-01-25',
    time: '10:00',
    description: 'Mandatory briefing session at SANRAL offices',
    location: 'SANRAL Offices, Centurion',
    priority: Priority.HIGH,
    relatedId: 'TND-001',
    completed: true
  },
  {
    id: 'CAL-003',
    title: 'Document Submission Reminder',
    type: 'reminder',
    date: '2024-01-24',
    time: '09:00',
    description: 'Upload remaining compliance documents',
    priority: Priority.MEDIUM,
    relatedId: 'BID-001',
    completed: false
  }
];

// Mock Dashboard Summary
export const mockDashboardSummary: DashboardSummary = {
  // Current metrics
  activeBids: 3,
  pendingTenders: 12,
  successRate: 68,
  totalEarnings: 2450000,
  
  // Trends
  bidsThisMonth: 5,
  bidsLastMonth: 3,
  successRateChange: 5.2,
  earningsChange: 12.8,
  
  // Urgent items
  urgentDeadlines: 2,
  missingDocuments: 3,
  complianceIssues: 1,
  
  // Activity and notifications
  recentActivity: mockActivityLog.slice(0, 5),
  notifications: mockNotifications.filter(n => !n.read),
  
  // Quick stats
  totalBidsSubmitted: 24,
  totalTendersViewed: 156,
  averageBidValue: 3200000,
  winRate: 68
};

// Service functions
export const getTenders = (): Promise<Tender[]> => {
  return new Promise((resolve) => {
    setTimeout(() => resolve(mockTenders), 500);
  });
};

export const getBids = (): Promise<Bid[]> => {
  return new Promise((resolve) => {
    setTimeout(() => resolve(mockBids), 300);
  });
};

export const getDashboardSummary = (): Promise<DashboardSummary> => {
  return new Promise((resolve) => {
    setTimeout(() => resolve(mockDashboardSummary), 400);
  });
};

export const getCalendarEvents = (): Promise<CalendarEvent[]> => {
  return new Promise((resolve) => {
    setTimeout(() => resolve(mockCalendarEvents), 200);
  });
};

export const markNotificationAsRead = (notificationId: string): Promise<void> => {
  return new Promise((resolve) => {
    const notification = mockNotifications.find(n => n.id === notificationId);
    if (notification) {
      notification.read = true;
    }
    setTimeout(() => resolve(), 100);
  });
};
