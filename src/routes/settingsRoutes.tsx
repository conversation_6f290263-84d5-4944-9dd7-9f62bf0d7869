/**
 * Settings Routes Configuration
 * Routing for all application settings and preferences
 */

import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useFeatureFlags } from '../hooks/useFeatureFlags';

// Lazy load settings pages
const GeneralSettings = React.lazy(() => import('../pages/settings/GeneralSettings'));
const WhatsAppSettings = React.lazy(() => import('../pages/settings/WhatsAppSettings'));
const AnalyticsSettings = React.lazy(() => import('../pages/settings/AnalyticsSettings'));
const NotificationSettings = React.lazy(() => import('../pages/settings/NotificationSettings'));
const PrivacySettings = React.lazy(() => import('../pages/settings/PrivacySettings'));
const BillingSettings = React.lazy(() => import('../pages/settings/BillingSettings'));
const FeatureSettings = React.lazy(() => import('../pages/settings/FeatureSettings'));
const ProfileSettings = React.lazy(() => import('../pages/settings/ProfileSettings'));
const SecuritySettings = React.lazy(() => import('../pages/settings/SecuritySettings'));
const IntegrationSettings = React.lazy(() => import('../pages/settings/IntegrationSettings'));

// Protected Route Component
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiresFeature?: string;
  requiresRole?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiresFeature,
  requiresRole 
}) => {
  const { user } = useAuth();
  const { isFeatureEnabled } = useFeatureFlags();

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  if (requiresFeature && !isFeatureEnabled(requiresFeature)) {
    return (
      <div style={{ 
        padding: '2rem', 
        textAlign: 'center',
        background: 'linear-gradient(135deg, #9E9E9E 0%, #616161 100%)',
        color: 'white',
        borderRadius: '12px',
        margin: '2rem'
      }}>
        <h2>⚙️ Setting Not Available</h2>
        <p>This setting requires a premium feature that is not enabled.</p>
        <p>Contact support to upgrade your plan.</p>
      </div>
    );
  }

  if (requiresRole && user.role !== requiresRole) {
    return (
      <div style={{ 
        padding: '2rem', 
        textAlign: 'center',
        background: 'linear-gradient(135deg, #F44336 0%, #D32F2F 100%)',
        color: 'white',
        borderRadius: '12px',
        margin: '2rem'
      }}>
        <h2>🔒 Access Restricted</h2>
        <p>This setting requires {requiresRole} access.</p>
        <p>Contact your administrator for access.</p>
      </div>
    );
  }

  return <>{children}</>;
};

// Settings Routes Component
const SettingsRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Main Settings Dashboard */}
      <Route 
        path="/" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Settings...</div>}>
              <GeneralSettings />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* General Settings */}
      <Route 
        path="/general" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading General Settings...</div>}>
              <GeneralSettings />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Profile Settings */}
      <Route 
        path="/profile" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Profile Settings...</div>}>
              <ProfileSettings />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Security Settings */}
      <Route 
        path="/security" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Security Settings...</div>}>
              <SecuritySettings />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* WhatsApp Settings */}
      <Route 
        path="/whatsapp" 
        element={
          <ProtectedRoute requiresFeature="whatsapp_autobid">
            <React.Suspense fallback={<div>Loading WhatsApp Settings...</div>}>
              <WhatsAppSettings />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Analytics Settings */}
      <Route 
        path="/analytics" 
        element={
          <ProtectedRoute requiresFeature="bid_analytics">
            <React.Suspense fallback={<div>Loading Analytics Settings...</div>}>
              <AnalyticsSettings />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Notification Settings */}
      <Route 
        path="/notifications" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Notification Settings...</div>}>
              <NotificationSettings />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Privacy Settings */}
      <Route 
        path="/privacy" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Privacy Settings...</div>}>
              <PrivacySettings />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Billing Settings */}
      <Route 
        path="/billing" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Billing Settings...</div>}>
              <BillingSettings />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Feature Settings */}
      <Route 
        path="/features" 
        element={
          <ProtectedRoute requiresRole="admin">
            <React.Suspense fallback={<div>Loading Feature Settings...</div>}>
              <FeatureSettings />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Integration Settings */}
      <Route 
        path="/integrations" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Integration Settings...</div>}>
              <IntegrationSettings />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* API Settings */}
      <Route 
        path="/api" 
        element={
          <ProtectedRoute requiresFeature="api_access">
            <div style={{ padding: '2rem' }}>
              <h2>🔌 API Settings</h2>
              <p>Manage your API keys and webhook configurations.</p>
              <p>Coming soon...</p>
            </div>
          </ProtectedRoute>
        } 
      />

      {/* Advanced Settings */}
      <Route 
        path="/advanced" 
        element={
          <ProtectedRoute requiresRole="admin">
            <div style={{ padding: '2rem' }}>
              <h2>🔧 Advanced Settings</h2>
              <p>Advanced configuration options for power users.</p>
              <p>Coming soon...</p>
            </div>
          </ProtectedRoute>
        } 
      />

      {/* Data Export */}
      <Route 
        path="/export" 
        element={
          <ProtectedRoute>
            <div style={{ padding: '2rem' }}>
              <h2>📤 Data Export</h2>
              <p>Export your data and settings for backup or migration.</p>
              <p>Coming soon...</p>
            </div>
          </ProtectedRoute>
        } 
      />

      {/* Data Import */}
      <Route 
        path="/import" 
        element={
          <ProtectedRoute>
            <div style={{ padding: '2rem' }}>
              <h2>📥 Data Import</h2>
              <p>Import data and settings from other platforms.</p>
              <p>Coming soon...</p>
            </div>
          </ProtectedRoute>
        } 
      />

      {/* Account Deletion */}
      <Route 
        path="/delete-account" 
        element={
          <ProtectedRoute>
            <div style={{ 
              padding: '2rem',
              background: 'linear-gradient(135deg, #F44336 0%, #D32F2F 100%)',
              color: 'white',
              borderRadius: '12px',
              margin: '2rem'
            }}>
              <h2>⚠️ Delete Account</h2>
              <p>Permanently delete your BidBeez account and all associated data.</p>
              <p>This action cannot be undone.</p>
              <p>Coming soon...</p>
            </div>
          </ProtectedRoute>
        } 
      />

      {/* Help & Support */}
      <Route 
        path="/help" 
        element={
          <ProtectedRoute>
            <div style={{ padding: '2rem' }}>
              <h2>❓ Settings Help</h2>
              <p>Get help with configuring your BidBeez settings.</p>
              <p>Coming soon...</p>
            </div>
          </ProtectedRoute>
        } 
      />

      {/* Redirect unknown routes to general settings */}
      <Route path="*" element={<Navigate to="/settings/general" replace />} />
    </Routes>
  );
};

export default SettingsRoutes;
