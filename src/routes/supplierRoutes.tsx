/**
 * Supplier Routes Configuration
 * Routing for Supplier Revenue Features and Rep Centre
 */

import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useFeatureFlags } from '../hooks/useFeatureFlags';

// Supplier Components
import SupplierDashboard from '../components/supplier/SupplierDashboard';

// Lazy load supplier pages
const SupplierMainDashboard = React.lazy(() => import('../pages/supplier/SupplierMainDashboard'));
const QuoteManagement = React.lazy(() => import('../pages/supplier/QuoteManagement'));
const SupplierOnboarding = React.lazy(() => import('../pages/supplier/SupplierOnboarding'));
const RepCentre = React.lazy(() => import('../pages/supplier/RepCentre'));
const RepOnboarding = React.lazy(() => import('../pages/supplier/RepOnboarding'));
const SupplierLeaderboard = React.lazy(() => import('../pages/supplier/SupplierLeaderboard'));
const SupplierAnalytics = React.lazy(() => import('../pages/supplier/SupplierAnalytics'));
const ProductSpecifications = React.lazy(() => import('../pages/supplier/ProductSpecifications'));
const ComplianceManagement = React.lazy(() => import('../pages/supplier/ComplianceManagement'));

// Protected Route Component
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiresFeature?: string;
  requiresRole?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiresFeature = 'supplier_revenue',
  requiresRole 
}) => {
  const { user } = useAuth();
  const { isFeatureEnabled } = useFeatureFlags();

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  if (!isFeatureEnabled(requiresFeature)) {
    return (
      <div style={{ 
        padding: '2rem', 
        textAlign: 'center',
        background: 'linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%)',
        color: 'white',
        borderRadius: '12px',
        margin: '2rem'
      }}>
        <h2>🏢 Supplier Features Not Available</h2>
        <p>Supplier revenue features are not enabled for your account.</p>
        <p>Contact support to enable supplier functionality.</p>
      </div>
    );
  }

  if (requiresRole && user.role !== requiresRole) {
    return (
      <div style={{ 
        padding: '2rem', 
        textAlign: 'center',
        background: 'linear-gradient(135deg, #FFA726 0%, #FF7043 100%)',
        color: 'white',
        borderRadius: '12px',
        margin: '2rem'
      }}>
        <h2>🔒 Access Restricted</h2>
        <p>This section requires {requiresRole} access.</p>
        <p>Contact your administrator for access.</p>
      </div>
    );
  }

  return <>{children}</>;
};

// Supplier Routes Component
const SupplierRoutes: React.FC = () => {
  const { user } = useAuth();

  return (
    <Routes>
      {/* Main Supplier Dashboard */}
      <Route 
        path="/" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Supplier Dashboard...</div>}>
              <SupplierMainDashboard />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Supplier Dashboard */}
      <Route 
        path="/dashboard" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Supplier Dashboard...</div>}>
              <SupplierMainDashboard />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Supplier Widget (for embedding) */}
      <Route 
        path="/widget" 
        element={
          <ProtectedRoute>
            <SupplierDashboard
              userId={user?.id || 'demo'}
              compact={false}
              onViewFullDashboard={() => window.location.href = '/supplier/dashboard'}
            />
          </ProtectedRoute>
        } 
      />

      {/* Supplier Onboarding */}
      <Route 
        path="/onboard" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Supplier Onboarding...</div>}>
              <SupplierOnboarding />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Quote Management */}
      <Route 
        path="/quotes" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Quote Management...</div>}>
              <QuoteManagement />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Quote Creation */}
      <Route 
        path="/quotes/create" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Quote Creation...</div>}>
              <QuoteManagement />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Quote Details */}
      <Route 
        path="/quotes/:quoteId" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Quote Details...</div>}>
              <QuoteManagement />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Rep Centre */}
      <Route 
        path="/rep-centre" 
        element={
          <ProtectedRoute requiresFeature="sales_rep_centre">
            <React.Suspense fallback={<div>Loading Rep Centre...</div>}>
              <RepCentre />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Rep Onboarding */}
      <Route 
        path="/rep/onboard" 
        element={
          <ProtectedRoute requiresFeature="sales_rep_centre">
            <React.Suspense fallback={<div>Loading Rep Onboarding...</div>}>
              <RepOnboarding />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Rep Dashboard */}
      <Route 
        path="/rep/dashboard" 
        element={
          <ProtectedRoute requiresFeature="sales_rep_centre">
            <React.Suspense fallback={<div>Loading Rep Dashboard...</div>}>
              <RepCentre />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Supplier Leaderboard */}
      <Route 
        path="/leaderboard" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Supplier Leaderboard...</div>}>
              <SupplierLeaderboard />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Supplier Analytics */}
      <Route 
        path="/analytics" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Supplier Analytics...</div>}>
              <SupplierAnalytics />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Product Specifications */}
      <Route 
        path="/specifications" 
        element={
          <ProtectedRoute requiresFeature="product_specifications">
            <React.Suspense fallback={<div>Loading Product Specifications...</div>}>
              <ProductSpecifications />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Compliance Management */}
      <Route 
        path="/compliance" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Compliance Management...</div>}>
              <ComplianceManagement />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Supplier Settings */}
      <Route 
        path="/settings" 
        element={
          <ProtectedRoute>
            <div style={{ padding: '2rem' }}>
              <h2>⚙️ Supplier Settings</h2>
              <p>Configure your supplier profile and preferences.</p>
              <p>Coming soon...</p>
            </div>
          </ProtectedRoute>
        } 
      />

      {/* Performance Tracking */}
      <Route 
        path="/performance" 
        element={
          <ProtectedRoute>
            <div style={{ padding: '2rem' }}>
              <h2>📊 Performance Tracking</h2>
              <p>Track your supplier performance metrics and KPIs.</p>
              <p>Coming soon...</p>
            </div>
          </ProtectedRoute>
        } 
      />

      {/* Revenue Optimization */}
      <Route 
        path="/revenue" 
        element={
          <ProtectedRoute requiresFeature="revenue_optimization">
            <div style={{ padding: '2rem' }}>
              <h2>💰 Revenue Optimization</h2>
              <p>AI-powered revenue optimization and pricing strategies.</p>
              <p>Coming soon...</p>
            </div>
          </ProtectedRoute>
        } 
      />

      {/* Help & Documentation */}
      <Route 
        path="/help" 
        element={
          <ProtectedRoute>
            <div style={{ padding: '2rem' }}>
              <h2>❓ Supplier Help</h2>
              <p>Learn how to maximize your success as a BidBeez supplier.</p>
              <p>Coming soon...</p>
            </div>
          </ProtectedRoute>
        } 
      />

      {/* Redirect unknown routes to main dashboard */}
      <Route path="*" element={<Navigate to="/supplier" replace />} />
    </Routes>
  );
};

export default SupplierRoutes;
