/**
 * Compliance Routes Configuration
 * Routing for SA Compliance Tool integration with NeuroMarketing optimization
 */

import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useNeuroMarketing } from '../hooks/useNeuroMarketing';

// Compliance Pages
import ProtestDashboard from '../pages/compliance/ProtestDashboard';
import ProtestWizard from '../pages/compliance/ProtestWizard';
import SMEAnalyzer from '../pages/compliance/SMEAnalyzer';
import TemplateGenerator from '../pages/compliance/TemplateGenerator';

// SkillSync & ToolSync Pages
import SkillSyncDashboard from '../pages/skillsync/SkillSyncDashboard';
import ToolSyncDashboard from '../pages/toolsync/ToolSyncDashboard';

// Additional compliance pages (to be created)
const ProtestDetails = React.lazy(() => import('../pages/compliance/ProtestDetails'));
const DeadlineTracker = React.lazy(() => import('../pages/compliance/DeadlineTracker'));
const IrregularityDetector = React.lazy(() => import('../pages/compliance/IrregularityDetector'));
const ComplianceReports = React.lazy(() => import('../pages/compliance/ComplianceReports'));
const LegalFramework = React.lazy(() => import('../pages/compliance/LegalFramework'));
const EvidenceManager = React.lazy(() => import('../pages/compliance/EvidenceManager'));
const ComplianceHelp = React.lazy(() => import('../pages/compliance/ComplianceHelp'));
const ProtestHistory = React.lazy(() => import('../pages/compliance/ProtestHistory'));
const ComplianceSettings = React.lazy(() => import('../pages/compliance/ComplianceSettings'));

// Protected Route Component with NeuroMarketing
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string;
  requiresSME?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredRole,
  requiresSME = false 
}) => {
  const { user, isAuthenticated } = useAuth();
  const { psychologicalState, startTracking } = useNeuroMarketing();

  React.useEffect(() => {
    // Start psychological tracking for compliance features
    if (isAuthenticated) {
      startTracking();
    }
  }, [isAuthenticated, startTracking]);

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRole && user?.role !== requiredRole) {
    return <Navigate to="/unauthorized" replace />;
  }

  if (requiresSME && !user?.isSME) {
    return <Navigate to="/compliance/sme-verification" replace />;
  }

  return <>{children}</>;
};

// Compliance Routes Component
const ComplianceRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Main Compliance Dashboard */}
      <Route 
        path="/" 
        element={
          <ProtectedRoute>
            <ProtestDashboard />
          </ProtectedRoute>
        } 
      />

      {/* Bid Protest Management */}
      <Route 
        path="/protests" 
        element={
          <ProtectedRoute>
            <ProtestDashboard />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/protests/create" 
        element={
          <ProtectedRoute>
            <ProtestWizard />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/protests/wizard" 
        element={
          <ProtectedRoute>
            <ProtestWizard />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/protests/:id" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading...</div>}>
              <ProtestDetails />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/protests/history" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading...</div>}>
              <ProtestHistory />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* SME-Specific Features */}
      <Route 
        path="/sme" 
        element={
          <ProtectedRoute requiresSME>
            <SMEAnalyzer />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/sme/analyzer" 
        element={
          <ProtectedRoute requiresSME>
            <SMEAnalyzer />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/sme/profile" 
        element={
          <ProtectedRoute requiresSME>
            <React.Suspense fallback={<div>Loading...</div>}>
              <SMEAnalyzer />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Document Management */}
      <Route 
        path="/templates" 
        element={
          <ProtectedRoute>
            <TemplateGenerator />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/templates/generate" 
        element={
          <ProtectedRoute>
            <TemplateGenerator />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/documents" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading...</div>}>
              <EvidenceManager />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/evidence" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading...</div>}>
              <EvidenceManager />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Analysis and Detection */}
      <Route 
        path="/analyze" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading...</div>}>
              <IrregularityDetector />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/scan-tender" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading...</div>}>
              <IrregularityDetector />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Deadline Management */}
      <Route 
        path="/deadlines" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading...</div>}>
              <DeadlineTracker />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Reporting */}
      <Route 
        path="/reports" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading...</div>}>
              <ComplianceReports />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Legal Framework */}
      <Route 
        path="/legal" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading...</div>}>
              <LegalFramework />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/legal-framework" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading...</div>}>
              <LegalFramework />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Help and Support */}
      <Route 
        path="/help" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading...</div>}>
              <ComplianceHelp />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* SkillSync Routes */}
      <Route
        path="/skillsync"
        element={
          <ProtectedRoute>
            <SkillSyncDashboard />
          </ProtectedRoute>
        }
      />

      <Route
        path="/skillsync/dashboard"
        element={
          <ProtectedRoute>
            <SkillSyncDashboard />
          </ProtectedRoute>
        }
      />

      <Route
        path="/skillsync/browse"
        element={
          <ProtectedRoute>
            <SkillSyncDashboard />
          </ProtectedRoute>
        }
      />

      <Route
        path="/skillsync/book/:skillId"
        element={
          <ProtectedRoute>
            <SkillSyncDashboard />
          </ProtectedRoute>
        }
      />

      {/* ToolSync Routes */}
      <Route
        path="/toolsync"
        element={
          <ProtectedRoute>
            <ToolSyncDashboard />
          </ProtectedRoute>
        }
      />

      <Route
        path="/toolsync/dashboard"
        element={
          <ProtectedRoute>
            <ToolSyncDashboard />
          </ProtectedRoute>
        }
      />

      <Route
        path="/toolsync/browse"
        element={
          <ProtectedRoute>
            <ToolSyncDashboard />
          </ProtectedRoute>
        }
      />

      <Route
        path="/toolsync/book/:toolId"
        element={
          <ProtectedRoute>
            <ToolSyncDashboard />
          </ProtectedRoute>
        }
      />

      {/* Settings */}
      <Route
        path="/settings"
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading...</div>}>
              <ComplianceSettings />
            </React.Suspense>
          </ProtectedRoute>
        }
      />

      {/* Redirect unknown routes to dashboard */}
      <Route path="*" element={<Navigate to="/compliance" replace />} />
    </Routes>
  );
};

export default ComplianceRoutes;
