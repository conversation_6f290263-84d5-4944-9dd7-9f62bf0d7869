/**
 * Analytics Routes Configuration
 * Routing for Bid Analytics and Performance Tracking
 */

import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useFeatureFlags } from '../hooks/useFeatureFlags';

// Analytics Pages
import BidAnalyticsDashboard from '../components/analytics/BidAnalyticsDashboard';
import BidSummaryWidget from '../components/analytics/BidSummaryWidget';

// Lazy load heavy analytics components
const AdvancedAnalytics = React.lazy(() => import('../pages/analytics/AdvancedAnalytics'));
const CompetitiveAnalytics = React.lazy(() => import('../pages/analytics/CompetitiveAnalytics'));
const PsychologicalAnalytics = React.lazy(() => import('../pages/analytics/PsychologicalAnalytics'));
const FinancialAnalytics = React.lazy(() => import('../pages/analytics/FinancialAnalytics'));
const ExportCenter = React.lazy(() => import('../pages/analytics/ExportCenter'));

// Protected Route Component
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiresFeature?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiresFeature = 'bid_analytics' 
}) => {
  const { user } = useAuth();
  const { isFeatureEnabled } = useFeatureFlags();

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  if (!isFeatureEnabled(requiresFeature)) {
    return (
      <div style={{ 
        padding: '2rem', 
        textAlign: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        borderRadius: '12px',
        margin: '2rem'
      }}>
        <h2>🔒 Feature Not Available</h2>
        <p>Bid Analytics is not enabled for your account.</p>
        <p>Contact support to upgrade your plan.</p>
      </div>
    );
  }

  return <>{children}</>;
};

// Analytics Routes Component
const AnalyticsRoutes: React.FC = () => {
  const { user } = useAuth();

  return (
    <Routes>
      {/* Main Analytics Dashboard */}
      <Route 
        path="/" 
        element={
          <ProtectedRoute>
            <BidAnalyticsDashboard
              userId={user?.id || 'demo'}
              timeRange="month"
              onTimeRangeChange={(range) => console.log('Time range changed:', range)}
            />
          </ProtectedRoute>
        } 
      />

      {/* Dashboard Route */}
      <Route 
        path="/dashboard" 
        element={
          <ProtectedRoute>
            <BidAnalyticsDashboard
              userId={user?.id || 'demo'}
              timeRange="month"
              onTimeRangeChange={(range) => console.log('Time range changed:', range)}
            />
          </ProtectedRoute>
        } 
      />

      {/* Summary Widget (for embedding) */}
      <Route 
        path="/summary" 
        element={
          <ProtectedRoute>
            <BidSummaryWidget
              userId={user?.id || 'demo'}
              compact={false}
              showEconomicImpact={true}
              showPsychologicalInsights={true}
            />
          </ProtectedRoute>
        } 
      />

      {/* Advanced Analytics */}
      <Route 
        path="/advanced" 
        element={
          <ProtectedRoute requiresFeature="advanced_analytics">
            <React.Suspense fallback={<div>Loading Advanced Analytics...</div>}>
              <AdvancedAnalytics />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Competitive Analytics */}
      <Route 
        path="/competitive" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Competitive Analytics...</div>}>
              <CompetitiveAnalytics />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Psychological Analytics */}
      <Route 
        path="/psychological" 
        element={
          <ProtectedRoute requiresFeature="psychological_analytics">
            <React.Suspense fallback={<div>Loading Psychological Analytics...</div>}>
              <PsychologicalAnalytics />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Financial Analytics */}
      <Route 
        path="/financial" 
        element={
          <ProtectedRoute requiresFeature="financial_analytics">
            <React.Suspense fallback={<div>Loading Financial Analytics...</div>}>
              <FinancialAnalytics />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Export Center */}
      <Route 
        path="/export" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Export Center...</div>}>
              <ExportCenter />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Performance by Category */}
      <Route 
        path="/categories" 
        element={
          <ProtectedRoute>
            <BidAnalyticsDashboard
              userId={user?.id || 'demo'}
              timeRange="year"
              onTimeRangeChange={(range) => console.log('Time range changed:', range)}
            />
          </ProtectedRoute>
        } 
      />

      {/* Time-based Analytics */}
      <Route 
        path="/trends" 
        element={
          <ProtectedRoute>
            <BidAnalyticsDashboard
              userId={user?.id || 'demo'}
              timeRange="quarter"
              onTimeRangeChange={(range) => console.log('Time range changed:', range)}
            />
          </ProtectedRoute>
        } 
      />

      {/* Real-time Analytics */}
      <Route 
        path="/realtime" 
        element={
          <ProtectedRoute requiresFeature="realtime_analytics">
            <div style={{ padding: '2rem' }}>
              <h2>🔴 Real-time Analytics</h2>
              <p>Live bidding performance and market intelligence.</p>
              <p>Coming soon...</p>
            </div>
          </ProtectedRoute>
        } 
      />

      {/* Analytics Settings */}
      <Route 
        path="/settings" 
        element={
          <ProtectedRoute>
            <div style={{ padding: '2rem' }}>
              <h2>⚙️ Analytics Settings</h2>
              <p>Configure your analytics preferences and reporting.</p>
              <p>Coming soon...</p>
            </div>
          </ProtectedRoute>
        } 
      />

      {/* Redirect unknown routes to main dashboard */}
      <Route path="*" element={<Navigate to="/analytics" replace />} />
    </Routes>
  );
};

export default AnalyticsRoutes;
