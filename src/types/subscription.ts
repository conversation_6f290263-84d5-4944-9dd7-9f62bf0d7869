/**
 * Subscription and Revenue Management Types
 * Defines pricing tiers, payment processing, and billing for BidBeez platform
 */

// Subscription Plans
export enum SubscriptionTier {
  FREE = 'free',
  BASIC = 'basic',
  PROFESSIONAL = 'professional',
  ENTERPRISE = 'enterprise',
  COMPLIANCE_PRO = 'compliance_pro'
}

export interface SubscriptionPlan {
  id: string;
  tier: SubscriptionTier;
  name: string;
  description: string;
  price: {
    monthly: number;
    yearly: number;
    currency: string;
  };
  features: PlanFeature[];
  limits: PlanLimits;
  popular: boolean;
  businessFocused: boolean;
  complianceIncluded: boolean;
}

export interface PlanFeature {
  id: string;
  name: string;
  description: string;
  included: boolean;
  limit?: number;
  premium?: boolean;
}

export interface PlanLimits {
  monthlyBids: number;
  tenderAlerts: number;
  ecosystemConnections: number;
  complianceProtests: number;
  teamMembers: number;
  apiCalls: number;
  storageGB: number;
  supportLevel: 'community' | 'email' | 'priority' | 'dedicated';
}

// User Subscription
export interface UserSubscription {
  id: string;
  userId: string;
  planId: string;
  tier: SubscriptionTier;
  status: SubscriptionStatus;
  
  // Billing
  billingCycle: 'monthly' | 'yearly';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  nextBillingDate: string;
  
  // Usage tracking
  usage: UsageMetrics;
  
  // Payment
  paymentMethodId?: string;
  lastPaymentDate?: string;
  nextPaymentAmount: number;
  
  // Compliance features
  complianceEnabled: boolean;
  complianceUsage: ComplianceUsage;
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
  cancelledAt?: string;
  trialEndsAt?: string;
}

export enum SubscriptionStatus {
  ACTIVE = 'active',
  TRIALING = 'trialing',
  PAST_DUE = 'past_due',
  CANCELLED = 'cancelled',
  UNPAID = 'unpaid',
  INCOMPLETE = 'incomplete'
}

export interface UsageMetrics {
  currentPeriod: {
    bidsSubmitted: number;
    tenderViews: number;
    ecosystemRequests: number;
    complianceActions: number;
    apiCalls: number;
    storageUsed: number;
  };
  historical: {
    totalBids: number;
    successfulBids: number;
    totalSpent: number;
    memberSince: string;
  };
}

export interface ComplianceUsage {
  protestsGenerated: number;
  templatesUsed: number;
  viabilityAnalyses: number;
  deadlineTracking: number;
  legalDocuments: number;
}

// Payment Processing
export interface PaymentMethod {
  id: string;
  type: 'card' | 'bank_account' | 'eft' | 'paypal';
  last4: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
  billingAddress: BillingAddress;
}

export interface BillingAddress {
  line1: string;
  line2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  description: string;
  paymentMethodId: string;
  subscriptionId: string;
  invoiceId?: string;
}

export enum PaymentStatus {
  REQUIRES_PAYMENT_METHOD = 'requires_payment_method',
  REQUIRES_CONFIRMATION = 'requires_confirmation',
  REQUIRES_ACTION = 'requires_action',
  PROCESSING = 'processing',
  SUCCEEDED = 'succeeded',
  CANCELLED = 'cancelled',
  FAILED = 'failed'
}

// Billing and Invoicing
export interface Invoice {
  id: string;
  subscriptionId: string;
  userId: string;
  
  // Invoice details
  invoiceNumber: string;
  amount: number;
  currency: string;
  status: InvoiceStatus;
  
  // Dates
  issueDate: string;
  dueDate: string;
  paidDate?: string;
  
  // Line items
  lineItems: InvoiceLineItem[];
  
  // Totals
  subtotal: number;
  tax: number;
  total: number;
  
  // Payment
  paymentMethodId?: string;
  paymentIntentId?: string;
  
  // Files
  pdfUrl?: string;
  downloadUrl?: string;
}

export enum InvoiceStatus {
  DRAFT = 'draft',
  OPEN = 'open',
  PAID = 'paid',
  VOID = 'void',
  UNCOLLECTIBLE = 'uncollectible'
}

export interface InvoiceLineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  amount: number;
  period?: {
    start: string;
    end: string;
  };
}

// Usage-based billing
export interface UsageBilling {
  id: string;
  userId: string;
  subscriptionId: string;
  
  // Metered usage
  meteringPeriod: {
    start: string;
    end: string;
  };
  
  // Usage items
  usageItems: UsageItem[];
  
  // Billing
  totalUsage: number;
  totalCost: number;
  includedInPlan: number;
  overageCharges: number;
}

export interface UsageItem {
  type: UsageType;
  quantity: number;
  unitPrice: number;
  totalCost: number;
  description: string;
}

export enum UsageType {
  BID_SUBMISSION = 'bid_submission',
  TENDER_ALERT = 'tender_alert',
  ECOSYSTEM_REQUEST = 'ecosystem_request',
  COMPLIANCE_PROTEST = 'compliance_protest',
  TEMPLATE_GENERATION = 'template_generation',
  API_CALL = 'api_call',
  STORAGE_GB = 'storage_gb',
  TEAM_MEMBER = 'team_member'
}

// Pricing and Revenue
export interface PricingConfig {
  plans: SubscriptionPlan[];
  usagePricing: UsagePricing[];
  promotions: Promotion[];
  taxRates: TaxRate[];
}

export interface UsagePricing {
  type: UsageType;
  unitPrice: number;
  currency: string;
  billingModel: 'per_unit' | 'tiered' | 'volume';
  tiers?: PricingTier[];
}

export interface PricingTier {
  upTo: number;
  unitPrice: number;
}

export interface Promotion {
  id: string;
  code: string;
  name: string;
  description: string;
  discountType: 'percentage' | 'fixed_amount';
  discountValue: number;
  validFrom: string;
  validTo: string;
  maxUses?: number;
  currentUses: number;
  applicablePlans: SubscriptionTier[];
}

export interface TaxRate {
  id: string;
  country: string;
  state?: string;
  rate: number;
  description: string;
}

// Revenue Analytics
export interface RevenueMetrics {
  period: {
    start: string;
    end: string;
  };
  
  // Revenue
  totalRevenue: number;
  recurringRevenue: number;
  usageRevenue: number;
  
  // Subscriptions
  activeSubscriptions: number;
  newSubscriptions: number;
  cancelledSubscriptions: number;
  churnRate: number;
  
  // Customer metrics
  averageRevenuePerUser: number;
  customerLifetimeValue: number;
  
  // Growth
  monthlyRecurringRevenue: number;
  annualRecurringRevenue: number;
  revenueGrowthRate: number;
  
  // By plan
  revenueByPlan: PlanRevenue[];
}

export interface PlanRevenue {
  planId: string;
  planName: string;
  subscribers: number;
  revenue: number;
  percentage: number;
}

// API Request/Response Types
export interface CreateSubscriptionRequest {
  planId: string;
  billingCycle: 'monthly' | 'yearly';
  paymentMethodId: string;
  promotionCode?: string;
  trialDays?: number;
}

export interface UpdateSubscriptionRequest {
  planId?: string;
  billingCycle?: 'monthly' | 'yearly';
  paymentMethodId?: string;
  cancelAtPeriodEnd?: boolean;
}

export interface CreatePaymentMethodRequest {
  type: 'card' | 'bank_account';
  cardToken?: string;
  bankAccountToken?: string;
  billingAddress: BillingAddress;
  setAsDefault?: boolean;
}

export interface ProcessPaymentRequest {
  paymentMethodId: string;
  amount: number;
  currency: string;
  description: string;
  subscriptionId?: string;
}

// Feature Gates
export interface FeatureGate {
  feature: string;
  enabled: boolean;
  reason?: string;
  upgradeRequired?: boolean;
  requiredPlan?: SubscriptionTier;
}

export interface FeatureUsage {
  feature: string;
  used: number;
  limit: number;
  percentage: number;
  unlimited: boolean;
}

// Subscription Events
export interface SubscriptionEvent {
  id: string;
  type: SubscriptionEventType;
  subscriptionId: string;
  userId: string;
  data: any;
  timestamp: string;
}

export enum SubscriptionEventType {
  SUBSCRIPTION_CREATED = 'subscription_created',
  SUBSCRIPTION_UPDATED = 'subscription_updated',
  SUBSCRIPTION_CANCELLED = 'subscription_cancelled',
  PAYMENT_SUCCEEDED = 'payment_succeeded',
  PAYMENT_FAILED = 'payment_failed',
  INVOICE_CREATED = 'invoice_created',
  INVOICE_PAID = 'invoice_paid',
  TRIAL_ENDING = 'trial_ending',
  USAGE_THRESHOLD_REACHED = 'usage_threshold_reached'
}
